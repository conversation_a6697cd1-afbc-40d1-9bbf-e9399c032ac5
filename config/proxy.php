<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Trusted Proxies Configuration
    |--------------------------------------------------------------------------
    |
    | Configure which proxies should be trusted by the application.
    | This is crucial for proper IP detection when running behind
    | reverse proxies, load balancers, or CDNs.
    |
    */

    'trusted_proxies' => [
        /*
        |--------------------------------------------------------------------------
        | Proxy IP Addresses
        |--------------------------------------------------------------------------
        |
        | List of IP addresses or CIDR ranges that should be trusted as proxies.
        | Use '*' to trust all proxies (not recommended for production).
        |
        */
        'ips' => env('TRUSTED_PROXIES', null) ? 
            array_map('trim', explode(',', env('TRUSTED_PROXIES'))) : 
            [
                // Common private network ranges
                '10.0.0.0/8',     // Private network
                '**********/12',  // Private network
                '***********/16', // Private network
                '*********/8',    // Loopback
                
                // Cloudflare IP ranges (update as needed)
                '************/20',
                '************/22',
                '************/22',
                '**********/22',
                '************/18',
                '*************/18',
                '************/20',
                '************/20',
                '*************/22',
                '************/17',
                '***********/15',
                '**********/13',
                '**********/14',
                '**********/13',
                '**********/22',
                
                // Add your specific proxy IPs here
                // Example: '***********',
            ],

        /*
        |--------------------------------------------------------------------------
        | Trusted Headers
        |--------------------------------------------------------------------------
        |
        | Headers that should be trusted from the configured proxies.
        | These headers contain the real client information.
        |
        */
        'headers' => env('TRUSTED_PROXY_HEADERS', 'HEADER_X_FORWARDED_ALL'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Trusted Hosts Configuration
    |--------------------------------------------------------------------------
    |
    | Configure which hostnames the application should accept requests from.
    | This prevents Host header injection attacks.
    |
    */

    'trusted_hosts' => [
        /*
        |--------------------------------------------------------------------------
        | Allowed Hostnames
        |--------------------------------------------------------------------------
        |
        | List of hostnames that the application should respond to.
        | Requests with other Host headers will be rejected.
        |
        */
        'hosts' => env('TRUSTED_HOSTS', null) ? 
            array_map('trim', explode(',', env('TRUSTED_HOSTS'))) : 
            [
                // Local development
                'localhost',
                '127.0.0.1',
                'svs.local',
                
                // Remote test environment
                'svs.devet.ru',
                
                // Add production domains here
                // 'your-production-domain.com',
                // 'www.your-production-domain.com',
            ],

        /*
        |--------------------------------------------------------------------------
        | Allow Subdomains
        |--------------------------------------------------------------------------
        |
        | Whether to automatically trust subdomains of the configured hosts.
        |
        */
        'allow_subdomains' => env('TRUSTED_HOSTS_ALLOW_SUBDOMAINS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Environment-Specific Settings
    |--------------------------------------------------------------------------
    |
    | Different configurations for different environments.
    |
    */

    'environments' => [
        'local' => [
            'trust_all_proxies' => env('LOCAL_TRUST_ALL_PROXIES', false),
            'additional_hosts' => [
                '*.test',
                '*.local',
                'localhost:*',
                '127.0.0.1:*',
            ],
        ],

        'testing' => [
            'trust_all_proxies' => true,
            'additional_hosts' => [
                'testserver',
                '*.test',
            ],
        ],

        'staging' => [
            'trust_all_proxies' => false,
            'additional_hosts' => [
                'staging.your-domain.com',
                '*.staging.your-domain.com',
            ],
        ],

        'production' => [
            'trust_all_proxies' => false,
            'additional_hosts' => [
                // Add production-specific hosts
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook-Specific Configuration
    |--------------------------------------------------------------------------
    |
    | Special configuration for webhook endpoints that need to accept
    | requests from external services like payment gateways.
    |
    */

    'webhook' => [
        /*
        |--------------------------------------------------------------------------
        | Webhook Trusted IPs
        |--------------------------------------------------------------------------
        |
        | Additional IP addresses that should be trusted for webhook endpoints.
        | These are typically the IP ranges of payment gateway providers.
        |
        */
        'trusted_ips' => [
            // T-Bank webhook IPs (from config/tbank.php)
            '************/23',
            '***********/27',
            '***********/27',
            '***********/25',
            '************',
            '************',
            
            // Add other payment gateway IPs here
        ],

        /*
        |--------------------------------------------------------------------------
        | Webhook Host Validation
        |--------------------------------------------------------------------------
        |
        | Whether to enforce host validation for webhook endpoints.
        | Set to false if webhooks come from various domains.
        |
        */
        'enforce_host_validation' => env('WEBHOOK_ENFORCE_HOST_VALIDATION', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Additional security settings for proxy and host handling.
    |
    */

    'security' => [
        /*
        |--------------------------------------------------------------------------
        | Log Suspicious Requests
        |--------------------------------------------------------------------------
        |
        | Whether to log requests from untrusted proxies or with invalid hosts.
        |
        */
        'log_suspicious_requests' => env('LOG_SUSPICIOUS_REQUESTS', true),

        /*
        |--------------------------------------------------------------------------
        | Strict Mode
        |--------------------------------------------------------------------------
        |
        | In strict mode, the application will be more restrictive about
        | which proxies and hosts are trusted.
        |
        */
        'strict_mode' => env('PROXY_STRICT_MODE', false),

        /*
        |--------------------------------------------------------------------------
        | Rate Limiting by Real IP
        |--------------------------------------------------------------------------
        |
        | Whether to use the real client IP (after proxy resolution) for
        | rate limiting instead of the proxy IP.
        |
        */
        'rate_limit_by_real_ip' => env('RATE_LIMIT_BY_REAL_IP', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Debug Settings
    |--------------------------------------------------------------------------
    |
    | Settings for debugging proxy and host configuration.
    |
    */

    'debug' => [
        /*
        |--------------------------------------------------------------------------
        | Log Proxy Headers
        |--------------------------------------------------------------------------
        |
        | Whether to log proxy headers for debugging purposes.
        | Only enable in development/staging environments.
        |
        */
        'log_headers' => env('DEBUG_PROXY_HEADERS', false),

        /*
        |--------------------------------------------------------------------------
        | Show IP Information
        |--------------------------------------------------------------------------
        |
        | Whether to include IP information in debug responses.
        | Only enable in development environments.
        |
        */
        'show_ip_info' => env('DEBUG_SHOW_IP_INFO', false),
    ],
];
