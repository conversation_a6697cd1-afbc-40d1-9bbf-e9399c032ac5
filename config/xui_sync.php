<?php

return [

    /*
    |--------------------------------------------------------------------------
    | XUI Server Synchronization Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the XUI server
    | synchronization service.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Default Batch Size
    |--------------------------------------------------------------------------
    |
    | The default number of servers to process concurrently during
    | synchronization. This can be overridden via command line options.
    |
    */

    'default_batch_size' => env('XUI_SYNC_DEFAULT_BATCH_SIZE', 10),

    /*
    |--------------------------------------------------------------------------
    | Online/Offline Detection Thresholds
    |--------------------------------------------------------------------------
    |
    | These settings control when users are considered online or offline
    | based on their last activity timestamps.
    |
    */

    'online_threshold_seconds' => env('XUI_SYNC_ONLINE_THRESHOLD', 20),
    'offline_threshold_seconds' => env('XUI_SYNC_OFFLINE_THRESHOLD', 15),

    /*
    |--------------------------------------------------------------------------
    | Sync Intervals and Timeouts
    |--------------------------------------------------------------------------
    |
    | Configuration for sync timing and timeout handling.
    |
    */

    'sync_timeout_seconds' => env('XUI_SYNC_TIMEOUT', 300), // 5 minutes
    'server_request_timeout' => env('XUI_SERVER_REQUEST_TIMEOUT', 30), // 30 seconds
    'max_retry_attempts' => env('XUI_SYNC_MAX_RETRIES', 3),
    'retry_delay_seconds' => env('XUI_SYNC_RETRY_DELAY', 60), // 1 minute

    /*
    |--------------------------------------------------------------------------
    | Server Status Thresholds
    |--------------------------------------------------------------------------
    |
    | Thresholds for determining server health and status.
    |
    */

    'server_online_threshold_minutes' => env('XUI_SERVER_ONLINE_THRESHOLD', 10),
    'server_load_warning_threshold' => env('XUI_SERVER_LOAD_WARNING', 80.0),
    'server_load_critical_threshold' => env('XUI_SERVER_LOAD_CRITICAL', 95.0),

    /*
    |--------------------------------------------------------------------------
    | Traffic Statistics Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for traffic statistics collection and processing.
    |
    */

    'traffic_stats' => [
        'enabled' => env('XUI_TRAFFIC_STATS_ENABLED', true),
        'aggregate_by_user' => env('XUI_TRAFFIC_AGGREGATE_BY_USER', true),
        'update_subscription_stats' => env('XUI_TRAFFIC_UPDATE_SUBSCRIPTION', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | User Online Status Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for user online status tracking and logging.
    |
    */

    'user_status' => [
        'enabled' => env('XUI_USER_STATUS_ENABLED', true),
        'log_status_changes' => env('XUI_USER_STATUS_LOG_CHANGES', true),
        'cleanup_old_logs_days' => env('XUI_USER_STATUS_CLEANUP_DAYS', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Background Job Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for background job processing and scheduling.
    |
    */

    'jobs' => [
        'queue' => env('XUI_SYNC_QUEUE', 'default'),
        'connection' => env('XUI_SYNC_QUEUE_CONNECTION', 'database'),
        'offline_detection_interval' => env('XUI_OFFLINE_DETECTION_INTERVAL', '*/5'), // Every 5 minutes
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for sync operation logging.
    |
    */

    'logging' => [
        'enabled' => env('XUI_SYNC_LOGGING_ENABLED', true),
        'log_successful_syncs' => env('XUI_SYNC_LOG_SUCCESS', true),
        'log_failed_syncs' => env('XUI_SYNC_LOG_FAILURES', true),
        'log_user_status_changes' => env('XUI_SYNC_LOG_USER_STATUS', true),
        'log_traffic_updates' => env('XUI_SYNC_LOG_TRAFFIC', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    |
    | Settings to optimize sync performance and resource usage.
    |
    */

    'performance' => [
        'max_concurrent_servers' => env('XUI_SYNC_MAX_CONCURRENT', 50),
        'memory_limit_mb' => env('XUI_SYNC_MEMORY_LIMIT', 512),
        'enable_database_transactions' => env('XUI_SYNC_USE_TRANSACTIONS', true),
        'chunk_size_for_user_updates' => env('XUI_SYNC_USER_CHUNK_SIZE', 100),
    ],

];
