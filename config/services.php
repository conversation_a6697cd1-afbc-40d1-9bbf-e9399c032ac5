<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'tbank' => [
        'terminal_key' => env('TEST_TERMINAL_ID'),
        'secret_key' => env('TEST_TERMINAL_PASSWORD'),
        'api_url' => env('TBANK_API_URL', 'https://securepay.tinkoff.ru/v2/'),
        'success_url' => env('TEST_TERMINAL_SUCCESS_URL'),
        'failed_url' => env('TEST_TERMINAL_FAILED_URL'),
        'notification_url' => env('TEST_TERMINAL_NOTIFICATION_URL'),
        'skip_signature_verification' => env('TBANK_SKIP_SIGNATURE_VERIFICATION', false),
    ],

    'leadteh' => [
        'api_key' => env('LEADTEH_API_KEY'),
        'api_url' => env('LEADTEH_API_URL', 'https://app.leadteh.ru/api/v1/'),
        'webhook_secret' => env('LEADTEH_WEBHOOK_SECRET'),
    ],

];
