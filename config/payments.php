<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Payment Gateway
    |--------------------------------------------------------------------------
    |
    | This option controls the default payment gateway that will be used
    | when no specific gateway is requested.
    |
    */
    'default' => env('PAYMENT_DEFAULT_GATEWAY', 'manual'),

    /*
    |--------------------------------------------------------------------------
    | Payment Gateways
    |--------------------------------------------------------------------------
    |
    | Here you may configure the payment gateways for your application.
    | Each gateway has its own configuration and driver.
    |
    */
    'gateways' => [
        'manual' => [
            'driver' => 'manual',
            'name' => 'Manual Payment',
            'description' => 'Admin-processed payments',
            'enabled' => env('PAYMENT_MANUAL_ENABLED', true),
            'supports_recurring' => false,
            'supports_webhooks' => false,
            'supports_refunds' => true,
            'config' => [
                'auto_confirm' => env('PAYMENT_MANUAL_AUTO_CONFIRM', false),
                'admin_notification' => env('PAYMENT_MANUAL_ADMIN_NOTIFICATION', true),
            ],
        ],

        'cash' => [
            'driver' => 'cash',
            'name' => 'Cash Payment',
            'description' => 'Offline cash payments',
            'enabled' => env('PAYMENT_CASH_ENABLED', true),
            'supports_recurring' => false,
            'supports_webhooks' => false,
            'supports_refunds' => true,
            'config' => [
                'auto_confirm' => env('PAYMENT_CASH_AUTO_CONFIRM', false),
                'require_receipt' => env('PAYMENT_CASH_REQUIRE_RECEIPT', true),
            ],
        ],

        'free' => [
            'driver' => 'free',
            'name' => 'Free Payment',
            'description' => 'Promotional/free subscriptions',
            'enabled' => env('PAYMENT_FREE_ENABLED', true),
            'supports_recurring' => false,
            'supports_webhooks' => false,
            'supports_refunds' => false,
            'config' => [
                'auto_confirm' => true,
                'max_amount' => 0, // Free payments should always be 0
            ],
        ],

        'tbank' => [
            'driver' => 'tbank',
            'name' => 'T-Bank',
            'description' => 'T-Bank payment gateway',
            'enabled' => env('PAYMENT_TBANK_ENABLED', false),
            'supports_recurring' => true,
            'supports_webhooks' => true,
            'supports_refunds' => true,
            'config' => [
                'terminal_key' => env('TBANK_TERMINAL_KEY'),
                'password' => env('TBANK_PASSWORD'),
                'api_url' => env('TBANK_API_URL', 'https://securepay.tinkoff.ru/v2'),
                'success_url' => env('TBANK_SUCCESS_URL'),
                'fail_url' => env('TBANK_FAIL_URL'),
                'notification_url' => env('TBANK_NOTIFICATION_URL'),
                'language' => env('TBANK_LANGUAGE', 'ru'),
                'currency' => env('TBANK_CURRENCY', 'RUB'),
                'pay_type' => env('TBANK_PAY_TYPE', 'O'), // O = one-stage, T = two-stage
                'timeout' => env('TBANK_TIMEOUT', 30),
                'verify_ssl' => env('TBANK_VERIFY_SSL', true),
                'verify_signature' => env('TBANK_WEBHOOK_VERIFY_SIGNATURE', env('APP_ENV') !== 'local'),
            ],
            'webhook' => [
                'verify_signature' => env('TBANK_WEBHOOK_VERIFY_SIGNATURE', env('APP_ENV') !== 'local'),
                'allowed_ips' => array_merge([
                    // T-Bank webhook IP addresses
                    '************/23',
                    '***********/27',
                    '***********/27',
                    '***********/25',
                    '************',
                    '************',
                ], env('APP_ENV') === 'local' ? [
                    // Local development IPs
                    '127.0.0.1/32',
                    '::1/128',
                    '10.0.0.0/8',
                    '**********/12',
                    '***********/16',
                ] : []),
                'timeout' => env('TBANK_WEBHOOK_TIMEOUT', 10),
                'max_retries' => env('TBANK_WEBHOOK_MAX_RETRIES', 3),
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Settings
    |--------------------------------------------------------------------------
    |
    | General payment system settings.
    |
    */
    'settings' => [
        'default_currency' => env('PAYMENT_DEFAULT_CURRENCY', 'RUB'),
        'order_expiry_minutes' => env('PAYMENT_ORDER_EXPIRY_MINUTES', 60),
        'webhook_timeout_seconds' => env('PAYMENT_WEBHOOK_TIMEOUT', 10),
        'max_retry_attempts' => env('PAYMENT_MAX_RETRY_ATTEMPTS', 3),
        'retry_delay_seconds' => env('PAYMENT_RETRY_DELAY', 5),
        'log_level' => env('PAYMENT_LOG_LEVEL', 'info'),
        'enable_debug' => env('PAYMENT_DEBUG', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook Security
    |--------------------------------------------------------------------------
    |
    | Security settings for webhook handling.
    |
    */
    'webhook' => [
        'verify_signature' => env('PAYMENT_WEBHOOK_VERIFY_SIGNATURE', true),
        'allowed_ips' => env('PAYMENT_WEBHOOK_ALLOWED_IPS', ''),
        'secret_key' => env('PAYMENT_WEBHOOK_SECRET_KEY'),
        'max_age_seconds' => env('PAYMENT_WEBHOOK_MAX_AGE', 300),
    ],

    /*
    |--------------------------------------------------------------------------
    | Recurring Payments
    |--------------------------------------------------------------------------
    |
    | Settings for recurring payment processing.
    |
    */
    'recurring' => [
        'enabled' => env('PAYMENT_RECURRING_ENABLED', true),
        'retry_failed_attempts' => env('PAYMENT_RECURRING_RETRY_ATTEMPTS', 3),
        'retry_delay_hours' => env('PAYMENT_RECURRING_RETRY_DELAY', 24),
        'notification_email' => env('PAYMENT_RECURRING_NOTIFICATION_EMAIL'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Testing
    |--------------------------------------------------------------------------
    |
    | Settings for testing payment functionality.
    |
    */
    'testing' => [
        'enabled' => env('PAYMENT_TESTING_ENABLED', false),
        'fake_success_rate' => env('PAYMENT_TESTING_SUCCESS_RATE', 0.9),
        'simulate_delays' => env('PAYMENT_TESTING_SIMULATE_DELAYS', false),
        'test_cards' => [
            '****************', // Visa test card
            '****************', // MasterCard test card
            '2200000000000004', // Mir test card
        ],
    ],
];
