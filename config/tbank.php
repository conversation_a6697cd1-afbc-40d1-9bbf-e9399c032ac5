<?php

return [
    /*
    |--------------------------------------------------------------------------
    | T-Bank API Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for T-Bank payment gateway integration.
    | Based on T-Bank Merchant API documentation.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | API Credentials
    |--------------------------------------------------------------------------
    */
    'terminal_key' => env('TBANK_TERMINAL_KEY'),
    'password' => env('TBANK_PASSWORD'),

    /*
    |--------------------------------------------------------------------------
    | API Endpoints
    |--------------------------------------------------------------------------
    */
    'api_url' => env('TBANK_API_URL', 'https://securepay.tinkoff.ru/v2'),
    'test_api_url' => env('TBANK_TEST_API_URL', 'https://rest-api-test.tinkoff.ru/v2'),

    /*
    |--------------------------------------------------------------------------
    | Environment Settings
    |--------------------------------------------------------------------------
    */
    'environment' => env('TBANK_ENVIRONMENT', 'production'), // production, test
    'verify_ssl' => env('TBANK_VERIFY_SSL', true),
    'timeout' => env('TBANK_TIMEOUT', 30),

    /*
    |--------------------------------------------------------------------------
    | Payment Form Settings
    |--------------------------------------------------------------------------
    */
    'language' => env('TBANK_LANGUAGE', 'ru'), // ru, en
    'currency' => env('TBANK_CURRENCY', 'RUB'),
    'pay_type' => env('TBANK_PAY_TYPE', 'O'), // O = one-stage, T = two-stage

    /*
    |--------------------------------------------------------------------------
    | URLs
    |--------------------------------------------------------------------------
    */
    'success_url' => env('TBANK_SUCCESS_URL'),
    'fail_url' => env('TBANK_FAIL_URL'),
    'notification_url' => env('TBANK_NOTIFICATION_URL'),

    /*
    |--------------------------------------------------------------------------
    | Operation Initiator Types
    |--------------------------------------------------------------------------
    |
    | Based on T-Bank documentation for payment system requirements.
    |
    */
    'operation_initiator_types' => [
        'customer_not_captured' => '0', // CIT CNC - Customer initiated, credentials not captured
        'customer_captured' => '1',     // CIT CC - Customer initiated, credentials captured
        'customer_on_file' => '2',      // CIT COF - Customer initiated, credentials on file
        'merchant_recurring' => 'R',    // MIT COF R - Merchant initiated, recurring
        'merchant_installment' => 'I',  // MIT COF I - Merchant initiated, installment
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Methods
    |--------------------------------------------------------------------------
    */
    'payment_methods' => [
        'card' => true,
        'tpay' => env('TBANK_TPAY_ENABLED', true),
        'sberpay' => env('TBANK_SBERPAY_ENABLED', true),
        'mirpay' => env('TBANK_MIRPAY_ENABLED', true),
        'sbp' => env('TBANK_SBP_ENABLED', true), // Система быстрых платежей
        'installments' => env('TBANK_INSTALLMENTS_ENABLED', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Recurring Payments
    |--------------------------------------------------------------------------
    */
    'recurring' => [
        'enabled' => env('TBANK_RECURRING_ENABLED', true),
        'save_card_default' => env('TBANK_SAVE_CARD_DEFAULT', false),
        'customer_key_required' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | 3DS Settings
    |--------------------------------------------------------------------------
    */
    '3ds' => [
        'version' => env('TBANK_3DS_VERSION', '2.1'), // 1.0, 2.1
        'force_3ds' => env('TBANK_FORCE_3DS', false),
        'challenge_window_size' => env('TBANK_3DS_CHALLENGE_WINDOW_SIZE', '05'), // 01-05
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook Settings
    |--------------------------------------------------------------------------
    */
    'webhook' => [
        'verify_signature' => env('TBANK_WEBHOOK_VERIFY_SIGNATURE', true),
        'allowed_ips' => [
            // T-Bank webhook IP addresses (should be updated based on actual IPs)
            '************/23',
            '***********/27',
            '***********/27',
            '***********/25',
            '************',
            '************',
            '127.0.0.1/32',
        ],
        'timeout' => env('TBANK_WEBHOOK_TIMEOUT', 10),
        'max_retries' => env('TBANK_WEBHOOK_MAX_RETRIES', 3),
    ],

    /*
    |--------------------------------------------------------------------------
    | Receipt Settings (for fiscal requirements)
    |--------------------------------------------------------------------------
    */
    'receipt' => [
        'enabled' => env('TBANK_RECEIPT_ENABLED', false),
        'taxation' => env('TBANK_RECEIPT_TAXATION', 'osn'), // osn, usn_income, etc.
        'email_required' => env('TBANK_RECEIPT_EMAIL_REQUIRED', false),
        'phone_required' => env('TBANK_RECEIPT_PHONE_REQUIRED', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Error Handling
    |--------------------------------------------------------------------------
    */
    'error_handling' => [
        'log_requests' => env('TBANK_LOG_REQUESTS', true),
        'log_responses' => env('TBANK_LOG_RESPONSES', true),
        'log_webhooks' => env('TBANK_LOG_WEBHOOKS', true),
        'mask_sensitive_data' => env('TBANK_MASK_SENSITIVE_DATA', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    */
    'rate_limiting' => [
        'requests_per_minute' => env('TBANK_RATE_LIMIT_RPM', 60),
        'burst_limit' => env('TBANK_RATE_LIMIT_BURST', 10),
    ],

    /*
    |--------------------------------------------------------------------------
    | Testing Configuration
    |--------------------------------------------------------------------------
    */
    'testing' => [
        'terminal_key' => env('TBANK_TEST_TERMINAL_KEY', 'TinkoffBankTest'),
        'password' => env('TBANK_TEST_PASSWORD', 'TinkoffBankTest'),
        'test_cards' => [
            'success' => [
                '****************', // Visa
                '****************', // MasterCard
                '****************', // Mir
            ],
            'failure' => [
                '****************', // Declined
                '****************', // Processing error
            ],
            '3ds_required' => [
                '****************', // 3DS authentication required
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | API Methods Configuration
    |--------------------------------------------------------------------------
    */
    'api_methods' => [
        'init' => '/Init',
        'finish_authorize' => '/FinishAuthorize',
        'confirm' => '/Confirm',
        'cancel' => '/Cancel',
        'get_state' => '/GetState',
        'charge' => '/Charge',
        'add_customer' => '/AddCustomer',
        'get_customer' => '/GetCustomer',
        'remove_customer' => '/RemoveCustomer',
        'add_card' => '/AddCard',
        'attach_card' => '/AttachCard',
        'get_card_list' => '/GetCardList',
        'remove_card' => '/RemoveCard',
        'check_3ds_version' => '/Check3dsVersion',
        'submit_3ds_authorization' => '/Submit3DSAuthorization',
        'submit_3ds_authorization_v2' => '/Submit3DSAuthorizationV2',
    ],

    /*
    |--------------------------------------------------------------------------
    | Status Codes
    |--------------------------------------------------------------------------
    */
    'status_codes' => [
        'new' => 'NEW',
        'form_showed' => 'FORM_SHOWED',
        'authorizing' => 'AUTHORIZING',
        'authorized' => 'AUTHORIZED',
        'confirmed' => 'CONFIRMED',
        'reversed' => 'REVERSED',
        'partial_reversed' => 'PARTIAL_REVERSED',
        'refunded' => 'REFUNDED',
        'partial_refunded' => 'PARTIAL_REFUNDED',
        'rejected' => 'REJECTED',
        'deadline_expired' => 'DEADLINE_EXPIRED',
        'canceled' => 'CANCELED',
        'auth_fail' => 'AUTH_FAIL',
        'unknown' => 'UNKNOWN',
    ],
];
