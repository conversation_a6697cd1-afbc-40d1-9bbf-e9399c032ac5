<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserRating extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'rating',
        'user_ip',
        'user_agent',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'rating' => 'integer',
        ];
    }

    /**
     * Get the user this rating belongs to.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get ratings for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get ratings with specific value.
     */
    public function scopeWithRating($query, int $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * Scope to get positive ratings (4-5).
     */
    public function scopePositive($query)
    {
        return $query->whereIn('rating', [4, 5]);
    }

    /**
     * Scope to get neutral ratings (3).
     */
    public function scopeNeutral($query)
    {
        return $query->where('rating', 3);
    }

    /**
     * Scope to get negative ratings (1-2).
     */
    public function scopeNegative($query)
    {
        return $query->whereIn('rating', [1, 2]);
    }

    /**
     * Scope to get ratings from a specific IP.
     */
    public function scopeFromIp($query, string $ip)
    {
        return $query->where('user_ip', $ip);
    }

    /**
     * Scope to get ratings within a date range.
     */
    public function scopeWithinDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope to get recent ratings.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Check if this is a positive rating.
     */
    public function isPositive(): bool
    {
        return $this->rating >= 4;
    }

    /**
     * Check if this is a neutral rating.
     */
    public function isNeutral(): bool
    {
        return $this->rating === 3;
    }

    /**
     * Check if this is a negative rating.
     */
    public function isNegative(): bool
    {
        return $this->rating <= 2;
    }

    /**
     * Get the rating emoji.
     */
    public function getEmojiAttribute(): string
    {
        return match ($this->rating) {
            1 => '😡', // angry
            2 => '😕', // confused
            3 => '😐', // neutral
            4 => '😊', // happy
            5 => '🤩', // excited
            default => '❓',
        };
    }

    /**
     * Get the rating label.
     */
    public function getLabelAttribute(): string
    {
        return match ($this->rating) {
            1 => 'Angry',
            2 => 'Confused',
            3 => 'Neutral',
            4 => 'Happy',
            5 => 'Excited',
            default => 'Unknown',
        };
    }

    /**
     * Get the rating description.
     */
    public function getDescriptionAttribute(): string
    {
        return match ($this->rating) {
            1 => 'Very dissatisfied',
            2 => 'Dissatisfied',
            3 => 'Neutral',
            4 => 'Satisfied',
            5 => 'Very satisfied',
            default => 'Unknown rating'
        };
    }

    /**
     * Get the time elapsed since this rating was created.
     */
    public function getTimeElapsedAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Get the average rating for all users.
     */
    public static function getAverageRating(): float
    {
        return static::avg('rating') ?: 0;
    }

    /**
     * Get the average rating for a specific user.
     */
    public static function getAverageRatingForUser($userId): float
    {
        return static::forUser($userId)->avg('rating') ?: 0;
    }

    /**
     * Get rating distribution.
     */
    public static function getRatingDistribution(): array
    {
        $distribution = static::selectRaw('rating, COUNT(*) as count')
            ->groupBy('rating')
            ->pluck('count', 'rating')
            ->toArray();

        // Ensure all ratings 1-5 are present
        for ($i = 1; $i <= 5; $i++) {
            if (!isset($distribution[$i])) {
                $distribution[$i] = 0;
            }
        }

        ksort($distribution);
        return $distribution;
    }

    /**
     * Get rating statistics.
     */
    public static function getStatistics(): array
    {
        $total = static::count();
        $average = static::getAverageRating();
        $distribution = static::getRatingDistribution();

        $positive = static::positive()->count();
        $neutral = static::neutral()->count();
        $negative = static::negative()->count();

        return [
            'total_ratings' => $total,
            'average_rating' => round($average, 2),
            'distribution' => $distribution,
            'positive_count' => $positive,
            'neutral_count' => $neutral,
            'negative_count' => $negative,
            'positive_percentage' => $total > 0 ? round(($positive / $total) * 100, 1) : 0,
            'neutral_percentage' => $total > 0 ? round(($neutral / $total) * 100, 1) : 0,
            'negative_percentage' => $total > 0 ? round(($negative / $total) * 100, 1) : 0,
        ];
    }

    /**
     * Get recent rating trends.
     */
    public static function getRecentTrends(int $days = 30): array
    {
        $startDate = now()->subDays($days);
        $endDate = now();

        $recentRatings = static::withinDateRange($startDate, $endDate)->get();
        $previousRatings = static::withinDateRange($startDate->copy()->subDays($days), $startDate)->get();

        $recentAverage = $recentRatings->avg('rating') ?: 0;
        $previousAverage = $previousRatings->avg('rating') ?: 0;

        $trend = $recentAverage - $previousAverage;

        return [
            'recent_average' => round($recentAverage, 2),
            'previous_average' => round($previousAverage, 2),
            'trend' => round($trend, 2),
            'trend_direction' => $trend > 0 ? 'up' : ($trend < 0 ? 'down' : 'stable'),
            'recent_count' => $recentRatings->count(),
            'previous_count' => $previousRatings->count(),
        ];
    }

    /**
     * Check if a user has already rated recently.
     */
    public static function hasUserRatedRecently($userId, int $hours = 24): bool
    {
        return static::forUser($userId)
            ->where('created_at', '>=', now()->subHours($hours))
            ->exists();
    }

    /**
     * Check if an IP has rated too frequently.
     */
    public static function hasIpRatedTooFrequently(string $ip, int $maxRatings = 5, int $hours = 24): bool
    {
        return static::fromIp($ip)
            ->where('created_at', '>=', now()->subHours($hours))
            ->count() >= $maxRatings;
    }
}
