<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

class Announce extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'title',
        'message',
        'url',
        'is_active',
        'published_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'published_at' => 'datetime',
        ];
    }

    /**
     * Scope to get only active announcements.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get only published announcements.
     */
    public function scopePublished($query)
    {
        return $query->whereNotNull('published_at')
                    ->where('published_at', '<=', now());
    }

    /**
     * Scope to get announcements that are both active and published.
     */
    public function scopeVisible($query)
    {
        return $query->active()->published();
    }

    /**
     * Scope to get announcements scheduled for future publication.
     */
    public function scopeScheduled($query)
    {
        return $query->whereNotNull('published_at')
                    ->where('published_at', '>', now());
    }

    /**
     * Scope to get draft announcements (not published).
     */
    public function scopeDraft($query)
    {
        return $query->whereNull('published_at');
    }

    /**
     * Scope to get recent announcements.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('published_at', '>=', now()->subDays($days));
    }

    /**
     * Check if the announcement is published.
     */
    public function isPublished(): bool
    {
        return $this->published_at && $this->published_at->isPast();
    }

    /**
     * Check if the announcement is scheduled for future publication.
     */
    public function isScheduled(): bool
    {
        return $this->published_at && $this->published_at->isFuture();
    }

    /**
     * Check if the announcement is a draft.
     */
    public function isDraft(): bool
    {
        return !$this->published_at;
    }

    /**
     * Check if the announcement is visible to users.
     */
    public function isVisible(): bool
    {
        return $this->is_active && $this->isPublished();
    }

    /**
     * Check if the announcement has a URL.
     */
    public function hasUrl(): bool
    {
        return !empty($this->url);
    }

    /**
     * Get the status of the announcement.
     */
    public function getStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'inactive';
        }

        if ($this->isDraft()) {
            return 'draft';
        }

        if ($this->isScheduled()) {
            return 'scheduled';
        }

        if ($this->isPublished()) {
            return 'published';
        }

        return 'unknown';
    }

    /**
     * Get the time until publication (for scheduled announcements).
     */
    public function getTimeUntilPublicationAttribute(): ?string
    {
        if (!$this->isScheduled()) {
            return null;
        }

        return $this->published_at->diffForHumans();
    }

    /**
     * Get the time since publication (for published announcements).
     */
    public function getTimeSincePublicationAttribute(): ?string
    {
        if (!$this->isPublished()) {
            return null;
        }

        return $this->published_at->diffForHumans();
    }

    /**
     * Publish the announcement now.
     */
    public function publish(): void
    {
        $this->update([
            'is_active' => true,
            'published_at' => now(),
        ]);
    }

    /**
     * Schedule the announcement for publication.
     */
    public function scheduleFor(Carbon $publishAt): void
    {
        $this->update([
            'is_active' => true,
            'published_at' => $publishAt,
        ]);
    }

    /**
     * Mark the announcement as draft.
     */
    public function markAsDraft(): void
    {
        $this->update([
            'published_at' => null,
        ]);
    }

    /**
     * Activate the announcement.
     */
    public function activate(): void
    {
        $this->update(['is_active' => true]);
    }

    /**
     * Deactivate the announcement.
     */
    public function deactivate(): void
    {
        $this->update(['is_active' => false]);
    }

    /**
     * Get the excerpt of the message.
     */
    public function getExcerptAttribute(int $length = 100): string
    {
        if (strlen($this->message) <= $length) {
            return $this->message;
        }

        return substr($this->message, 0, $length) . '...';
    }

    /**
     * Get announcements that should be displayed to users.
     */
    public static function getVisibleAnnouncements(?int $limit = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = static::visible()->latest('published_at');

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * Get the latest visible announcement.
     */
    public static function getLatestVisible(): ?self
    {
        return static::visible()->latest('published_at')->first();
    }

    /**
     * Check if there are any new announcements since a given date.
     */
    public static function hasNewAnnouncementsSince(Carbon $since): bool
    {
        return static::visible()
            ->where('published_at', '>', $since)
            ->exists();
    }
}
