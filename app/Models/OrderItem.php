<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use VpnSubscriptionOrderItemDTO;

class OrderItem extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'order_id',
        'item_type',
        'item_id',
        'action',
        'notes',
        'admin_notes',
        'quantity',
        'unit_price',
    ];

    public const ACTION_NEW = 'new';
    public const ACTION_RENEW = 'renew';
    public const ACTION_UPGRADE = 'upgrade';
    public const ACTION_EXTEND = 'extend';
    public const ACTION_DOWNGRADE = 'downgrade';

    public const ACTION_TYPES = [
        self::ACTION_NEW,
        self::ACTION_RENEW,
        self::ACTION_UPGRADE,
        self::ACTION_EXTEND,
        self::ACTION_DOWNGRADE,
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'quantity' => 'integer',
            'unit_price' => 'integer',
        ];
    }

    public static function createVpnSubscriptionItemFromDTO(VpnSubscriptionOrderItemDTO $dto, Order $order, ?string $actionType = self::ACTION_NEW): OrderItem
    {
        return OrderItem::create([
            'order_id' => $order->id,
            'item_type' => $dto->itemType,
            'item_id' => $dto->subscriptionPlanId,
            'action' => $actionType,
            'quantity' => 1,
            'unit_price' => $order->price,
        ]);
    }

    /**
     * Get the order that owns this item.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the related item (subscription plan, addon, etc.).
     */
    public function item(): MorphTo
    {
        return $this->morphTo('item', 'item_type', 'item_id');
    }

    /**
     * Get the subscription plan if this item is a subscription plan.
     */
    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class, 'item_id')
            ->where('item_type', 'subscription_plan');
    }

    /**
     * Scope to get items of specific type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('item_type', $type);
    }

    /**
     * Scope to get subscription plan items.
     */
    public function scopeSubscriptionPlans($query)
    {
        return $query->where('item_type', 'subscription_plan');
    }

    /**
     * Scope to get addon items.
     */
    public function scopeAddons($query)
    {
        return $query->where('item_type', 'addon');
    }

    /**
     * Scope to get custom items.
     */
    public function scopeCustom($query)
    {
        return $query->where('item_type', 'custom');
    }

    /**
     * Get the total price for this item (quantity * unit_price).
     */
    public function getTotalPriceAttribute(): int
    {
        return $this->quantity * $this->unit_price;
    }

    /**
     * Get the formatted unit price in major units.
     */
    public function getFormattedUnitPriceAttribute(): string
    {
        $currency = $this->order->currency ?? 'RUB';
        return number_format($this->unit_price / 100, 2) . ' ' . $currency;
    }

    /**
     * Get the formatted total price in major units.
     */
    public function getFormattedTotalPriceAttribute(): string
    {
        $currency = $this->order->currency ?? 'RUB';
        return number_format($this->total_price / 100, 2) . ' ' . $currency;
    }

    /**
     * Check if this item is a subscription plan.
     */
    public function isSubscriptionPlan(): bool
    {
        return $this->item_type === 'subscription_plan';
    }

    /**
     * Check if this item is an addon.
     */
    public function isAddon(): bool
    {
        return $this->item_type === 'addon';
    }

    /**
     * Check if this item is a custom item.
     */
    public function isCustom(): bool
    {
        return $this->item_type === 'custom';
    }

    /**
     * Get the item name for display.
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->isSubscriptionPlan() && $this->subscriptionPlan) {
            return $this->subscriptionPlan->name;
        }

        return $this->notes ?? 'Unknown Item';
    }

    /**
     * Update the order total when this item is saved.
     */
    protected static function booted(): void
    {
        static::saved(function (OrderItem $orderItem) {
            $orderItem->order->updateTotalAmount();
        });

        static::deleted(function (OrderItem $orderItem) {
            $orderItem->order->updateTotalAmount();
        });
    }
}
