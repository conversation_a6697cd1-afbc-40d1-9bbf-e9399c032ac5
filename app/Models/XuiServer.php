<?php

namespace App\Models;

use App\DTOs\Xui\InboundDTO;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class XuiServer extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'address',
        'port',
        'web_base_path',
        'username',
        'password',
        'notes',
        'last_login_at',
        'session_cookie',
        'is_active',
        'auto_sync',
        'system_client_id_for_inbound',
        'server_load',
        'clients_count',
        'clients_online_count',
        'clients_online_list',
        'raw_server_status',
        'raw_settings_all',
        'raw_settings_short',
        'raw_inbounds_list',
        'raw_clients_online',
        'raw_server_status_updated_at',
        'raw_settings_all_updated_at',
        'raw_settings_short_updated_at',
        'raw_inbounds_list_updated_at',
        'raw_clients_online_updated_at',
        'last_sync_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'session_cookie',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'port' => 'integer',
            'last_login_at' => 'datetime',
            'is_active' => 'boolean',
            'auto_sync' => 'boolean',
            'server_load' => 'decimal:2',
            'clients_count' => 'integer',
            'clients_online_count' => 'integer',
            'clients_online_list' => 'array',
            'raw_server_status' => 'array',
            'raw_settings_all' => 'array',
            'raw_settings_short' => 'array',
            'raw_inbounds_list' => 'array',
            'raw_clients_online' => 'array',
            'raw_server_status_updated_at' => 'datetime',
            'raw_settings_all_updated_at' => 'datetime',
            'raw_settings_short_updated_at' => 'datetime',
            'raw_inbounds_list_updated_at' => 'datetime',
            'raw_clients_online_updated_at' => 'datetime',
            'last_sync_at' => 'datetime',
        ];
    }


    /* ---------------- Relationships ---------------- */

    /**
     * Get the server pools this server is assigned to.
     */
    public function serverPools(): BelongsToMany
    {
        return $this->belongsToMany(ServerPool::class, 'xui_server_pool_assignments', 'xui_server_id', 'server_pool_id')
            ->withPivot(['assigned_at', 'released_at'])
            ->withTimestamps();
    }

    /**
     * Get the traffic logs for this server.
     */
    public function trafficLogs(): HasMany
    {
        return $this->hasMany(UserTrafficLog::class);
    }

    /**
     * Get the online logs for this server.
     */
    public function onlineLogs(): HasMany
    {
        return $this->hasMany(UserOnlineLog::class);
    }


    /* ---------------- Filtered Relationships ---------------- */

    /**
     * Get active server pools for this server.
     */
    public function activeServerPools(): BelongsToMany
    {
        return $this->serverPools()
            ->where('server_pools.is_active', true)
            ->wherePivotNull('released_at');
    }


    /* ---------------- Accessors & Mutators ---------------- */

    /**
     * Get the full server URL.
     */
    public function getFullUrlAttribute(bool $useSsl = true): string
    {
        $protocol = $useSsl ? 'https' : 'http';
        $basePath = $this->web_base_path ? '/' . trim($this->web_base_path, '/') : '';

        return "{$protocol}://{$this->address}:{$this->port}{$basePath}";
    }

    public function inbounds(): Collection
    {
        // return inbound as InboundDTO objects
        return $this->raw_inbounds_list
            ? collect($this->raw_inbounds_list)->map(function ($inbound) {
                return InboundDTO::makeDtoFromInboundArray($inbound);
            })
            : collect([]);
    }

    /* ---------------- Scopes ---------------- */

    /**
     * Scope to get only active servers.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get servers with auto sync enabled.
     */
    public function scopeAutoSync($query)
    {
        return $query->where('auto_sync', true);
    }


    /* ---------------- Events ---------------- */

    protected static function booted(): void
    {
        static::creating(function (self $server) {
            // generate uuid for system_client_id_for_inbound
            $server->system_client_id_for_inbound = (string) Str::uuid();
        });
    }


    /* ---------------- State Checks ---------------- */

    /**
     * Check if the server is online and accessible.
     */
    public function isOnline(): bool
    {
        return $this->last_sync_at &&
               $this->last_sync_at->diffInMinutes(now()) <= 10;
    }

    /**
     * Check if the server needs authentication.
     */
    public function needsAuthentication(): bool
    {
        return !$this->session_cookie ||
               !$this->last_login_at ||
               $this->last_login_at->diffInHours(now()) >= 24;
    }

    /**
     * Get the server load status.
     */
    public function getLoadStatusAsString(): string
    {
        if (!$this->server_load) {
            return 'unknown';
        }

        if ($this->server_load < 50) {
            return 'low';
        } elseif ($this->server_load < 80) {
            return 'medium';
        } else {
            return 'high';
        }
    }

    /**
     * Check if server data is stale and needs refresh.
     */
    public function isDataStale(int $minutes = 30): bool
    {
        return !$this->last_sync_at ||
               $this->last_sync_at->diffInMinutes(now()) > $minutes;
    }



}
