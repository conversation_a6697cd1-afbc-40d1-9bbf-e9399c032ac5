<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class SubscriptionHistory extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'subscription_id',
        'user_id',
        'change_type',
        'notes',
        'admin_notes',
        'delta_duration',
        'delta_duration_units',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'delta_duration' => 'integer',
        ];
    }

    /**
     * Get the subscription this history record belongs to.
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * Get the user this history record belongs to.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get history for a specific subscription.
     */
    public function scopeForSubscription($query, $subscriptionId)
    {
        return $query->where('subscription_id', $subscriptionId);
    }

    /**
     * Scope to get history for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get history by change type.
     */
    public function scopeByChangeType($query, string $changeType)
    {
        return $query->where('change_type', $changeType);
    }

    /**
     * Scope to get creation records.
     */
    public function scopeCreated($query)
    {
        return $query->where('change_type', 'created');
    }

    /**
     * Scope to get extension records.
     */
    public function scopeExtended($query)
    {
        return $query->where('change_type', 'extended');
    }

    /**
     * Scope to get upgrade records.
     */
    public function scopeUpgraded($query)
    {
        return $query->where('change_type', 'upgraded');
    }

    /**
     * Scope to get expiration records.
     */
    public function scopeExpired($query)
    {
        return $query->where('change_type', 'expired');
    }

    /**
     * Scope to get manual change records.
     */
    public function scopeManual($query)
    {
        return $query->where('change_type', 'manual');
    }

    /**
     * Scope to get history within a date range.
     */
    public function scopeWithinDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope to get recent history.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Check if this is a creation record.
     */
    public function isCreation(): bool
    {
        return $this->change_type === 'created';
    }

    /**
     * Check if this is an extension record.
     */
    public function isExtension(): bool
    {
        return $this->change_type === 'extended';
    }

    /**
     * Check if this is an upgrade record.
     */
    public function isUpgrade(): bool
    {
        return $this->change_type === 'upgraded';
    }

    /**
     * Check if this is an expiration record.
     */
    public function isExpiration(): bool
    {
        return $this->change_type === 'expired';
    }

    /**
     * Check if this is a manual change record.
     */
    public function isManual(): bool
    {
        return $this->change_type === 'manual';
    }

    /**
     * Get the formatted delta duration.
     */
    public function getFormattedDeltaDurationAttribute(): ?string
    {
        if (!$this->delta_duration || !$this->delta_duration_units) {
            return null;
        }

        $unit = $this->delta_duration_units;
        if ($this->delta_duration !== 1) {
            $unit = $this->pluralizeUnit($unit);
        }

        return "{$this->delta_duration} {$unit}";
    }

    /**
     * Get the change type label.
     */
    public function getChangeTypeLabelAttribute(): string
    {
        return match ($this->change_type) {
            'created' => 'Created',
            'renewed' => 'Renewed',
            'extended' => 'Extended',
            'upgraded' => 'Upgraded',
            'expired' => 'Expired',
            'manual' => 'Manual Change',
            default => ucfirst($this->change_type),
        };
    }

    /**
     * Get the change type description.
     */
    public function getChangeTypeDescriptionAttribute(): string
    {
        return match ($this->change_type) {
            'created' => 'Subscription was created',
            'renewed' => 'Subscription was renewed',
            'extended' => 'Subscription duration was extended',
            'upgraded' => 'Subscription was upgraded to a higher plan',
            'expired' => 'Subscription expired',
            'manual' => 'Manual change made by administrator',
            default => 'Unknown change type',
        };
    }

    /**
     * Get the time elapsed since this change.
     */
    public function getTimeElapsedAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Pluralize duration unit.
     */
    private function pluralizeUnit(string $unit): string
    {
        return match ($unit) {
            'hour' => 'hours',
            'day' => 'days',
            'week' => 'weeks',
            'month' => 'months',
            'year' => 'years',
            default => $unit,
        };
    }

    /**
     * Create a history record for subscription creation.
     */
    public static function recordCreation(Subscription $subscription, ?string $notes = null): self
    {
        return static::create([
            'subscription_id' => $subscription->id,
            'user_id' => $subscription->user_id,
            'change_type' => 'created',
            'notes' => $notes,
        ]);
    }

    /**
     * Create a history record for subscription renewal.
     */
    public static function recordRenewal(Subscription $subscription, ?string $notes = null): self
    {
        return static::create([
            'subscription_id' => $subscription->id,
            'user_id' => $subscription->user_id,
            'change_type' => 'renewed',
            'notes' => $notes,
        ]);
    }

    /**
     * Create a history record for subscription extension.
     */
    public static function recordExtension(
        Subscription $subscription,
        int $deltaDuration,
        string $deltaDurationUnits,
        ?string $notes = null
    ): self {
        return static::create([
            'subscription_id' => $subscription->id,
            'user_id' => $subscription->user_id,
            'change_type' => 'extended',
            'notes' => $notes,
            'delta_duration' => $deltaDuration,
            'delta_duration_units' => $deltaDurationUnits,
        ]);
    }

    /**
     * Create a history record for subscription upgrade.
     */
    public static function recordUpgrade(Subscription $subscription, ?string $notes = null): self
    {
        return static::create([
            'subscription_id' => $subscription->id,
            'user_id' => $subscription->user_id,
            'change_type' => 'upgraded',
            'notes' => $notes,
        ]);
    }

    /**
     * Create a history record for subscription cancellation.
     */
    public static function recordCancellation(Subscription $subscription, ?string $notes = null): self
    {
        return static::create([
            'subscription_id' => $subscription->id,
            'user_id' => $subscription->user_id,
            'change_type' => 'cancelled',
            'notes' => $notes,
        ]);
    }

    /**
     * Create a history record for subscription expiration.
     */
    public static function recordExpiration(Subscription $subscription, ?string $notes = null): self
    {
        return static::create([
            'subscription_id' => $subscription->id,
            'user_id' => $subscription->user_id,
            'change_type' => 'expired',
            'notes' => $notes,
        ]);
    }

    /**
     * Create a history record for manual changes.
     */
    public static function recordManualChange(
        Subscription $subscription,
        ?string $notes = null,
        ?string $adminNotes = null
    ): self {
        return static::create([
            'subscription_id' => $subscription->id,
            'user_id' => $subscription->user_id,
            'change_type' => 'manual',
            'notes' => $notes,
            'admin_notes' => $adminNotes,
        ]);
    }

    /**
     * Get subscription history statistics.
     */
    public static function getStatistics(): array
    {
        $total = static::count();
        $byType = static::selectRaw('change_type, COUNT(*) as count')
            ->groupBy('change_type')
            ->pluck('count', 'change_type')
            ->toArray();

        return [
            'total_changes' => $total,
            'by_type' => $byType,
            'created_count' => $byType['created'] ?? 0,
            'extended_count' => $byType['extended'] ?? 0,
            'upgraded_count' => $byType['upgraded'] ?? 0,
            'expired_count' => $byType['expired'] ?? 0,
            'manual_count' => $byType['manual'] ?? 0,
        ];
    }

    /**
     * Get recent activity for a subscription.
     */
    public static function getRecentActivityForSubscription($subscriptionId, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return static::forSubscription($subscriptionId)
            ->latest()
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent activity for a user.
     */
    public static function getRecentActivityForUser($userId, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return static::forUser($userId)
            ->latest()
            ->limit($limit)
            ->get();
    }
}
