<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserTrafficLog extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'subscription_id',
        'xui_server_id',
        'traffic_used_bytes',
        'traffic_up_bytes',
        'traffic_down_bytes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'traffic_used_bytes' => 'integer',
            'traffic_up_bytes' => 'integer',
            'traffic_down_bytes' => 'integer',
        ];
    }

    /**
     * Get the user this traffic log belongs to.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subscription this traffic log belongs to.
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * Get the XUI server this traffic log is from.
     */
    public function xuiServer(): BelongsTo
    {
        return $this->belongsTo(XuiServer::class);
    }

    /**
     * Scope to get logs for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get logs for a specific subscription.
     */
    public function scopeForSubscription($query, $subscriptionId)
    {
        return $query->where('subscription_id', $subscriptionId);
    }

    /**
     * Scope to get logs for a specific server.
     */
    public function scopeForServer($query, $serverId)
    {
        return $query->where('xui_server_id', $serverId);
    }

    /**
     * Scope to get logs within a date range.
     */
    public function scopeWithinDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope to get logs for today.
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    /**
     * Scope to get logs for this week.
     */
    public function scopeThisWeek($query)
    {
        return $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
    }

    /**
     * Scope to get logs for this month.
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
    }

    /**
     * Get the formatted traffic used in human readable format.
     */
    public function getFormattedTrafficUsedAttribute(): string
    {
        return $this->formatBytes($this->traffic_used_bytes);
    }

    /**
     * Get the formatted traffic up in human readable format.
     */
    public function getFormattedTrafficUpAttribute(): string
    {
        return $this->formatBytes($this->traffic_up_bytes);
    }

    /**
     * Get the formatted traffic down in human readable format.
     */
    public function getFormattedTrafficDownAttribute(): string
    {
        return $this->formatBytes($this->traffic_down_bytes);
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Get the traffic ratio (up/down).
     */
    public function getTrafficRatio(): float
    {
        if ($this->traffic_down_bytes === 0) {
            return $this->traffic_up_bytes > 0 ? INF : 0;
        }

        return $this->traffic_up_bytes / $this->traffic_down_bytes;
    }

    /**
     * Check if this is a high traffic usage log.
     */
    public function isHighTrafficUsage(int $thresholdMB = 1000): bool
    {
        return $this->traffic_used_bytes > ($thresholdMB * 1024 * 1024);
    }

    /**
     * Get the traffic usage percentage for the subscription.
     */
    public function getTrafficUsagePercentage(): ?float
    {
        if (!$this->subscription || !$this->subscription->plan || !$this->subscription->plan->traffic_limit_bytes) {
            return null;
        }

        return min(100, ($this->traffic_used_bytes / $this->subscription->plan->traffic_limit_bytes) * 100);
    }

    /**
     * Aggregate traffic logs for a user within a date range.
     */
    public static function aggregateForUser($userId, $startDate, $endDate): array
    {
        $logs = static::forUser($userId)
            ->withinDateRange($startDate, $endDate)
            ->get();

        return [
            'total_traffic_used' => $logs->sum('traffic_used_bytes'),
            'total_traffic_up' => $logs->sum('traffic_up_bytes'),
            'total_traffic_down' => $logs->sum('traffic_down_bytes'),
            'log_count' => $logs->count(),
        ];
    }

    /**
     * Aggregate traffic logs for a subscription within a date range.
     */
    public static function aggregateForSubscription($subscriptionId, $startDate, $endDate): array
    {
        $logs = static::forSubscription($subscriptionId)
            ->withinDateRange($startDate, $endDate)
            ->get();

        return [
            'total_traffic_used' => $logs->sum('traffic_used_bytes'),
            'total_traffic_up' => $logs->sum('traffic_up_bytes'),
            'total_traffic_down' => $logs->sum('traffic_down_bytes'),
            'log_count' => $logs->count(),
        ];
    }
}
