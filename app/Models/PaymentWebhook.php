<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentWebhook extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'payment_id',
        'method_id',
        'external_payment_id',
        'raw_payload',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'raw_payload' => 'array',
        ];
    }

    /**
     * Get the payment this webhook belongs to.
     */
    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class);
    }

    /**
     * Get the payment method this webhook is for.
     */
    public function method(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class, 'method_id');
    }

    /**
     * Scope to get webhooks for a specific payment method.
     */
    public function scopeForMethod($query, $methodId)
    {
        return $query->where('method_id', $methodId);
    }

    /**
     * Scope to get webhooks for a specific external payment ID.
     */
    public function scopeForExternalPayment($query, string $externalPaymentId)
    {
        return $query->where('external_payment_id', $externalPaymentId);
    }

    /**
     * Scope to get recent webhooks.
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Get the webhook event type from the payload.
     */
    public function getEventTypeAttribute(): ?string
    {
        if (!$this->raw_payload) {
            return null;
        }

        // Try to extract event type from common webhook payload structures
        return $this->raw_payload['event'] ?? 
               $this->raw_payload['type'] ?? 
               $this->raw_payload['event_type'] ?? 
               null;
    }

    /**
     * Get the webhook status from the payload.
     */
    public function getWebhookStatusAttribute(): ?string
    {
        if (!$this->raw_payload) {
            return null;
        }

        // Try to extract status from common webhook payload structures
        return $this->raw_payload['status'] ?? 
               $this->raw_payload['object']['status'] ?? 
               $this->raw_payload['data']['object']['status'] ?? 
               null;
    }

    /**
     * Check if this webhook indicates a successful payment.
     */
    public function isSuccessfulPayment(): bool
    {
        $eventType = $this->event_type;
        $status = $this->webhook_status;

        // Common success indicators across different payment providers
        $successEvents = [
            'payment.succeeded',
            'payment_intent.succeeded',
            'charge.succeeded',
            'invoice.payment_succeeded',
            'payment.captured',
            'payment.confirmed',
        ];

        $successStatuses = [
            'succeeded',
            'paid',
            'completed',
            'captured',
            'confirmed',
        ];

        return in_array($eventType, $successEvents) || 
               in_array($status, $successStatuses);
    }

    /**
     * Check if this webhook indicates a failed payment.
     */
    public function isFailedPayment(): bool
    {
        $eventType = $this->event_type;
        $status = $this->webhook_status;

        // Common failure indicators across different payment providers
        $failureEvents = [
            'payment.failed',
            'payment_intent.payment_failed',
            'charge.failed',
            'invoice.payment_failed',
            'payment.declined',
        ];

        $failureStatuses = [
            'failed',
            'declined',
            'canceled',
            'cancelled',
            'rejected',
        ];

        return in_array($eventType, $failureEvents) || 
               in_array($status, $failureStatuses);
    }

    /**
     * Get the amount from the webhook payload.
     */
    public function getWebhookAmountAttribute(): ?int
    {
        if (!$this->raw_payload) {
            return null;
        }

        // Try to extract amount from common webhook payload structures
        return $this->raw_payload['amount'] ?? 
               $this->raw_payload['object']['amount'] ?? 
               $this->raw_payload['data']['object']['amount'] ?? 
               $this->raw_payload['amount_received'] ?? 
               null;
    }

    /**
     * Get the currency from the webhook payload.
     */
    public function getWebhookCurrencyAttribute(): ?string
    {
        if (!$this->raw_payload) {
            return null;
        }

        // Try to extract currency from common webhook payload structures
        return $this->raw_payload['currency'] ?? 
               $this->raw_payload['object']['currency'] ?? 
               $this->raw_payload['data']['object']['currency'] ?? 
               null;
    }

    /**
     * Process this webhook and update the related payment.
     */
    public function processWebhook(): bool
    {
        if (!$this->payment) {
            return false;
        }

        if ($this->isSuccessfulPayment()) {
            $this->payment->markAsPaid();
            return true;
        }

        if ($this->isFailedPayment()) {
            $this->payment->markAsFailed();
            return true;
        }

        return false;
    }
}
