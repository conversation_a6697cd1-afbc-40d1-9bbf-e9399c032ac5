<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserServerAssignment extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'user_server_assignments';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'pool_id',
        'assigned_at',
        'released_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'assigned_at' => 'datetime',
            'released_at' => 'datetime',
        ];
    }

    /**
     * Get the user this assignment belongs to.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the server pool this assignment belongs to.
     */
    public function serverPool(): BelongsTo
    {
        return $this->belongsTo(ServerPool::class, 'pool_id');
    }

    /**
     * Scope to get active assignments (not released).
     */
    public function scopeActive($query)
    {
        return $query->whereNull('released_at');
    }

    /**
     * Scope to get released assignments.
     */
    public function scopeReleased($query)
    {
        return $query->whereNotNull('released_at');
    }

    /**
     * Scope to get assignments for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get assignments for a specific server pool.
     */
    public function scopeForPool($query, $poolId)
    {
        return $query->where('pool_id', $poolId);
    }

    /**
     * Scope to get assignments within a date range.
     */
    public function scopeAssignedBetween($query, $startDate, $endDate)
    {
        return $query->whereBetween('assigned_at', [$startDate, $endDate]);
    }

    /**
     * Scope to get assignments released within a date range.
     */
    public function scopeReleasedBetween($query, $startDate, $endDate)
    {
        return $query->whereBetween('released_at', [$startDate, $endDate]);
    }

    /**
     * Check if the assignment is currently active.
     */
    public function isActive(): bool
    {
        return $this->released_at === null;
    }

    /**
     * Check if the assignment has been released.
     */
    public function isReleased(): bool
    {
        return $this->released_at !== null;
    }

    /**
     * Get the duration of the assignment.
     */
    public function getDurationAttribute(): ?int
    {
        if (!$this->assigned_at) {
            return null;
        }

        $endTime = $this->released_at ?: now();
        return $this->assigned_at->diffInMinutes($endTime);
    }

    /**
     * Get the formatted duration.
     */
    public function getFormattedDurationAttribute(): ?string
    {
        $duration = $this->duration;
        
        if ($duration === null) {
            return null;
        }

        if ($duration < 60) {
            return "{$duration} minutes";
        }

        $hours = floor($duration / 60);
        $minutes = $duration % 60;

        if ($hours < 24) {
            return $minutes > 0 ? "{$hours}h {$minutes}m" : "{$hours} hours";
        }

        $days = floor($hours / 24);
        $remainingHours = $hours % 24;

        return $remainingHours > 0 ? "{$days}d {$remainingHours}h" : "{$days} days";
    }

    /**
     * Release the assignment.
     */
    public function release(): void
    {
        $this->update(['released_at' => now()]);
    }

    /**
     * Assign a user to a pool.
     */
    public static function assignUserToPool(string $userId, int $poolId): self
    {
        // Check if there's already an active assignment
        $existingAssignment = static::forUser($userId)
            ->forPool($poolId)
            ->active()
            ->first();

        if ($existingAssignment) {
            return $existingAssignment;
        }

        return static::create([
            'user_id' => $userId,
            'pool_id' => $poolId,
            'assigned_at' => now(),
        ]);
    }

    /**
     * Release a user from a pool.
     */
    public static function releaseUserFromPool(string $userId, int $poolId): bool
    {
        $assignment = static::forUser($userId)
            ->forPool($poolId)
            ->active()
            ->first();

        if ($assignment) {
            $assignment->release();
            return true;
        }

        return false;
    }

    /**
     * Release all active assignments for a user.
     */
    public static function releaseAllForUser(string $userId): int
    {
        $assignments = static::forUser($userId)->active()->get();
        
        foreach ($assignments as $assignment) {
            $assignment->release();
        }

        return $assignments->count();
    }

    /**
     * Get active assignments for a user.
     */
    public static function getActiveAssignmentsForUser(string $userId): \Illuminate\Database\Eloquent\Collection
    {
        return static::forUser($userId)
            ->active()
            ->with('serverPool')
            ->get();
    }

    /**
     * Get active assignments for a pool.
     */
    public static function getActiveAssignmentsForPool(int $poolId): \Illuminate\Database\Eloquent\Collection
    {
        return static::forPool($poolId)
            ->active()
            ->with('user')
            ->get();
    }

    /**
     * Get the current pool for a user.
     */
    public static function getCurrentPoolForUser(string $userId): ?ServerPool
    {
        $assignment = static::forUser($userId)
            ->active()
            ->with('serverPool')
            ->first();

        return $assignment ? $assignment->serverPool : null;
    }

    /**
     * Check if a user is assigned to a specific pool.
     */
    public static function isUserAssignedToPool(string $userId, int $poolId): bool
    {
        return static::forUser($userId)
            ->forPool($poolId)
            ->active()
            ->exists();
    }

    /**
     * Get assignment statistics.
     */
    public static function getStatistics(): array
    {
        $total = static::count();
        $active = static::active()->count();
        $released = static::released()->count();

        return [
            'total_assignments' => $total,
            'active_assignments' => $active,
            'released_assignments' => $released,
            'active_percentage' => $total > 0 ? round(($active / $total) * 100, 1) : 0,
        ];
    }

    /**
     * Get assignment history for a user.
     */
    public static function getHistoryForUser(string $userId, int $limit = 50): \Illuminate\Database\Eloquent\Collection
    {
        return static::forUser($userId)
            ->with('serverPool')
            ->latest('assigned_at')
            ->limit($limit)
            ->get();
    }

    /**
     * Get assignment history for a pool.
     */
    public static function getHistoryForPool(int $poolId, int $limit = 50): \Illuminate\Database\Eloquent\Collection
    {
        return static::forPool($poolId)
            ->with('user')
            ->latest('assigned_at')
            ->limit($limit)
            ->get();
    }

    /**
     * Get the count of active users in a pool.
     */
    public static function getActiveUserCountForPool(int $poolId): int
    {
        return static::forPool($poolId)
            ->active()
            ->count();
    }

    /**
     * Get the count of active pools for a user.
     */
    public static function getActivePoolCountForUser(string $userId): int
    {
        return static::forUser($userId)
            ->active()
            ->count();
    }

    /**
     * Find the best available pool for a user.
     */
    public static function findBestAvailablePool(): ?ServerPool
    {
        return ServerPool::active()
            ->whereRaw('(SELECT COUNT(*) FROM user_server_assignments WHERE pool_id = server_pools.id AND released_at IS NULL) < max_users')
            ->orderByRaw('(SELECT COUNT(*) FROM user_server_assignments WHERE pool_id = server_pools.id AND released_at IS NULL) ASC')
            ->first();
    }

    /**
     * Auto-assign a user to the best available pool.
     */
    public static function autoAssignUser(string $userId): ?self
    {
        // Check if user is already assigned to an active pool
        $existingAssignment = static::forUser($userId)->active()->first();
        if ($existingAssignment) {
            return $existingAssignment;
        }

        // Find the best available pool
        $pool = static::findBestAvailablePool();
        if (!$pool) {
            return null;
        }

        return static::assignUserToPool($userId, $pool->id);
    }
}
