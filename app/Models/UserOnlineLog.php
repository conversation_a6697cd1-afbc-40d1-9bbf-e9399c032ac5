<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserOnlineLog extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'xui_server_id',
        'status',
    ];

    // user statuses
    const STATUS_ONLINE = 'online';
    const STATUS_OFFLINE = 'offline';
    const STATUS_UNKNOWN = 'unknown';

    /**
     * Get the user this online log belongs to.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the XUI server this online log is from.
     */
    public function xuiServer(): BelongsTo
    {
        return $this->belongsTo(XuiServer::class);
    }

    /**
     * Scope to get logs for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get logs for a specific server.
     */
    public function scopeForServer($query, $serverId)
    {
        return $query->where('xui_server_id', $serverId);
    }

    /**
     * Scope to get online status logs.
     */
    public function scopeOnline($query)
    {
        return $query->where('status', 'online');
    }

    /**
     * Scope to get offline status logs.
     */
    public function scopeOffline($query)
    {
        return $query->where('status', 'offline');
    }

    /**
     * Scope to get unknown status logs.
     */
    public function scopeUnknown($query)
    {
        return $query->where('status', 'unknown');
    }

    /**
     * Scope to get logs within a date range.
     */
    public function scopeWithinDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope to get logs for today.
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    /**
     * Scope to get logs for this week.
     */
    public function scopeThisWeek($query)
    {
        return $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
    }

    /**
     * Scope to get logs for this month.
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
    }

    /**
     * Scope to get recent logs.
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Check if this log indicates the user went online.
     */
    public function isOnlineStatus(): bool
    {
        return $this->status === 'online';
    }

    /**
     * Check if this log indicates the user went offline.
     */
    public function isOfflineStatus(): bool
    {
        return $this->status === 'offline';
    }

    /**
     * Check if this log has unknown status.
     */
    public function isUnknownStatus(): bool
    {
        return $this->status === 'unknown';
    }

    /**
     * Get the time elapsed since this log was created.
     */
    public function getTimeElapsedAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Get the latest online status for a user.
     */
    public static function getLatestStatusForUser($userId): ?string
    {
        $latestLog = static::forUser($userId)->latest()->first();
        return $latestLog ? $latestLog->status : null;
    }

    /**
     * Check if a user is currently online based on recent logs.
     */
    public static function isUserCurrentlyOnline($userId, int $minutesThreshold = 5): bool
    {
        $recentLog = static::forUser($userId)
            ->where('created_at', '>=', now()->subMinutes($minutesThreshold))
            ->latest()
            ->first();

        return $recentLog && $recentLog->status === 'online';
    }

    /**
     * Get online time statistics for a user within a date range.
     */
    public static function getOnlineStatsForUser($userId, $startDate, $endDate): array
    {
        $logs = static::forUser($userId)
            ->withinDateRange($startDate, $endDate)
            ->orderBy('created_at')
            ->get();

        $totalOnlineTime = 0;
        $onlineSessions = 0;
        $lastOnlineTime = null;

        foreach ($logs as $log) {
            if ($log->status === 'online') {
                $lastOnlineTime = $log->created_at;
                $onlineSessions++;
            } elseif ($log->status === 'offline' && $lastOnlineTime) {
                $sessionDuration = $lastOnlineTime->diffInMinutes($log->created_at);
                $totalOnlineTime += $sessionDuration;
                $lastOnlineTime = null;
            }
        }

        // If the last status was online and no offline log follows,
        // consider the session ongoing until now (or end date)
        if ($lastOnlineTime) {
            $endTime = min(now(), $endDate);
            $sessionDuration = $lastOnlineTime->diffInMinutes($endTime);
            $totalOnlineTime += $sessionDuration;
        }

        return [
            'total_online_time_minutes' => $totalOnlineTime,
            'total_online_time_hours' => round($totalOnlineTime / 60, 2),
            'online_sessions' => $onlineSessions,
            'average_session_duration_minutes' => $onlineSessions > 0 ? round($totalOnlineTime / $onlineSessions, 2) : 0,
        ];
    }

    /**
     * Get the count of users currently online on a specific server.
     */
    public static function getCurrentlyOnlineCountForServer($serverId, int $minutesThreshold = 5): int
    {
        return static::forServer($serverId)
            ->where('created_at', '>=', now()->subMinutes($minutesThreshold))
            ->where('status', 'online')
            ->distinct('user_id')
            ->count('user_id');
    }

    /**
     * Get the list of users currently online on a specific server.
     */
    public static function getCurrentlyOnlineUsersForServer($serverId, int $minutesThreshold = 5): array
    {
        return static::forServer($serverId)
            ->where('created_at', '>=', now()->subMinutes($minutesThreshold))
            ->where('status', 'online')
            ->distinct('user_id')
            ->pluck('user_id')
            ->toArray();
    }
}
