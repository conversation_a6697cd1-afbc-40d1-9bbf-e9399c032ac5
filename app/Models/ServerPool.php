<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ServerPool extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'notes',
        'max_users',
        'is_active',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'max_users' => 'integer',
            'is_active' => 'boolean',
        ];
    }


    /* ---------------- Relationships ---------------- */

    /**
     * Get the XUI servers assigned to this pool.
     */
    public function servers(): BelongsToMany
    {
        return $this->belongsToMany(XuiServer::class, 'xui_server_pool_assignments', 'server_pool_id', 'xui_server_id')
            ->withPivot(['assigned_at', 'released_at'])
            ->withTimestamps();
    }

    /**
     * Get the users assigned to this pool.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_server_assignments', 'pool_id', 'user_id')
            ->withPivot(['assigned_at', 'released_at'])
            ->withTimestamps();
    }

    /**
     * Get active XUI servers in this pool.
     */
    public function activeServers(): BelongsToMany
    {
        return $this->servers()
            ->where('xui_servers.is_active', true)
            ->wherePivotNull('released_at');
    }

    /**
     * Get active users in this pool.
     */
    public function activeUsers(): BelongsToMany
    {
        return $this->users()
            ->where('users.is_active', true)
            ->wherePivotNull('released_at');
    }


    /* ---------------- Scopes ---------------- */

    /**
     * Scope to get only active pools.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }


    /* ---------------- Accessors & Mutators ---------------- */



    /* ---------------- State Checks ---------------- */

    /**
     * Check if the pool has available capacity for new users.
     */
    public function hasAvailableCapacity(): bool
    {
        return $this->getCurrentUsersCount() < $this->max_users;
    }

    /**
     * Check if the pool is full.
     */
    public function isFull(): bool
    {
        return $this->getCurrentUsersCount() >= $this->max_users;
    }


    /* ---------------- Helpers / Utilities ---------------- */



    /* ---------------- Aggregates / Stats ---------------- */

    /**
     * Get the current number of assigned users.
     */
    public function getCurrentUsersCount(): int
    {
        return $this->users()
            ->wherePivotNull('released_at')
            ->count();
    }

    /**
     * Get the current number of assigned servers.
     */
    public function getCurrentServersCount(): int
    {
        return $this->servers()
            ->wherePivotNull('released_at')
            ->count();
    }

    /**
     * Get the remaining capacity for new users.
     */
    public function getRemainingCapacity(): int
    {
        return max(0, $this->max_users - $this->getCurrentUsersCount());
    }

    /**
     * Get the usage percentage.
     */
    public function getUsagePercentage(): float
    {
        if ($this->max_users === 0) {
            return 0;
        }
        return ($this->getCurrentUsersCount() / $this->max_users) * 100;
    }

}
