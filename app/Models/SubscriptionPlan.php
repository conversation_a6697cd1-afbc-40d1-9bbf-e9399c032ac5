<?php

namespace App\Models;

use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class SubscriptionPlan extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'duration',
        'duration_units', // enum ['minute', 'hour', 'day', 'week', 'month', 'year'] or null
        'traffic_limit_bytes',
        'traffic_limit_duration',
        'traffic_limit_duration_unit', // enum ['minute', 'hour','day','week','month'] or null
        'price',
        'regular_price',
        'currency',
        'is_custom',
        'is_active',
        'is_demo',
        'is_public',
        'is_archived',
        'next_plan_id',
        'access_expires_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'traffic_limit_bytes' => 'integer',
            'traffic_limit_duration' => 'integer',
            'price' => 'integer',
            'regular_price' => 'integer',
            'is_custom' => 'boolean',
            'is_active' => 'boolean',
            'is_demo' => 'boolean',
            'is_public' => 'boolean',
            'is_archived' => 'boolean',
            'access_expires_at' => 'datetime',
        ];
    }


    /* ---------------- Relationships ---------------- */

    /**
     * Get the next plan that will be used when this plan is archived.
     */
    public function nextPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class, 'next_plan_id');
    }

    /**
     * Get the plans that have this plan as their next plan.
     */
    public function previousPlans(): HasMany
    {
        return $this->hasMany(SubscriptionPlan::class, 'next_plan_id');
    }

    /**
     * Get the subscriptions using this plan.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class, 'plan_id');
    }

    /**
     * Get the users who have this plan in their subscription.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'subscription_plan_id');
    }


    /* ---------------- Scopes ---------------- */

    /**
     * Scope to get only active plans.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get only public plans.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope to get only non-archived plans.
     */
    public function scopeNotArchived($query)
    {
        return $query->where('is_archived', false);
    }


    /* ---------------- Accessors & Mutators ---------------- */

    /**
     * Get the formatted price in major units.
     */
    public function getFormattedPriceAttribute(): string
    {
        return number_format($this->price / 100, 2) . ' ' . $this->currency;
    }

    /**
     * Get the formatted regular price in major units.
     */
    public function getFormattedRegularPriceAttribute(): ?string
    {
        if (!$this->regular_price) {
            return null;
        }
        return number_format($this->regular_price / 100, 2) . ' ' . $this->currency;
    }

    /**
     * Get the discount percentage.
     */
    public function getDiscountPercentage(): ?int
    {
        if (!$this->hasDiscount()) {
            return null;
        }
        return round((($this->regular_price - $this->price) / $this->regular_price) * 100);
    }


    /* ---------------- State Checks ---------------- */

    /**
     * Check if the plan has a discount.
     */
    public function hasDiscount(): bool
    {
        return $this->regular_price && $this->regular_price > $this->price;
    }


    /* ---------------- Actions ---------------- */

    /**
     * Archive the plan.
     */
    public function archive(): void
    {
        $this->update([
            'is_archived' => true,
        ]);
    }


    /* ---------------- Helpers / Utilities ---------------- */

    /**
     * Calculate end date based on start date and plan duration.
     */
    public function calculateEndDate(?Carbon $startDate = null): ?Carbon
    {
        if (!$startDate) {
            $startDate = now();
        }

        // calculate end date based on duration and duration units
        if ($this->duration && $this->duration_units) {
            return $startDate->add($this->duration_units, $this->duration);
        }

        return null;
    }


    /* ---------------- Aggregates / Stats ---------------- */

    // TODO:
    // toFormattedPrice
    // getDiscountPercentage


    public function toFormattedPrice(): string
    {
        return number_format($this->price / 100, 2) . ' ' . $this->currency;
    }
}
