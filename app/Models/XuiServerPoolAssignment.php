<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class XuiServerPoolAssignment extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'server_pool_id',
        'xui_server_id',
        'assigned_at',
        'released_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'assigned_at' => 'datetime',
            'released_at' => 'datetime',
        ];
    }

    /**
     * Get the server pool this assignment belongs to.
     */
    public function serverPool(): BelongsTo
    {
        return $this->belongsTo(ServerPool::class);
    }

    /**
     * Get the XUI server this assignment belongs to.
     */
    public function xuiServer(): BelongsTo
    {
        return $this->belongsTo(XuiServer::class);
    }

    /**
     * Scope to get active assignments (not released).
     */
    public function scopeActive($query)
    {
        return $query->whereNull('released_at');
    }

    /**
     * Scope to get released assignments.
     */
    public function scopeReleased($query)
    {
        return $query->whereNotNull('released_at');
    }

    /**
     * Scope to get assignments for a specific server pool.
     */
    public function scopeForServerPool($query, $serverPoolId)
    {
        return $query->where('server_pool_id', $serverPoolId);
    }

    /**
     * Scope to get assignments for a specific XUI server.
     */
    public function scopeForXuiServer($query, $xuiServerId)
    {
        return $query->where('xui_server_id', $xuiServerId);
    }

    /**
     * Scope to get assignments within a date range.
     */
    public function scopeAssignedBetween($query, $startDate, $endDate)
    {
        return $query->whereBetween('assigned_at', [$startDate, $endDate]);
    }

    /**
     * Scope to get assignments released within a date range.
     */
    public function scopeReleasedBetween($query, $startDate, $endDate)
    {
        return $query->whereBetween('released_at', [$startDate, $endDate]);
    }

    /**
     * Check if the assignment is currently active.
     */
    public function isActive(): bool
    {
        return $this->released_at === null;
    }

    /**
     * Check if the assignment has been released.
     */
    public function isReleased(): bool
    {
        return $this->released_at !== null;
    }

    /**
     * Get the duration of the assignment.
     */
    public function getDurationAttribute(): ?int
    {
        if (!$this->assigned_at) {
            return null;
        }

        $endTime = $this->released_at ?: now();
        return $this->assigned_at->diffInMinutes($endTime);
    }

    /**
     * Get the formatted duration.
     */
    public function getFormattedDurationAttribute(): ?string
    {
        $duration = $this->duration;

        if ($duration === null) {
            return null;
        }

        if ($duration < 60) {
            return "{$duration} minutes";
        }

        $hours = floor($duration / 60);
        $minutes = $duration % 60;

        if ($hours < 24) {
            return $minutes > 0 ? "{$hours}h {$minutes}m" : "{$hours} hours";
        }

        $days = floor($hours / 24);
        $remainingHours = $hours % 24;

        return $remainingHours > 0 ? "{$days}d {$remainingHours}h" : "{$days} days";
    }

    /**
     * Release the assignment.
     */
    public function release(): void
    {
        $this->update(['released_at' => now()]);
    }

    /**
     * Assign a server to a pool.
     */
    public static function assignServerToPool(int $xuiServerId, int $serverPoolId): self
    {
        // Check if there's already an active assignment
        $existingAssignment = static::forXuiServer($xuiServerId)
            ->forServerPool($serverPoolId)
            ->active()
            ->first();

        if ($existingAssignment) {
            return $existingAssignment;
        }

        return static::create([
            'xui_server_id' => $xuiServerId,
            'server_pool_id' => $serverPoolId,
            'assigned_at' => now(),
        ]);
    }

    /**
     * Release a server from a pool.
     */
    public static function releaseServerFromPool(int $xuiServerId, int $serverPoolId): bool
    {
        $assignment = static::forXuiServer($xuiServerId)
            ->forServerPool($serverPoolId)
            ->active()
            ->first();

        if ($assignment) {
            $assignment->release();
            return true;
        }

        return false;
    }

    /**
     * Get active assignments for a server pool.
     */
    public static function getActiveAssignmentsForPool(int $serverPoolId): \Illuminate\Database\Eloquent\Collection
    {
        return static::forServerPool($serverPoolId)
            ->active()
            ->with('xuiServer')
            ->get();
    }

    /**
     * Get active assignments for a XUI server.
     */
    public static function getActiveAssignmentsForServer(int $xuiServerId): \Illuminate\Database\Eloquent\Collection
    {
        return static::forXuiServer($xuiServerId)
            ->active()
            ->with('serverPool')
            ->get();
    }

    /**
     * Get assignment statistics.
     */
    public static function getStatistics(): array
    {
        $total = static::count();
        $active = static::active()->count();
        $released = static::released()->count();

        return [
            'total_assignments' => $total,
            'active_assignments' => $active,
            'released_assignments' => $released,
            'active_percentage' => $total > 0 ? round(($active / $total) * 100, 1) : 0,
        ];
    }

    /**
     * Get assignment history for a server pool.
     */
    public static function getHistoryForPool(int $serverPoolId, int $limit = 50): \Illuminate\Database\Eloquent\Collection
    {
        return static::forServerPool($serverPoolId)
            ->with('xuiServer')
            ->latest('assigned_at')
            ->limit($limit)
            ->get();
    }

    /**
     * Get assignment history for a XUI server.
     */
    public static function getHistoryForServer(int $xuiServerId, int $limit = 50): \Illuminate\Database\Eloquent\Collection
    {
        return static::forXuiServer($xuiServerId)
            ->with('serverPool')
            ->latest('assigned_at')
            ->limit($limit)
            ->get();
    }

    /**
     * Check if a server is assigned to a specific pool.
     */
    public static function isServerAssignedToPool(int $xuiServerId, int $serverPoolId): bool
    {
        return static::forXuiServer($xuiServerId)
            ->forServerPool($serverPoolId)
            ->active()
            ->exists();
    }

    /**
     * Get the count of active servers in a pool.
     */
    public static function getActiveServerCountForPool(int $serverPoolId): int
    {
        return static::forServerPool($serverPoolId)
            ->active()
            ->count();
    }

    /**
     * Get the count of active pools for a server.
     */
    public static function getActivePoolCountForServer(int $xuiServerId): int
    {
        return static::forXuiServer($xuiServerId)
            ->active()
            ->count();
    }
}
