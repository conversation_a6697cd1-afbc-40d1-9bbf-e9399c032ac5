<?php

namespace App\DTOs\Xui;

/**
 * A data transfer object for client settings: 'clientSettings' field of InboundDTO
 */
class ClientSettingsDTO
{
    public function __construct(
        public string $id, // Client id (UUID)
        public string $email, // Client email (user_email + "_" + inbound_id)
        public bool $enable,
        public int $expiryTime,
        public string $flow,
        public int $limitIp,
        public int $reset,
        public string $subId,
        public string $tgId,
        public int $totalGB
    ) {
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id, // Client id (UUID)
            'email' => $this->email, // Client email (user_email + "_" + inbound_id)
            'enable' => $this->enable,
            'expiryTime' => $this->expiryTime,
            'flow' => $this->flow,
            'limitIp' => $this->limitIp,
            'reset' => $this->reset,
            'subId' => $this->subId,
            'tgId' => $this->tgId,
            'totalGB' => $this->totalGB,
        ];
    }

}
