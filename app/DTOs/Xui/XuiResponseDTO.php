<?php

namespace App\DTOs\Xui;

use Illuminate\Support\Facades\Log;

final readonly class XuiResponseDTO
{
    public function __construct(
        public bool $success,
        public string $msg,
        public mixed $obj = null,
    ) {}

    public static function fromArray(array $data): self
    {
        return self::buildFromData($data);
    }

    public static function fromJson(string $json): self
    {
        $data = json_decode($json, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::error(__METHOD__ . ': Invalid JSON response', [
                'json' => $json,
                'error' => json_last_error_msg()
            ]);
            return self::errorResponse('Invalid JSON for XuiResponseDTO');
        }

        return self::buildFromData($data);
    }

    public static function fromXuiResponse(mixed $response): self
    {
        try {
            $data = self::normalizeResponse($response);
            return self::buildFromData($data);
        } catch (\Throwable $e) {
            Log::error(__METHOD__ . ': Failed to parse XUI response', [
                'response' => $response,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return self::errorResponse('Invalid XUI response format');
        }
    }

    private static function normalizeResponse(mixed $response): array
    {
        if (is_string($response)) {
            $data = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \InvalidArgumentException('Invalid JSON response: ' . json_last_error_msg());
            }

            return $data;
        }

        if (is_array($response)) {
            return $response;
        }

        throw new \InvalidArgumentException('Response must be JSON string or array');
    }

    private static function buildFromData(array $data): self
    {
        return new self(
            $data['success'] ?? false,
            $data['msg'] ?? '',
            $data['obj'] ?? null
        );
    }

    public static function errorResponse(string $message = 'Unknown error'): self
    {
        return new self(false, $message);
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function hasErrorMessage(): bool
    {
        return !$this->success && !empty($this->msg);
    }

    /**
     * @return array|string|int|object|null
     */
    public function getData(): mixed
    {
        return $this->obj;
    }

    public function getMessage(): string
    {
        return $this->msg;
    }
}
