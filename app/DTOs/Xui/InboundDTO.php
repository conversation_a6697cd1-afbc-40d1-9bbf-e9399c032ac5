<?php

namespace App\DTOs\Xui;

use Illuminate\Support\Collection;
use App\DTOs\Xui\ClientStatDTO;
use App\DTOs\Xui\ClientSettingsDTO;
use InboundStreamSettingsDTO;

// to process an inbound like this
/*
{
    "success": true,
    "msg": "",
    "obj": [
        {
            "id": 2,
            "up": 627655488,
            "down": 4146594340,
            "total": 0,
            "remark": "🇷🇺 Russia (SPB)",
            "enable": true,
            "expiryTime": 0,
            "clientStats": [
                {
                    "id": 26,
                    "inboundId": 2,
                    "enable": true,
                    "email": "9y0auvrb",
                    "up": 14303481,
                    "down": 1608719276,
                    "expiryTime": 0,
                    "total": 0,
                    "reset": 0
                },
                {
                    "id": 27,
                    "inboundId": 2,
                    "enable": true,
                    "email": "first@example.com_2",
                    "up": 12960,
                    "down": 7884,
                    "expiryTime": 1754740780000,
                    "total": 107374182400,
                    "reset": 0
                },
                {
                    "id": 28,
                    "inboundId": 2,
                    "enable": true,
                    "email": "second@example.com_2",
                    "up": 0,
                    "down": 0,
                    "expiryTime": 1760097641000,
                    "total": 322122547200,
                    "reset": 0
                },
                {
                    "id": 29,
                    "inboundId": 2,
                    "enable": false,
                    "email": "tg100011@leadteh_2",
                    "up": 0,
                    "down": 0,
                    "expiryTime": 1752181847000,
                    "total": 0,
                    "reset": 0
                },
                {
                    "id": 30,
                    "inboundId": 2,
                    "enable": false,
                    "email": "tg100012@leadteh_2",
                    "up": 0,
                    "down": 0,
                    "expiryTime": 1752183707000,
                    "total": 0,
                    "reset": 0
                },
                {
                    "id": 31,
                    "inboundId": 2,
                    "enable": false,
                    "email": "tg100013@leadteh_2",
                    "up": 0,
                    "down": 0,
                    "expiryTime": 1752184629000,
                    "total": 0,
                    "reset": 0
                },
                {
                    "id": 32,
                    "inboundId": 2,
                    "enable": false,
                    "email": "tg100014@leadteh_2",
                    "up": 0,
                    "down": 0,
                    "expiryTime": 1752184880000,
                    "total": 0,
                    "reset": 0
                }
            ],
            "listen": "",
            "port": 443,
            "protocol": "vless",
            "settings": "{\n  \"clients\": [\n    {\n      \"comment\": \"Server Default\",\n      \"email\": \"9y0auvrb\",\n      \"enable\": true,\n      \"expiryTime\": 0,\n      \"flow\": \"xtls-rprx-vision\",\n      \"id\": \"57ec817a-5c86-453f-b62f-d0c14d3cd07b\",\n      \"limitIp\": 0,\n      \"reset\": 0,\n      \"subId\": \"i3gcvzaiygs1hg7d\",\n      \"tgId\": \"\",\n      \"totalGB\": 0\n    },\n    {\n      \"comment\": \"\",\n      \"email\": \"first@example.com_2\",\n      \"enable\": true,\n      \"expiryTime\": 1754740780000,\n      \"flow\": \"xtls-rprx-vision\",\n      \"id\": \"285ddf0e-3c5d-4f3d-86aa-ad5507e7b94f\",\n      \"limitIp\": 0,\n      \"reset\": 0,\n      \"subId\": \"F174BvI2UaJQCIYP\",\n      \"tgId\": \"32452345\",\n      \"totalGB\": 107374182400\n    },\n    {\n      \"comment\": \"sec\",\n      \"email\": \"second@example.com_2\",\n      \"enable\": false,\n      \"expiryTime\": 1760097641000,\n      \"flow\": \"xtls-rprx-vision\",\n      \"id\": \"0c4051f8-d9fe-438d-a855-da8aa2498786\",\n      \"limitIp\": 0,\n      \"reset\": 0,\n      \"subId\": \"AmxRt6mE9vNun4RM\",\n      \"tgId\": \"53645\",\n      \"totalGB\": 322122547200\n    },\n    {\n      \"comment\": \"test11\",\n      \"email\": \"tg100011@leadteh_2\",\n      \"enable\": true,\n      \"expiryTime\": 1752181847000,\n      \"flow\": \"xtls-rprx-vision\",\n      \"id\": \"47091520-7669-45e4-8b9b-e3e9d72fbe5d\",\n      \"limitIp\": 0,\n      \"reset\": 0,\n      \"subId\": \"4sPz58AzSlJR72bh\",\n      \"tgId\": \"100011\",\n      \"totalGB\": 0\n    },\n    {\n      \"comment\": \"test12\",\n      \"email\": \"tg100012@leadteh_2\",\n      \"enable\": true,\n      \"expiryTime\": 1752183707000,\n      \"flow\": \"xtls-rprx-vision\",\n      \"id\": \"13a62645-54ae-410c-ab1b-7f594c9bf329\",\n      \"limitIp\": 0,\n      \"reset\": 0,\n      \"subId\": \"IADeuu48inz2Jpnb\",\n      \"tgId\": \"100012\",\n      \"totalGB\": 0\n    },\n    {\n      \"comment\": \"test13\",\n      \"email\": \"tg100013@leadteh_2\",\n      \"enable\": true,\n      \"expiryTime\": 1752184629000,\n      \"flow\": \"xtls-rprx-vision\",\n      \"id\": \"8465785e-06ae-4cd0-a5ac-60a847dd9407\",\n      \"limitIp\": 0,\n      \"reset\": 0,\n      \"subId\": \"grYxvSdo9D4uHfwg\",\n      \"tgId\": \"100013\",\n      \"totalGB\": 0\n    },\n    {\n      \"comment\": \"test14\",\n      \"email\": \"tg100014@leadteh_2\",\n      \"enable\": true,\n      \"expiryTime\": 1752184880000,\n      \"flow\": \"xtls-rprx-vision\",\n      \"id\": \"2cc554aa-8274-4f47-be19-9633ed599d2c\",\n      \"limitIp\": 0,\n      \"reset\": 0,\n      \"subId\": \"TmK0XGvnVn4yEvkT\",\n      \"tgId\": \"100014\",\n      \"totalGB\": 0\n    }\n  ],\n  \"decryption\": \"none\",\n  \"fallbacks\": []\n}",
            "streamSettings": "{\n  \"network\": \"tcp\",\n  \"security\": \"reality\",\n  \"externalProxy\": [],\n  \"realitySettings\": {\n    \"show\": false,\n    \"xver\": 0,\n    \"dest\": \"yahoo.com:443\",\n    \"serverNames\": [\n      \"yahoo.com\",\n      \"www.yahoo.com\"\n    ],\n    \"privateKey\": \"4E9QxbvVjhuPtjnt4XR3YGH0CSsXbLQvFipWSJHLpXs\",\n    \"minClient\": \"\",\n    \"maxClient\": \"\",\n    \"maxTimediff\": 0,\n    \"shortIds\": [\n      \"138a\",\n      \"66\",\n      \"8a608f7e1af5da\",\n      \"66e985\",\n      \"dac465ec56\",\n      \"068ea0d7\",\n      \"03946d00d3c0\",\n      \"24592e1cba468e0b\"\n    ],\n    \"settings\": {\n      \"publicKey\": \"UA70SUdtnWh6XuN_l9ZzH8wwfrW6BwfsMpUrn7AahTc\",\n      \"fingerprint\": \"chrome\",\n      \"serverName\": \"\",\n      \"spiderX\": \"/\"\n    }\n  },\n  \"tcpSettings\": {\n    \"acceptProxyProtocol\": false,\n    \"header\": {\n      \"type\": \"none\"\n    }\n  }\n}",
            "tag": "inbound-443",
            "sniffing": "{\n  \"enabled\": false,\n  \"destOverride\": [\n    \"http\",\n    \"tls\",\n    \"quic\",\n    \"fakedns\"\n  ],\n  \"metadataOnly\": false,\n  \"routeOnly\": false\n}",
            "allocate": "{\n  \"strategy\": \"always\",\n  \"refresh\": 5,\n  \"concurrency\": 3\n}"
        },
        {
            "id": 3,
            "up": 898089,
            "down": 723905,
            "total": 0,
            "remark": "Russia 8443",
            "enable": true,
            "expiryTime": 0,
            "clientStats": [
                {
                    "id": 33,
                    "inboundId": 3,
                    "enable": true,
                    "email": "kzd5ljt8",
                    "up": 891386,
                    "down": 723545,
                    "expiryTime": 0,
                    "total": 0,
                    "reset": 0
                },
                {
                    "id": 35,
                    "inboundId": 3,
                    "enable": true,
                    "email": "evx3iwyu",
                    "up": 0,
                    "down": 0,
                    "expiryTime": 0,
                    "total": 0,
                    "reset": 0
                }
            ],
            "listen": "",
            "port": 8443,
            "protocol": "vless",
            "settings": "{\n  \"clients\": [\n    {\n      \"comment\": \"\",\n      \"email\": \"kzd5ljt8\",\n      \"enable\": true,\n      \"expiryTime\": 0,\n      \"flow\": \"\",\n      \"id\": \"f061adb6-d746-4078-af8c-4eed7b1077bf\",\n      \"limitIp\": 0,\n      \"reset\": 0,\n      \"subId\": \"cswaii5l5e587bpg\",\n      \"tgId\": \"\",\n      \"totalGB\": 0\n    },\n    {\n      \"comment\": \"\",\n      \"email\": \"evx3iwyu\",\n      \"enable\": true,\n      \"expiryTime\": 0,\n      \"flow\": \"\",\n      \"id\": \"285ddf0e-3c5d-4f3d-86aa-ad5507e7b94f\",\n      \"limitIp\": 0,\n      \"reset\": 0,\n      \"subId\": \"a0k8a5r38y249n0z\",\n      \"tgId\": \"\",\n      \"totalGB\": 0\n    }\n  ],\n  \"decryption\": \"none\",\n  \"fallbacks\": []\n}",
            "streamSettings": "{\n  \"network\": \"tcp\",\n  \"security\": \"reality\",\n  \"externalProxy\": [],\n  \"realitySettings\": {\n    \"show\": false,\n    \"xver\": 0,\n    \"dest\": \"yahoo.com:443\",\n    \"serverNames\": [\n      \"yahoo.com\",\n      \"www.yahoo.com\"\n    ],\n    \"privateKey\": \"EFIWynkPG_mDCJP69e4yF4A9xM9yyayHYmwO2BZkVG0\",\n    \"minClient\": \"\",\n    \"maxClient\": \"\",\n    \"maxTimediff\": 0,\n    \"shortIds\": [\n      \"94\",\n      \"b903\",\n      \"d1a3b4\",\n      \"fa66586148c1e8\",\n      \"d2b985be150c5c9d\",\n      \"77c04940\",\n      \"4c3bfa3a67e3\",\n      \"b2f94ee8ca\"\n    ],\n    \"settings\": {\n      \"publicKey\": \"otmLWA6Av_o5fn4D_eyoIS6G2d03Zq9voyfax2DE00E\",\n      \"fingerprint\": \"chrome\",\n      \"serverName\": \"\",\n      \"spiderX\": \"/\"\n    }\n  },\n  \"tcpSettings\": {\n    \"acceptProxyProtocol\": false,\n    \"header\": {\n      \"type\": \"none\"\n    }\n  }\n}",
            "tag": "inbound-8443",
            "sniffing": "{\n  \"enabled\": false,\n  \"destOverride\": [\n    \"http\",\n    \"tls\",\n    \"quic\",\n    \"fakedns\"\n  ],\n  \"metadataOnly\": false,\n  \"routeOnly\": false\n}",
            "allocate": "{\n  \"strategy\": \"always\",\n  \"refresh\": 5,\n  \"concurrency\": 3\n}"
        }
    ]
}
*/

class InboundDTO
{
    public int $id; // Inbound ID
    public int $up; // Used up traffic in bytes
    public int $down; // Used down traffic in bytes
    public int $total; // Total Traffic limit in bytes
    public string $remark; // Inbound name (title)
    public int $port; // Port number
    public string $protocol; // Protocol type, like vless, vmess, trojan, shadowsocks
    public bool $enable; // Is inbound enabled
    public array $clientStats; // Array of clients statistics
    public array $settings; // Clients settings array, decoded from json
    public array $streamSettings; // Inbound settings, decoded from json
    public array $sniffing;
    public array $allocate;
    public string $tag;

    public function __construct(array $data)
    {
        $this->id = $data['id'];
        $this->remark = $data['remark'];
        $this->port = $data['port'];
        $this->protocol = $data['protocol'];
        $this->enable = $data['enable'];
        $this->clientStats = $data['clientStats'] ?? [];
        $this->settings = json_decode($data['settings'] ?? '{}', true);
        $this->streamSettings = json_decode($data['streamSettings'] ?? '{}', true);
        $this->sniffing = json_decode($data['sniffing'] ?? '{}', true);
        $this->allocate = json_decode($data['allocate'] ?? '{}', true);
        $this->tag = $data['tag'] ?? '';
    }

    /**
     * Возвращает коллекцию клиентов
     */
    public function clients(): Collection
    {
        return collect($this->clientStats);
    }

    /**
     * Возвращает коллекцию статистики клиентов
     */
    public function clientsStats(): Collection
    {
        return $this->clients()->map(function ($client) {
            return new ClientStatDTO(
                id: $client['id'],
                inboundId: $client['inboundId'],
                enable: $client['enable'],
                email: $client['email'],
                up: $client['up'],
                down: $client['down'],
                expiryTime: $client['expiryTime'],
                total: $client['total'],
                reset: $client['reset']
            );
        });
    }

    /**
     * Возвращает коллекцию активных клиентов
     */
    public function activeClientsStats(): Collection
    {
        return $this->clients()->where('enable', true)->map(function ($client) {
            return new ClientStatDTO(
                id: $client['id'],
                inboundId: $client['inboundId'],
                enable: $client['enable'],
                email: $client['email'],
                up: $client['up'],
                down: $client['down'],
                expiryTime: $client['expiryTime'],
                total: $client['total'],
                reset: $client['reset']
            );
        });
    }

    /**
     * Возвращает коллекцию настроек клиентов
     */
    public function clientsSettings(): Collection
    {
        return collect($this->settings['clients'] ?? [])->map(function ($client) {
            return new ClientSettingsDTO(
                id: $client['id'],
                email: $client['email'],
                enable: $client['enable'],
                expiryTime: $client['expiryTime'],
                flow: $client['flow'],
                limitIp: $client['limitIp'],
                reset: $client['reset'],
                subId: $client['subId'],
                tgId: $client['tgId'],
                totalGB: $client['totalGB']
            );
        });
    }

    /**
     * Возвращает DTO настроек транспорта
     */
    public function getStreamSettings(): InboundStreamSettingsDTO
    {
        return new InboundStreamSettingsDTO(
            network: $this->streamSettings['network'] ?? '',
            security: $this->streamSettings['security'] ?? '',
            externalProxy: $this->streamSettings['externalProxy'] ?? [],
            realitySettings: $this->streamSettings['realitySettings'] ? json_decode($this->streamSettings['realitySettings'], true) : [],
            tcpSettings: $this->streamSettings['tcpSettings'] ? json_decode($this->streamSettings['tcpSettings'], true) : []
        );
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'remark' => $this->remark,
            'port' => $this->port,
            'protocol' => $this->protocol,
            'enable' => $this->enable,
            'clientStats' => $this->clientStats,
            'settings' => $this->settings,
            'streamSettings' => $this->streamSettings,
            'sniffing' => $this->sniffing,
            'allocate' => $this->allocate,
            'tag' => $this->tag,
        ];
    }

}
