<?php

namespace App\DTOs\Xui;

use App\Models\User;

/**
 * A data transfer object for client statistics: 'clientStats' field of InboundDTO
 */

class ClientStatDTO
{
    public function __construct(
        public int $id, // Not to be confused with client uuid. This is not the same as client id, since client id is a UUID string, and this id is an integer client id from the XUI internal database (sqlite).
        public int $inboundId, // Inbound id that client located
        public bool $enable, // Is client enabled
        public string $email, // Email is unique for each client of the server (it does not matter how many inbounds in it)
        public int $up, // Used up traffic in bytes
        public int $down, // Used down traffic in bytes
        public int $expiryTime, // Expiry time in milliseconds
        public int $total, // Total traffic in bytes
        public int $reset // Auto-renewal after expiration. (0 = disable)(unit: day)
    ) {
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'inboundId' => $this->inboundId,
            'enable' => $this->enable,
            'email' => $this->email,
            'up' => $this->up,
            'down' => $this->down,
            'expiryTime' => $this->expiryTime,
            'total' => $this->total,
            'reset' => $this->reset,
        ];
    }

    /**
     * Get the real email from the client email.
     */
    public function getRealEmail(): string
    {
        return User::extractEmailFromClientEmail($this->email);
    }
}
