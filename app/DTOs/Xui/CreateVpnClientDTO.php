<?php

namespace App\DTOs\Xui;

use App\Models\Setting;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class CreateVpnClientDTO
{
    /**
     * A data transfer object for creating a VPN client on the XUI server.
     * The data on which the client will be created
     *
     * @see \alirezax5\XuiApi\Panel\MHSanaei::addNewClient()
     */
    public function __construct(
        public string $uuid,
        public string $email,
        public bool $enable = true,
        public string $flow = '',
        public int $totalGB = 0, // totalgb in bytes
        public int $limitIp = 0,
        public int $expiryTime = 0, // eT in milliseconds
        public string $fingerprint = 'chrome',
        public string $tgId = '', // telegram id
        public string $subId = '',
        public bool $isTrojan = false
    ) {
    }

    public function toArray(): array
    {
        return [
            'id' => $this->uuid,
            'email' => $this->email,
            'enable' => $this->enable,
            'flow' => $this->flow,
            'totalGB' => $this->totalGB,
            'expiryTime' => $this->expiryTime,
            'limitIp' => $this->limitIp,
            'fingerprint' => $this->fingerprint,
            'tgId' => $this->tgId,
            'subId' => $this->subId,
            'isTrojan' => $this->isTrojan,
        ];
    }

    public static function makeDtoFromUserAndInbound(User $user, InboundDTO $inbound): self
    {
        return new self(
            uuid: $user->getClientId(),
            email: $user->generateClientEmailForInbound($inbound->id),
            enable: true,
            flow: Setting::get('default_flow') ?? 'xtls-rprx-vision',
            totalGB: $user->subscriptionPlan->getTotalGB(),
            limitIp: $user->subscriptionPlan->getLimitIp(),
            expiryTime: $user->currentSubscription->getExpiryTimeMs(),
            fingerprint: $inbound->realitySettings->settings->fingerprint ?? 'chrome',
            tgId: $user->tg_id ?? '',
            subId: bin2hex(random_bytes(8)),
            isTrojan: false
        );
    }

}
