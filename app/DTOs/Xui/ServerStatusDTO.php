<?php

namespace App\DTOs\Xui;

/*
{
    "success": true,
    "msg": "",
    "obj": {
        "cpu": 1.492537315268439,
        "cpuCores": 1,
        "logicalPro": 1,
        "cpuSpeedMhz": 2399.998,
        "mem": {
            "current": 169168896,
            "total": 476143616
        },
        "swap": {
            "current": 0,
            "total": 0
        },
        "disk": {
            "current": 4529434624,
            "total": 21027491840
        },
        "xray": {
            "state": "running",
            "errorMsg": "",
            "version": "25.6.8"
        },
        "uptime": 715138,
        "loads": [
            0.06,
            0.02,
            0
        ],
        "tcpCount": 43,
        "udpCount": 6,
        "netIO": {
            "up": 2396,
            "down": 2340
        },
        "netTraffic": {
            "sent": 5991692892,
            "recv": 6677040916
        },
        "publicIP": {
            "ipv4": "***********",
            "ipv6": "N/A"
        },
        "appStats": {
            "threads": 15,
            "mem": 75519240,
            "uptime": 179
        }
    }
}
*/

class ServerStatusDTO
{
    public function __construct(
        public float $cpu,
        public int $cpuCores,
        public int $logicalPro,
        public float $cpuSpeedMhz,
        public array $mem,
        public array $swap,
        public array $disk,
        public array $xray,
        public int $uptime,
        public array $loads,
        public int $tcpCount,
        public int $udpCount,
        public array $netIO,
        public array $netTraffic,
        public array $publicIP,
        public array $appStats,
    ) {
    }

    public function toArray(): array
    {
        return [
            'cpu' => $this->cpu,
            'cpuCores' => $this->cpuCores,
            'logicalPro' => $this->logicalPro,
            'cpuSpeedMhz' => $this->cpuSpeedMhz,
            'mem' => $this->mem,
            'swap' => $this->swap,
            'disk' => $this->disk,
            'xray' => $this->xray,
            'uptime' => $this->uptime,
            'loads' => $this->loads,
            'tcpCount' => $this->tcpCount,
            'udpCount' => $this->udpCount,
            'netIO' => $this->netIO,
            'netTraffic' => $this->netTraffic,
            'publicIP' => $this->publicIP,
            'appStats' => $this->appStats,
        ];
    }
}
