<?php

namespace App\DTOs\Xui;

class UpdateVpnClientOnlyFieldsDTO
{
    /**
     * Разрешённые поля для обновления в XUI API.
     */
    private const ALLOWED_FIELDS = [
        'enable',
        'id',
        'email',
        'flow',
        'totalGB',
        'expiryTime',
        'limitIp',
        'fingerprint',
        'tgId',
        'subId',
    ];

    public function __construct(
        public readonly array $fields
    ) {
        $this->fields = $this->filterAllowedFields($fields);
    }

    private function filterAllowedFields(array $input): array
    {
        return array_intersect_key($input, array_flip(self::ALLOWED_FIELDS));
    }

    public function toArray(): array
    {
        return $this->fields;
    }
}
