<?php

namespace App\DTOs\Xui;

use App\Models\Setting;
use App\Models\User;

class UpdateVpnClientDTO
{
    /**
     * A data transfer object for updating a VPN client on the XUI server.
     * The data to be changed on the client
     *
     * @see \alirezax5\XuiApi\Panel\MHSanaei::editClient()
     */
    public function __construct(
        public string $uuid,
        public string $email,
        public bool $enable = true,
        public string $flow = '',
        public int $totalGB = 0, // totalgb in bytes
        public int $limitIp = 0,
        public int $expiryTime = 0, // eT in milliseconds
        public string $fingerprint = 'chrome',
        public string $tgId = '', // telegram id
        public string $subId = '',
        public bool $isTrojan = false
    ) {
    }

    public function toArray(): array
    {
        return [
            'id' => $this->uuid,
            'email' => $this->email,
            'enable' => $this->enable,
            'flow' => $this->flow,
            'totalGB' => $this->totalGB,
            'expiryTime' => $this->expiryTime,
            'limitIp' => $this->limitIp,
            'fingerprint' => $this->fingerprint,
            'tgId' => $this->tgId,
            'subId' => $this->subId,
            'isTrojan' => $this->isTrojan,
        ];
    }

    public static function makeDtoFromUserAndInbound(User $user, InboundDTO $inbound): UpdateVpnClientDTO
    {
        return new self(
            uuid: $user->getClientId(),
            email: $user->generateClientEmailForInbound($inbound->id),
            enable: true, // TODO: $user->isEnabled(),
            flow: Setting::get('default_flow') ?? 'xtls-rprx-vision',
            totalGB: 5555555555, // TODO: $user->plan->traffic_limit ?? 0,
            limitIp: $user->plan->limit_ip ?? 0,
            expiryTime: now()->addDays(30)->getTimestampMs(), // TODO: calculate from subscription end date
            fingerprint: $inbound->realitySettings->settings->fingerprint ?? 'chrome',
            tgId: $user->tg_id ?? '',
            subId: bin2hex(random_bytes(8)),
            isTrojan: false,
        );
    }
}
