<?php

use App\Models\OrderItem;

class VpnSubscriptionOrderItemDTO
{
    public function __construct(
        public readonly string $itemType = 'subscription_plan',
        public readonly int $subscriptionPlanId, // ID of the subscription plan
        public readonly ?string $action = OrderItem::ACTION_NEW, // full list in OrderItem::ACTION_TYPES
        public readonly int $quantity = 1,
    ) {
        if (!in_array($this->action, OrderItem::ACTION_TYPES, true)) {
            throw new \InvalidArgumentException('Invalid action');
        }
    }

    public function toArray(): array
    {
        return [
            'item_type' => $this->itemType,
            'subscription_plan_id' => $this->subscriptionPlanId,
            'action' => $this->action,
            'quantity' => $this->quantity,
        ];
    }
}
