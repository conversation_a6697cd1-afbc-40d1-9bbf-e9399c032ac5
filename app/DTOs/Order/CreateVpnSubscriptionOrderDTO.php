<?

use App\Models\User;

class CreateVpnSubscriptionOrderDTO
{
    public function __construct(
        public readonly User $user,
        public readonly string $currency,
        public readonly int $totalAmount,
        public readonly array $items, // массив из VpnSubscriptionOrderItemDTO
        public readonly string $status = 'new',
        public readonly ?string $notes = null,
        public readonly ?string $adminNotes = null,
    ) {
        if (empty($items)) {
            throw new \InvalidArgumentException('Items cannot be empty');
        }

        foreach ($items as $item) {
            if (! $item instanceof VpnSubscriptionOrderItemDTO) {
                throw new \InvalidArgumentException('Item must be an instance of VpnSubscriptionOrderItemDTO');
            }
        }
    }

    public function toArray(): array
    {
        return [
            'user_id' => $this->user->id,
            'currency' => $this->currency,
            'total_amount' => $this->totalAmount,
            'items' => array_map(fn ($item) => $item->toArray(), $this->items),
            'status' => $this->status,
            'notes' => $this->notes,
            'admin_notes' => $this->adminNotes,
        ];
    }

    public function getItemsTotalAmount(): int
    {
        return array_reduce($this->items, fn ($carry, $item) => $carry + $item->price * $item->quantity, 0);
    }
}
