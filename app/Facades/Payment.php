<?php

namespace App\Facades;

use App\Services\Payment\PaymentService;
use Illuminate\Support\Facades\Facade;

/**
 * @method static \App\Services\Payment\DTOs\PaymentResponse createPayment(\App\Services\Payment\DTOs\PaymentRequest $request, ?string $gatewayName = null)
 * @method static \App\Services\Payment\DTOs\PaymentStatusResponse getPaymentStatus(string $paymentId, ?string $gatewayName = null)
 * @method static \App\Services\Payment\DTOs\PaymentResponse cancelPayment(string $paymentId, ?string $gatewayName = null)
 * @method static \App\Services\Payment\DTOs\RefundResponse refundPayment(\App\Services\Payment\DTOs\RefundRequest $request, ?string $gatewayName = null)
 * @method static \App\Services\Payment\DTOs\PaymentResponse processRecurringPayment(\App\Services\Payment\DTOs\RecurringPaymentRequest $request, ?string $gatewayName = null)
 * @method static array getAvailablePaymentMethods()
 * @method static array getPaymentFormData(\App\Models\Payment $payment)
 * @method static ?string getPaymentUrl(\App\Models\Payment $payment)
 *
 * @see \App\Services\Payment\PaymentService
 */
class Payment extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return PaymentService::class;
    }
}
