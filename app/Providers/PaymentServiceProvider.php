<?php

namespace App\Providers;

use App\Services\Payment\PaymentService;
use App\Services\Payment\PaymentGatewayManager;
use App\Services\Payment\WebhookService;
use App\Services\Payment\PaymentLogger;
use App\Services\Payment\Gateways\ManualGateway;
use App\Services\Payment\Gateways\CashGateway;
use App\Services\Payment\Gateways\FreeGateway;
use App\Services\Payment\Gateways\TBankGateway;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Log;

class PaymentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register the payment logger
        $this->app->singleton(PaymentLogger::class, function () {
            return new PaymentLogger();
        });

        // Register the payment gateway manager
        $this->app->singleton(PaymentGatewayManager::class, function () {
            return new PaymentGatewayManager();
        });

        // Register the webhook service
        $this->app->singleton(WebhookService::class, function ($app) {
            return new WebhookService($app->make(PaymentGatewayManager::class));
        });

        // Register the main payment service
        $this->app->singleton(PaymentService::class, function ($app) {
            return new PaymentService($app->make(PaymentGatewayManager::class));
        });

        // Register individual gateways
        $this->registerGateways();

        // Register aliases
        $this->app->alias(PaymentService::class, 'payment.service');
        $this->app->alias(PaymentGatewayManager::class, 'payment.gateway.manager');
        $this->app->alias(WebhookService::class, 'payment.webhook.service');
        $this->app->alias(PaymentLogger::class, 'payment.logger');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration files
        $this->publishes([
            __DIR__.'/../../config/payments.php' => config_path('payments.php'),
            __DIR__.'/../../config/tbank.php' => config_path('tbank.php'),
        ], 'payment-config');

        // Register console commands
        if ($this->app->runningInConsole()) {
            $this->registerCommands();
        }

        // Log service provider boot
        Log::debug('Payment service provider booted', [
            'gateways_available' => $this->getAvailableGateways(),
            'default_gateway' => config('payments.default'),
        ]);
    }

    /**
     * Register individual payment gateways.
     */
    private function registerGateways(): void
    {
        // Manual Gateway
        $this->app->bind('payment.gateway.manual', function () {
            $gateway = new ManualGateway();
            $config = config('payments.gateways.manual', []);
            $gateway->initialize($config);
            return $gateway;
        });

        // Cash Gateway
        $this->app->bind('payment.gateway.cash', function () {
            $gateway = new CashGateway();
            $config = config('payments.gateways.cash', []);
            $gateway->initialize($config);
            return $gateway;
        });

        // Free Gateway
        $this->app->bind('payment.gateway.free', function () {
            $gateway = new FreeGateway();
            $config = config('payments.gateways.free', []);
            $gateway->initialize($config);
            return $gateway;
        });

        // T-Bank Gateway
        $this->app->bind('payment.gateway.tbank', function () {
            $gateway = new TBankGateway();
            $config = config('payments.gateways.tbank', []);
            $gateway->initialize($config);
            return $gateway;
        });
    }

    /**
     * Register console commands.
     */
    private function registerCommands(): void
    {
        $this->commands([
            \App\Console\Commands\PaymentTestCommand::class,
            \App\Console\Commands\TestProxyConfigCommand::class,
        ]);
    }

    /**
     * Get available gateways from configuration.
     */
    private function getAvailableGateways(): array
    {
        return array_keys(config('payments.gateways', []));
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            PaymentService::class,
            PaymentGatewayManager::class,
            WebhookService::class,
            PaymentLogger::class,
            'payment.service',
            'payment.gateway.manager',
            'payment.webhook.service',
            'payment.logger',
            'payment.gateway.manual',
            'payment.gateway.cash',
            'payment.gateway.free',
            'payment.gateway.tbank',
        ];
    }
}
