<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\ServiceProvider;
use App\Support\ProxyHelper;

class ProxyServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->configureRateLimiting();
    }

    /**
     * Configure rate limiting for the application.
     */
    protected function configureRateLimiting(): void
    {
        // Webhook rate limiter - more permissive for legitimate webhook sources
        RateLimiter::for('webhook', function (Request $request) {
            $clientIp = $request->ip();

            // Check if request is from a trusted webhook source
            if ($this->isFromTrustedWebhookSource($request)) {
                // Allow more requests from trusted sources (e.g., payment gateways)
                return Limit::perMinute(100)->by($clientIp);
            }

            // Stricter limits for unknown sources
            return Limit::perMinute(10)->by($clientIp);
        });

        // API rate limiter with real IP consideration
        RateLimiter::for('api', function (Request $request) {
            $identifier = $request->user()?->id ?: $request->ip();

            // Use real client IP for rate limiting if configured
            if (config('proxy.security.rate_limit_by_real_ip', true)) {
                $identifier = $request->ip();
            }

            return Limit::perMinute(60)->by($identifier);
        });

        // Strict rate limiter for sensitive endpoints
        RateLimiter::for('sensitive', function (Request $request) {
            return Limit::perMinute(5)->by($request->ip());
        });

        // Global rate limiter
        RateLimiter::for('global', function (Request $request) {
            return Limit::perMinute(1000)->by($request->ip());
        });
    }

    /**
     * Check if request is from a trusted webhook source.
     */
    private function isFromTrustedWebhookSource(Request $request): bool
    {
        return ProxyHelper::isFromTrustedWebhookSource($request);
    }
}
