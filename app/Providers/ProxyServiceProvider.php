<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\ServiceProvider;

class ProxyServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->configureRateLimiting();
    }

    /**
     * Configure rate limiting for the application.
     */
    protected function configureRateLimiting(): void
    {
        // Webhook rate limiter - more permissive for legitimate webhook sources
        RateLimiter::for('webhook', function (Request $request) {
            $clientIp = $request->ip();
            
            // Check if request is from a trusted webhook source
            if ($this->isFromTrustedWebhookSource($request)) {
                // Allow more requests from trusted sources (e.g., payment gateways)
                return Limit::perMinute(100)->by($clientIp);
            }
            
            // Stricter limits for unknown sources
            return Limit::perMinute(10)->by($clientIp);
        });

        // API rate limiter with real IP consideration
        RateLimiter::for('api', function (Request $request) {
            $identifier = $request->user()?->id ?: $request->ip();
            
            // Use real client IP for rate limiting if configured
            if (config('proxy.security.rate_limit_by_real_ip', true)) {
                $identifier = $request->ip();
            }
            
            return Limit::perMinute(60)->by($identifier);
        });

        // Strict rate limiter for sensitive endpoints
        RateLimiter::for('sensitive', function (Request $request) {
            return Limit::perMinute(5)->by($request->ip());
        });

        // Global rate limiter
        RateLimiter::for('global', function (Request $request) {
            return Limit::perMinute(1000)->by($request->ip());
        });
    }

    /**
     * Check if request is from a trusted webhook source.
     */
    private function isFromTrustedWebhookSource(Request $request): bool
    {
        $clientIp = $request->ip();
        
        // Check against webhook-specific trusted IPs
        $webhookIps = config('proxy.webhook.trusted_ips', []);
        foreach ($webhookIps as $ip) {
            if ($this->ipInRange($clientIp, $ip)) {
                return true;
            }
        }

        // Check User-Agent for known payment gateways
        $userAgent = $request->userAgent();
        $trustedUserAgents = [
            'T-Bank',
            'Tinkoff',
            'PayPal',
            'Stripe',
            'Square',
        ];

        foreach ($trustedUserAgents as $trustedAgent) {
            if ($userAgent && str_contains($userAgent, $trustedAgent)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if IP is in range (supports CIDR notation).
     */
    private function ipInRange(string $ip, string $range): bool
    {
        if ($range === '*') {
            return true;
        }

        if (!str_contains($range, '/')) {
            return $ip === $range;
        }

        [$subnet, $mask] = explode('/', $range);
        
        if (!filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) ||
            !filter_var($subnet, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return false;
        }

        $ipLong = ip2long($ip);
        $subnetLong = ip2long($subnet);
        $maskLong = -1 << (32 - (int) $mask);

        return ($ipLong & $maskLong) === ($subnetLong & $maskLong);
    }
}
