<?php

namespace App\Support;

use Illuminate\Http\Request;

class ProxyHelper
{
    /**
     * Get trusted proxy IP addresses based on environment and configuration.
     */
    public static function getTrustedProxies(): array|string|null
    {
        $environment = app()->environment();
        
        // Check if we should trust all proxies for this environment
        if (config("proxy.environments.{$environment}.trust_all_proxies", false)) {
            return '*';
        }
        
        // Get base trusted proxies
        $proxies = config('proxy.trusted_proxies.ips', []);
        
        // Add environment-specific proxies
        $envProxies = config("proxy.environments.{$environment}.additional_proxies", []);
        if (!empty($envProxies)) {
            $proxies = array_merge($proxies, $envProxies);
        }
        
        // Add webhook-specific IPs if this is a webhook request
        if (request() && str_contains(request()->path(), 'webhook')) {
            $webhookIps = config('proxy.webhook.trusted_ips', []);
            $proxies = array_merge($proxies, $webhookIps);
        }
        
        return array_filter($proxies);
    }

    /**
     * Get trusted proxy headers configuration.
     */
    public static function getTrustedProxyHeaders(): int
    {
        $headerConfig = config('proxy.trusted_proxies.headers', 'HEADER_X_FORWARDED_ALL');
        
        // Map string configuration to constants
        return match ($headerConfig) {
            'HEADER_X_FORWARDED_ALL' => Request::HEADER_X_FORWARDED_FOR |
                                       Request::HEADER_X_FORWARDED_HOST |
                                       Request::HEADER_X_FORWARDED_PORT |
                                       Request::HEADER_X_FORWARDED_PROTO |
                                       Request::HEADER_X_FORWARDED_PREFIX |
                                       Request::HEADER_X_FORWARDED_AWS_ELB,
            'HEADER_X_FORWARDED_AWS_ELB' => Request::HEADER_X_FORWARDED_AWS_ELB,
            'HEADER_FORWARDED' => Request::HEADER_FORWARDED,
            default => Request::HEADER_X_FORWARDED_FOR |
                      Request::HEADER_X_FORWARDED_HOST |
                      Request::HEADER_X_FORWARDED_PORT |
                      Request::HEADER_X_FORWARDED_PROTO,
        };
    }

    /**
     * Get trusted host names based on environment and configuration.
     */
    public static function getTrustedHosts(): array|callable
    {
        return function () {
            $environment = app()->environment();
            
            // Get base trusted hosts
            $hosts = config('proxy.trusted_hosts.hosts', []);
            
            // Add environment-specific hosts
            $envHosts = config("proxy.environments.{$environment}.additional_hosts", []);
            if (!empty($envHosts)) {
                $hosts = array_merge($hosts, $envHosts);
            }
            
            // Add current APP_URL host if not already included
            $appUrl = config('app.url');
            if ($appUrl) {
                $appHost = parse_url($appUrl, PHP_URL_HOST);
                if ($appHost && !in_array($appHost, $hosts)) {
                    $hosts[] = $appHost;
                }
            }
            
            return array_filter($hosts);
        };
    }

    /**
     * Check if an IP address is in a given range (supports CIDR notation).
     */
    public static function ipInRange(string $ip, string $range): bool
    {
        if ($range === '*') {
            return true;
        }

        if (!str_contains($range, '/')) {
            return $ip === $range;
        }

        [$subnet, $mask] = explode('/', $range);
        
        if (!filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) ||
            !filter_var($subnet, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return false;
        }

        $ipLong = ip2long($ip);
        $subnetLong = ip2long($subnet);
        $maskLong = -1 << (32 - (int) $mask);

        return ($ipLong & $maskLong) === ($subnetLong & $maskLong);
    }

    /**
     * Check if an IP is trusted based on proxy configuration.
     */
    public static function isIpTrusted(string $ip): bool
    {
        $trustedProxies = static::getTrustedProxies();
        
        if ($trustedProxies === '*') {
            return true;
        }

        if (!is_array($trustedProxies)) {
            return false;
        }

        foreach ($trustedProxies as $proxy) {
            if (static::ipInRange($ip, $proxy)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if a host is trusted based on configuration.
     */
    public static function isHostTrusted(string $host): bool
    {
        $trustedHosts = static::getTrustedHosts();
        if (is_callable($trustedHosts)) {
            $trustedHosts = $trustedHosts();
        }

        return in_array($host, $trustedHosts);
    }

    /**
     * Check if request is from a trusted webhook source.
     */
    public static function isFromTrustedWebhookSource(Request $request): bool
    {
        $clientIp = $request->ip();
        
        // Check against webhook-specific trusted IPs
        $webhookIps = config('proxy.webhook.trusted_ips', []);
        foreach ($webhookIps as $ip) {
            if (static::ipInRange($clientIp, $ip)) {
                return true;
            }
        }

        // Check User-Agent for known payment gateways
        $userAgent = $request->userAgent();
        $trustedUserAgents = [
            'T-Bank',
            'Tinkoff',
            'PayPal',
            'Stripe',
            'Square',
        ];

        foreach ($trustedUserAgents as $trustedAgent) {
            if ($userAgent && str_contains($userAgent, $trustedAgent)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get proxy information for debugging.
     */
    public static function getProxyDebugInfo(Request $request): array
    {
        return [
            'client_ip' => $request->ip(),
            'is_secure' => $request->isSecure(),
            'host' => $request->header('Host'),
            'method' => $request->method(),
            'url' => $request->url(),
            'full_url' => $request->fullUrl(),
            'proxy_headers' => [
                'X-Forwarded-For' => $request->header('X-Forwarded-For'),
                'X-Forwarded-Host' => $request->header('X-Forwarded-Host'),
                'X-Forwarded-Port' => $request->header('X-Forwarded-Port'),
                'X-Forwarded-Proto' => $request->header('X-Forwarded-Proto'),
                'X-Real-IP' => $request->header('X-Real-IP'),
                'CF-Connecting-IP' => $request->header('CF-Connecting-IP'),
                'Forwarded' => $request->header('Forwarded'),
            ],
            'server_info' => [
                'server_name' => $_SERVER['SERVER_NAME'] ?? 'unknown',
                'server_addr' => $_SERVER['SERVER_ADDR'] ?? 'unknown',
                'server_port' => $_SERVER['SERVER_PORT'] ?? 'unknown',
                'https' => $_SERVER['HTTPS'] ?? 'off',
                'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            ],
            'configuration' => [
                'trusted_proxies' => static::getTrustedProxies(),
                'trusted_hosts' => is_callable(static::getTrustedHosts()) ? static::getTrustedHosts()() : static::getTrustedHosts(),
                'environment' => app()->environment(),
            ],
        ];
    }

    /**
     * Get header names from bitmask for debugging.
     */
    public static function getHeaderNames(int $headers): array
    {
        $names = [];

        if ($headers & Request::HEADER_X_FORWARDED_FOR) {
            $names[] = 'X-Forwarded-For';
        }
        if ($headers & Request::HEADER_X_FORWARDED_HOST) {
            $names[] = 'X-Forwarded-Host';
        }
        if ($headers & Request::HEADER_X_FORWARDED_PORT) {
            $names[] = 'X-Forwarded-Port';
        }
        if ($headers & Request::HEADER_X_FORWARDED_PROTO) {
            $names[] = 'X-Forwarded-Proto';
        }
        if ($headers & Request::HEADER_X_FORWARDED_PREFIX) {
            $names[] = 'X-Forwarded-Prefix';
        }
        if ($headers & Request::HEADER_X_FORWARDED_AWS_ELB) {
            $names[] = 'X-Forwarded-AWS-ELB';
        }
        if ($headers & Request::HEADER_FORWARDED) {
            $names[] = 'Forwarded';
        }

        return $names;
    }
}
