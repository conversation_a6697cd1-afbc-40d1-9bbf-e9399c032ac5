<?php

/**
 * Global helper functions for proxy configuration.
 * 
 * These functions provide backward compatibility and convenience
 * for accessing proxy configuration methods.
 */

use App\Support\ProxyHelper;

if (!function_exists('getTrustedProxies')) {
    /**
     * Get trusted proxy IP addresses based on environment and configuration.
     */
    function getTrustedProxies(): array|string|null
    {
        return ProxyHelper::getTrustedProxies();
    }
}

if (!function_exists('getTrustedProxyHeaders')) {
    /**
     * Get trusted proxy headers configuration.
     */
    function getTrustedProxyHeaders(): int
    {
        return ProxyHelper::getTrustedProxyHeaders();
    }
}

if (!function_exists('getTrustedHosts')) {
    /**
     * Get trusted host names based on environment and configuration.
     */
    function getTrustedHosts(): array|callable
    {
        return ProxyHelper::getTrustedHosts();
    }
}

if (!function_exists('isIpTrusted')) {
    /**
     * Check if an IP address is trusted based on proxy configuration.
     */
    function isIpTrusted(string $ip): bool
    {
        return ProxyHelper::isIpTrusted($ip);
    }
}

if (!function_exists('isHostTrusted')) {
    /**
     * Check if a host is trusted based on configuration.
     */
    function isHostTrusted(string $host): bool
    {
        return ProxyHelper::isHostTrusted($host);
    }
}

if (!function_exists('ipInRange')) {
    /**
     * Check if an IP address is in a given range (supports CIDR notation).
     */
    function ipInRange(string $ip, string $range): bool
    {
        return ProxyHelper::ipInRange($ip, $range);
    }
}

if (!function_exists('getProxyDebugInfo')) {
    /**
     * Get proxy information for debugging.
     */
    function getProxyDebugInfo(\Illuminate\Http\Request $request): array
    {
        return ProxyHelper::getProxyDebugInfo($request);
    }
}

if (!function_exists('isFromTrustedWebhookSource')) {
    /**
     * Check if request is from a trusted webhook source.
     */
    function isFromTrustedWebhookSource(\Illuminate\Http\Request $request): bool
    {
        return ProxyHelper::isFromTrustedWebhookSource($request);
    }
}
