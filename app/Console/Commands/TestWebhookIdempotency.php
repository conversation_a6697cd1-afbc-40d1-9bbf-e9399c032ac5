<?php

namespace App\Console\Commands;

use App\Models\Payment;
use App\Models\PaymentWebhook;
use App\Services\PaymentService;
use Illuminate\Console\Command;

class TestWebhookIdempotency extends Command
{
    protected $signature = 'webhook:test-idempotency {payment_id} {--count=3}';
    protected $description = 'Test webhook idempotency by sending multiple identical webhooks';

    public function handle(PaymentService $paymentService): int
    {
        $paymentId = $this->argument('payment_id');
        $count = (int) $this->option('count');

        $this->info("Testing webhook idempotency for payment ID: {$paymentId}");
        $this->info("Will send {$count} identical webhooks");

        // Find payment to get current status
        $payment = Payment::where('external_payment_id', $paymentId)->first();
        
        if (!$payment) {
            $this->error("Payment not found with external_payment_id: {$paymentId}");
            return 1;
        }

        $this->info("Payment found: {$payment->public_id}");
        $this->info("Current status: {$payment->status}");

        // Count existing webhooks
        $initialWebhookCount = PaymentWebhook::where('external_payment_id', $paymentId)->count();
        $this->info("Initial webhook count: {$initialWebhookCount}");

        // Create test webhook data
        $webhookData = [
            'TerminalKey' => '1726392591291DEMO',
            'OrderId' => $payment->order->public_id ?? 'TEST-ORDER',
            'Success' => true,
            'Status' => 'CONFIRMED',
            'PaymentId' => (int) $paymentId,
            'ErrorCode' => '0',
            'Amount' => $payment->amount ?? 100,
            'CardId' => *********,
            'Pan' => '430000******0777',
            'ExpDate' => '1230',
            'Token' => 'test_token_' . time(),
        ];

        $this->info("\nSending {$count} identical webhooks...");

        $results = [];
        for ($i = 1; $i <= $count; $i++) {
            $this->info("Sending webhook #{$i}...");
            
            $result = $paymentService->handleWebhook('tbank', $webhookData, []);
            $results[] = $result;
            
            $this->info($result ? "✅ Webhook #{$i} processed successfully" : "❌ Webhook #{$i} failed");
            
            // Small delay to ensure different timestamps
            usleep(100000); // 0.1 seconds
        }

        // Check results
        $this->info("\n=== RESULTS ===");
        
        // Reload payment to check status
        $payment->refresh();
        $this->info("Final payment status: {$payment->status}");
        
        // Count final webhooks
        $finalWebhookCount = PaymentWebhook::where('external_payment_id', $paymentId)->count();
        $this->info("Final webhook count: {$finalWebhookCount}");
        $this->info("New webhooks logged: " . ($finalWebhookCount - $initialWebhookCount));

        // Check if all webhooks were processed successfully
        $successCount = array_sum($results);
        $this->info("Successful webhook processing: {$successCount}/{$count}");

        // Verify idempotency
        if ($successCount === $count) {
            $this->info("✅ All webhooks processed successfully");
        } else {
            $this->warn("⚠️ Some webhooks failed processing");
        }

        if ($finalWebhookCount - $initialWebhookCount === $count) {
            $this->info("✅ All webhooks logged to database");
        } else {
            $this->warn("⚠️ Not all webhooks were logged");
        }

        // Show recent webhook logs
        $this->info("\n=== RECENT WEBHOOK LOGS ===");
        $recentWebhooks = PaymentWebhook::where('external_payment_id', $paymentId)
            ->latest()
            ->take($count)
            ->get();

        foreach ($recentWebhooks as $webhook) {
            $this->info("Webhook ID: {$webhook->id}, Status: {$webhook->webhook_status}, Created: {$webhook->created_at}");
        }

        return 0;
    }
}
