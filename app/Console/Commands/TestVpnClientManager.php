<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Services\VpnClientManager;
use Illuminate\Console\Command;

class TestVpnClientManager extends Command
{
    protected $signature = 'test:vpn-client-manager {user_id}';
    protected $description = 'Test VpnClientManager functionality';

    public function handle(): int
    {
        $userId = $this->argument('user_id');

        try {
            // Find user
            $user = User::findOrFail($userId);
            $this->info("Testing VpnClientManager for user: {$user->name} ({$user->id})");

            // Check user's server pools and servers
            $this->info("\n📊 User's Server Configuration:");

            $pools = $user->serverPools()
                ->where('server_pools.is_active', true)
                ->wherePivotNull('released_at')
                ->get();

            $this->info("Active pools: {$pools->count()}");

            $totalServers = 0;
            foreach ($pools as $pool) {
                $activeServers = $pool->servers()
                    ->where('xui_servers.is_active', true)
                    ->wherePivotNull('released_at')
                    ->get();

                $totalServers += $activeServers->count();

                $this->info("- Pool: {$pool->name} (ID: {$pool->id})");
                $this->info("  Active servers: {$activeServers->count()}");

                foreach ($activeServers as $server) {
                    $this->info("    - Server: {$server->name} (ID: {$server->id})");
                }
            }

            $this->info("Total active servers: {$totalServers}");

            // Test VpnClientManager
            $this->info("\n🔧 Testing VpnClientManager:");

            $vpnManager = new VpnClientManager($user);

            // Get xuiServerManagers property via reflection
            $reflection = new \ReflectionClass($vpnManager);
            $property = $reflection->getProperty('xuiServerManagers');
            $property->setAccessible(true);
            $managers = $property->getValue($vpnManager);

            $this->info("VpnClientManager created successfully");
            $this->info("Server managers count: " . count($managers));

            foreach ($managers as $serverId => $manager) {
                $this->info("- Server manager for server ID: {$serverId}");
            }

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Test failed: {$e->getMessage()}");
            $this->error("Stack trace: " . $e->getTraceAsString());
            return 1;
        }
    }
}
