<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class TestProxyConfigCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'proxy:test 
                            {--show-config : Show current proxy configuration}
                            {--test-ip=127.0.0.1 : IP address to test}
                            {--test-host=localhost : Host to test}
                            {--simulate-webhook : Simulate webhook request}';

    /**
     * The console command description.
     */
    protected $description = 'Test and validate proxy configuration';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Proxy Configuration Test Tool');
        $this->line('');

        if ($this->option('show-config')) {
            $this->showConfiguration();
            return 0;
        }

        $this->testConfiguration();
        
        if ($this->option('simulate-webhook')) {
            $this->simulateWebhookRequest();
        }

        return 0;
    }

    /**
     * Show current proxy configuration.
     */
    private function showConfiguration(): void
    {
        $this->info('Current Proxy Configuration:');
        $this->line('');

        // Trusted Proxies
        $this->comment('Trusted Proxies:');
        $proxies = getTrustedProxies();
        if ($proxies === '*') {
            $this->line('  - All proxies trusted (*)');
        } elseif (is_array($proxies)) {
            foreach ($proxies as $proxy) {
                $this->line("  - {$proxy}");
            }
        } else {
            $this->line('  - None configured');
        }

        $this->line('');

        // Trusted Headers
        $this->comment('Trusted Headers:');
        $headers = getTrustedProxyHeaders();
        $headerNames = $this->getHeaderNames($headers);
        foreach ($headerNames as $headerName) {
            $this->line("  - {$headerName}");
        }

        $this->line('');

        // Trusted Hosts
        $this->comment('Trusted Hosts:');
        $hosts = getTrustedHosts();
        if (is_callable($hosts)) {
            $hosts = $hosts();
        }
        foreach ($hosts as $host) {
            $this->line("  - {$host}");
        }

        $this->line('');

        // Environment Settings
        $this->comment('Environment Settings:');
        $environment = app()->environment();
        $this->line("  - Environment: {$environment}");
        $this->line("  - Trust all proxies: " . (config("proxy.environments.{$environment}.trust_all_proxies", false) ? 'Yes' : 'No'));
        $this->line("  - Allow subdomains: " . (config('proxy.trusted_hosts.allow_subdomains', true) ? 'Yes' : 'No'));
        $this->line("  - Strict mode: " . (config('proxy.security.strict_mode', false) ? 'Yes' : 'No'));
        $this->line("  - Log suspicious requests: " . (config('proxy.security.log_suspicious_requests', true) ? 'Yes' : 'No'));
    }

    /**
     * Test proxy configuration.
     */
    private function testConfiguration(): void
    {
        $this->info('Testing Proxy Configuration:');
        $this->line('');

        $testIp = $this->option('test-ip');
        $testHost = $this->option('test-host');

        // Test IP trust
        $this->comment("Testing IP trust for: {$testIp}");
        $isTrusted = $this->isIpTrusted($testIp);
        $this->line($isTrusted ? '  ✓ IP is trusted' : '  ✗ IP is not trusted');

        $this->line('');

        // Test host trust
        $this->comment("Testing host trust for: {$testHost}");
        $isHostTrusted = $this->isHostTrusted($testHost);
        $this->line($isHostTrusted ? '  ✓ Host is trusted' : '  ✗ Host is not trusted');

        $this->line('');

        // Test webhook IPs
        $this->comment('Testing webhook IP ranges:');
        $webhookIps = config('proxy.webhook.trusted_ips', []);
        if (empty($webhookIps)) {
            $this->line('  - No webhook IPs configured');
        } else {
            foreach ($webhookIps as $ip) {
                $this->line("  - {$ip}");
            }
        }

        $this->line('');

        // Test current request context
        if (app()->runningInConsole()) {
            $this->comment('Current Request Context:');
            $this->line('  - Running in console mode');
            $this->line('  - APP_URL: ' . config('app.url'));
        }
    }

    /**
     * Simulate a webhook request.
     */
    private function simulateWebhookRequest(): void
    {
        $this->line('');
        $this->info('Simulating Webhook Request:');
        $this->line('');

        $appUrl = config('app.url');
        if (!$appUrl || $appUrl === 'http://localhost') {
            $this->warn('APP_URL is not properly configured. Using default localhost.');
            $appUrl = 'http://localhost:8000';
        }

        $webhookUrl = rtrim($appUrl, '/') . '/webhook/test';

        $this->comment("Testing webhook endpoint: {$webhookUrl}");

        // Simulate T-Bank webhook
        $tBankIp = '************'; // Example T-Bank IP
        $this->line("Simulating request from T-Bank IP: {$tBankIp}");

        $headers = [
            'X-Forwarded-For' => $tBankIp,
            'X-Forwarded-Host' => parse_url($appUrl, PHP_URL_HOST),
            'X-Forwarded-Proto' => 'https',
            'User-Agent' => 'T-Bank-Webhook/1.0',
            'Content-Type' => 'application/json',
        ];

        $payload = [
            'TerminalKey' => 'test_terminal',
            'OrderId' => 'ORD-TEST123',
            'PaymentId' => '12345',
            'Status' => 'CONFIRMED',
            'Amount' => 10000,
        ];

        try {
            $response = Http::withHeaders($headers)
                ->timeout(10)
                ->post($webhookUrl, $payload);

            $this->line("Response status: {$response->status()}");
            
            if ($response->successful()) {
                $this->line('  ✓ Webhook simulation successful');
            } else {
                $this->line('  ✗ Webhook simulation failed');
                $this->line("  Error: {$response->body()}");
            }

        } catch (\Exception $e) {
            $this->error("Webhook simulation failed: {$e->getMessage()}");
            $this->line('This is expected if the webhook endpoint is not set up yet.');
        }
    }

    /**
     * Check if IP is trusted.
     */
    private function isIpTrusted(string $ip): bool
    {
        $trustedProxies = getTrustedProxies();
        
        if ($trustedProxies === '*') {
            return true;
        }

        if (!is_array($trustedProxies)) {
            return false;
        }

        foreach ($trustedProxies as $proxy) {
            if ($this->ipInRange($ip, $proxy)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if host is trusted.
     */
    private function isHostTrusted(string $host): bool
    {
        $trustedHosts = getTrustedHosts();
        if (is_callable($trustedHosts)) {
            $trustedHosts = $trustedHosts();
        }

        return in_array($host, $trustedHosts);
    }

    /**
     * Check if IP is in range.
     */
    private function ipInRange(string $ip, string $range): bool
    {
        if ($range === '*') {
            return true;
        }

        if (!str_contains($range, '/')) {
            return $ip === $range;
        }

        [$subnet, $mask] = explode('/', $range);
        
        if (!filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) ||
            !filter_var($subnet, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return false;
        }

        $ipLong = ip2long($ip);
        $subnetLong = ip2long($subnet);
        $maskLong = -1 << (32 - (int) $mask);

        return ($ipLong & $maskLong) === ($subnetLong & $maskLong);
    }

    /**
     * Get header names from bitmask.
     */
    private function getHeaderNames(int $headers): array
    {
        $names = [];

        if ($headers & Request::HEADER_X_FORWARDED_FOR) {
            $names[] = 'X-Forwarded-For';
        }
        if ($headers & Request::HEADER_X_FORWARDED_HOST) {
            $names[] = 'X-Forwarded-Host';
        }
        if ($headers & Request::HEADER_X_FORWARDED_PORT) {
            $names[] = 'X-Forwarded-Port';
        }
        if ($headers & Request::HEADER_X_FORWARDED_PROTO) {
            $names[] = 'X-Forwarded-Proto';
        }
        if ($headers & Request::HEADER_X_FORWARDED_PREFIX) {
            $names[] = 'X-Forwarded-Prefix';
        }
        if ($headers & Request::HEADER_X_FORWARDED_AWS_ELB) {
            $names[] = 'X-Forwarded-AWS-ELB';
        }
        if ($headers & Request::HEADER_FORWARDED) {
            $names[] = 'Forwarded';
        }

        return $names;
    }
}
