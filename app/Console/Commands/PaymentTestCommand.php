<?php

namespace App\Console\Commands;

use App\Services\Payment\PaymentGatewayManager;
use App\Services\Payment\DTOs\PaymentRequest;
use App\Models\Order;
use App\Models\User;
use Illuminate\Console\Command;

class PaymentTestCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'payment:test
                            {gateway? : The payment gateway to test}
                            {--all : Test all available gateways}
                            {--amount=100 : Test payment amount in kopecks}
                            {--currency=RUB : Test payment currency}';

    /**
     * The console command description.
     */
    protected $description = 'Test payment gateway functionality';

    private PaymentGatewayManager $gatewayManager;

    public function __construct(PaymentGatewayManager $gatewayManager)
    {
        parent::__construct();
        $this->gatewayManager = $gatewayManager;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Payment Gateway Test Tool');
        $this->line('');

        if ($this->option('all')) {
            return $this->testAllGateways();
        }

        $gateway = $this->argument('gateway');
        if (!$gateway) {
            $gateway = $this->choice(
                'Which gateway would you like to test?',
                $this->gatewayManager->getAvailableGateways()
            );
        }

        return $this->testGateway($gateway);
    }

    /**
     * Test all available gateways.
     */
    private function testAllGateways(): int
    {
        $gateways = $this->gatewayManager->getAvailableGateways();
        $results = [];

        foreach ($gateways as $gateway) {
            $this->info("Testing gateway: {$gateway}");
            $result = $this->performGatewayTest($gateway);
            $results[$gateway] = $result;
            $this->line('');
        }

        $this->displayTestResults($results);
        return 0;
    }

    /**
     * Test a specific gateway.
     */
    private function testGateway(string $gateway): int
    {
        $this->info("Testing gateway: {$gateway}");
        $result = $this->performGatewayTest($gateway);

        $this->displayGatewayResult($gateway, $result);

        return $result['overall_success'] ? 0 : 1;
    }

    /**
     * Perform gateway test.
     */
    private function performGatewayTest(string $gatewayName): array
    {
        $result = [
            'gateway' => $gatewayName,
            'configuration_valid' => false,
            'gateway_available' => false,
            'connectivity_test' => false,
            'validation_test' => false,
            'overall_success' => false,
            'errors' => [],
        ];

        try {
            // Test 1: Configuration validation
            $gateway = $this->gatewayManager->getGateway($gatewayName);
            $result['configuration_valid'] = true;
            $this->line("✓ Configuration valid");

            // Test 2: Gateway availability
            $result['gateway_available'] = $gateway->isAvailable();
            if ($result['gateway_available']) {
                $this->line("✓ Gateway available");
            } else {
                $this->line("✗ Gateway not available");
                $result['errors'][] = 'Gateway is not available';
            }

            // Test 3: Connectivity test
            $connectivityResult = $this->gatewayManager->testGateway($gatewayName);
            $result['connectivity_test'] = $connectivityResult['available'];
            if ($result['connectivity_test']) {
                $this->line("✓ Connectivity test passed");
            } else {
                $this->line("✗ Connectivity test failed");
                if (isset($connectivityResult['error'])) {
                    $result['errors'][] = $connectivityResult['error'];
                }
            }

            // Test 4: Validation test
            $validationResult = $this->testPaymentValidation($gateway);
            $result['validation_test'] = $validationResult['success'];

            if ($result['validation_test']) {
                $this->line("✓ Validation test passed");
            } else {
                // Check if this is a configuration error
                if (isset($validationResult['configuration_error']) && $validationResult['configuration_error']) {
                    $this->line("⚠ Configuration incomplete (not a validation failure)");
                    // Don't count configuration errors as validation failures
                    $result['validation_test'] = true;
                    $result['configuration_incomplete'] = true;
                } else {
                    $this->line("✗ Validation test failed");
                }
                $result['errors'] = array_merge($result['errors'], $validationResult['errors']);
            }

            // Overall success (configuration incomplete is not a failure)
            $result['overall_success'] = $result['configuration_valid'] &&
                                       $result['gateway_available'] &&
                                       $result['connectivity_test'] &&
                                       $result['validation_test'];

        } catch (\Exception $e) {
            $this->line("✗ Test failed: " . $e->getMessage());
            $result['errors'][] = $e->getMessage();
        }

        return $result;
    }

    /**
     * Test payment validation.
     */
    private function testPaymentValidation($gateway): array
    {
        try {
            // Get or create a test user
            $user = $this->getOrCreateTestUser();

            if (!$user) {
                return [
                    'success' => false,
                    'errors' => ['No test user available and unable to create one'],
                ];
            }

            // Get gateway-specific test amount
            $testAmount = $this->getTestAmountForGateway($gateway->getName());

            // Create a test order with proper attributes
            $order = $this->createTestOrder($user, $testAmount);

            $paymentRequest = new PaymentRequest(
                order: $order,
                user: $user,
                amount: $testAmount,
                currency: $this->option('currency'),
                description: 'Test payment'
            );

            $errors = $gateway->validatePaymentRequest($paymentRequest);

            return [
                'success' => empty($errors),
                'errors' => $errors,
            ];

        } catch (\Exception $e) {
            // Check if this is a configuration error rather than validation error
            if ($this->isConfigurationError($e, $gateway->getName())) {
                return [
                    'success' => false,
                    'errors' => ['Configuration incomplete: ' . $e->getMessage()],
                    'configuration_error' => true,
                ];
            }

            return [
                'success' => false,
                'errors' => [$e->getMessage()],
            ];
        }
    }

    /**
     * Get or create a test user for validation.
     */
    private function getOrCreateTestUser(): ?User
    {
        // Try to get an existing user first
        $user = User::first();

        if ($user) {
            return $user;
        }

        // If no users exist, try to create a test user
        try {
            return User::create([
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'is_active' => true,
                'registered_at' => now(),
            ]);
        } catch (\Exception) {
            // If we can't create a user (e.g., due to unique constraints),
            // try to find any existing user with a different email
            $existingUser = User::where('email', 'like', 'test%')->first();

            if ($existingUser) {
                return $existingUser;
            }

            // Try with a unique email
            try {
                return User::create([
                    'name' => 'Test User',
                    'email' => 'test-' . time() . '@example.com',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                    'is_active' => true,
                    'registered_at' => now(),
                ]);
            } catch (\Exception) {
                // If all else fails, return null
                return null;
            }
        }
    }

    /**
     * Get the appropriate test amount for a specific gateway.
     */
    private function getTestAmountForGateway(string $gatewayName): int
    {
        return match (strtolower($gatewayName)) {
            'free' => 0, // Free gateway requires zero amount
            default => (int) $this->option('amount'), // Use specified amount for other gateways
        };
    }

    /**
     * Check if an exception is a configuration error rather than validation error.
     */
    private function isConfigurationError(\Exception $e, string $gatewayName): bool
    {
        $message = $e->getMessage();

        // T-Bank specific configuration errors
        if (strtolower($gatewayName) === 'tbank') {
            return str_contains($message, 'terminal_key') ||
                   str_contains($message, 'password') ||
                   str_contains($message, 'requires');
        }

        // General configuration error patterns
        $configErrorPatterns = [
            'requires',
            'missing configuration',
            'invalid configuration',
            'not configured',
            'configuration error',
        ];

        foreach ($configErrorPatterns as $pattern) {
            if (str_contains(strtolower($message), $pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Create a test order for validation.
     */
    private function createTestOrder(User $user, ?int $amount = null): Order
    {
        $order = new Order();

        // Use provided amount or fall back to option
        $testAmount = $amount ?? (int) $this->option('amount');

        // Set required attributes
        $order->id = (string) \Illuminate\Support\Str::uuid();
        $order->public_id = 'ORD-TEST' . strtoupper(\Illuminate\Support\Str::random(7));
        $order->user_id = $user->id;
        $order->status = 'pending';
        $order->total_amount = $testAmount;
        $order->currency = $this->option('currency');
        $order->created_at = now();
        $order->updated_at = now();

        // Set the user relationship to avoid additional database queries
        $order->setRelation('user', $user);

        // Mark the model as existing to prevent save attempts during validation
        $order->exists = false;

        return $order;
    }

    /**
     * Display test results for all gateways.
     */
    private function displayTestResults(array $results): void
    {
        $this->info('Test Results Summary:');
        $this->line('');

        $headers = ['Gateway', 'Config', 'Available', 'Connectivity', 'Validation', 'Overall'];
        $rows = [];

        foreach ($results as $result) {
            // Handle validation display with configuration status
            $validationDisplay = $result['validation_test'] ? '✓' : '✗';
            if (isset($result['configuration_incomplete']) && $result['configuration_incomplete']) {
                $validationDisplay = '⚠'; // Warning symbol for configuration incomplete
            }

            $rows[] = [
                $result['gateway'],
                $result['configuration_valid'] ? '✓' : '✗',
                $result['gateway_available'] ? '✓' : '✗',
                $result['connectivity_test'] ? '✓' : '✗',
                $validationDisplay,
                $result['overall_success'] ? '✓' : '✗',
            ];
        }

        $this->table($headers, $rows);

        // Show legend
        $this->line('');
        $this->comment('Legend: ✓ = Success, ✗ = Failed, ⚠ = Configuration Incomplete');

        // Show errors and configuration notes
        foreach ($results as $result) {
            if (!empty($result['errors'])) {
                $this->line('');
                if (isset($result['configuration_incomplete']) && $result['configuration_incomplete']) {
                    $this->warn("Configuration notes for {$result['gateway']}:");
                } else {
                    $this->error("Errors for {$result['gateway']}:");
                }
                foreach ($result['errors'] as $error) {
                    $this->line("  - {$error}");
                }
            }
        }
    }

    /**
     * Display result for a single gateway.
     */
    private function displayGatewayResult(string $gateway, array $result): void
    {
        $this->line('');

        if ($result['overall_success']) {
            if (isset($result['configuration_incomplete']) && $result['configuration_incomplete']) {
                $this->warn("⚠ Gateway {$gateway} test completed with configuration incomplete!");
                $this->line('  Note: Gateway functionality is working, but some configuration is missing.');
            } else {
                $this->info("✓ Gateway {$gateway} test completed successfully!");
            }
        } else {
            $this->error("✗ Gateway {$gateway} test failed!");
        }

        if (!empty($result['errors'])) {
            $this->line('');
            if (isset($result['configuration_incomplete']) && $result['configuration_incomplete']) {
                $this->warn('Configuration Notes:');
            } else {
                $this->error('Errors:');
            }
            foreach ($result['errors'] as $error) {
                $this->line("  - {$error}");
            }
        }
    }
}
