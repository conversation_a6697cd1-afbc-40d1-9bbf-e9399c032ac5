<?php

namespace App\Console\Commands;

use App\Services\Payment\PaymentGatewayManager;
use App\Services\Payment\DTOs\PaymentRequest;
use App\Models\Order;
use App\Models\User;
use Illuminate\Console\Command;

class PaymentTestCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'payment:test
                            {gateway? : The payment gateway to test}
                            {--all : Test all available gateways}
                            {--amount=100 : Test payment amount in kopecks}
                            {--currency=RUB : Test payment currency}';

    /**
     * The console command description.
     */
    protected $description = 'Test payment gateway functionality';

    private PaymentGatewayManager $gatewayManager;

    public function __construct(PaymentGatewayManager $gatewayManager)
    {
        parent::__construct();
        $this->gatewayManager = $gatewayManager;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Payment Gateway Test Tool');
        $this->line('');

        if ($this->option('all')) {
            return $this->testAllGateways();
        }

        $gateway = $this->argument('gateway');
        if (!$gateway) {
            $gateway = $this->choice(
                'Which gateway would you like to test?',
                $this->gatewayManager->getAvailableGateways()
            );
        }

        return $this->testGateway($gateway);
    }

    /**
     * Test all available gateways.
     */
    private function testAllGateways(): int
    {
        $gateways = $this->gatewayManager->getAvailableGateways();
        $results = [];

        foreach ($gateways as $gateway) {
            $this->info("Testing gateway: {$gateway}");
            $result = $this->performGatewayTest($gateway);
            $results[$gateway] = $result;
            $this->line('');
        }

        $this->displayTestResults($results);
        return 0;
    }

    /**
     * Test a specific gateway.
     */
    private function testGateway(string $gateway): int
    {
        $this->info("Testing gateway: {$gateway}");
        $result = $this->performGatewayTest($gateway);

        $this->displayGatewayResult($gateway, $result);

        return $result['overall_success'] ? 0 : 1;
    }

    /**
     * Perform gateway test.
     */
    private function performGatewayTest(string $gatewayName): array
    {
        $result = [
            'gateway' => $gatewayName,
            'configuration_valid' => false,
            'gateway_available' => false,
            'connectivity_test' => false,
            'validation_test' => false,
            'overall_success' => false,
            'errors' => [],
        ];

        try {
            // Test 1: Configuration validation
            $gateway = $this->gatewayManager->getGateway($gatewayName);
            $result['configuration_valid'] = true;
            $this->line("✓ Configuration valid");

            // Test 2: Gateway availability
            $result['gateway_available'] = $gateway->isAvailable();
            if ($result['gateway_available']) {
                $this->line("✓ Gateway available");
            } else {
                $this->line("✗ Gateway not available");
                $result['errors'][] = 'Gateway is not available';
            }

            // Test 3: Connectivity test
            $connectivityResult = $this->gatewayManager->testGateway($gatewayName);
            $result['connectivity_test'] = $connectivityResult['available'];
            if ($result['connectivity_test']) {
                $this->line("✓ Connectivity test passed");
            } else {
                $this->line("✗ Connectivity test failed");
                if (isset($connectivityResult['error'])) {
                    $result['errors'][] = $connectivityResult['error'];
                }
            }

            // Test 4: Validation test
            $validationResult = $this->testPaymentValidation($gateway);
            $result['validation_test'] = $validationResult['success'];
            if ($result['validation_test']) {
                $this->line("✓ Validation test passed");
            } else {
                $this->line("✗ Validation test failed");
                $result['errors'] = array_merge($result['errors'], $validationResult['errors']);
            }

            // Overall success
            $result['overall_success'] = $result['configuration_valid'] &&
                                       $result['gateway_available'] &&
                                       $result['connectivity_test'] &&
                                       $result['validation_test'];

        } catch (\Exception $e) {
            $this->line("✗ Test failed: " . $e->getMessage());
            $result['errors'][] = $e->getMessage();
        }

        return $result;
    }

    /**
     * Test payment validation.
     */
    private function testPaymentValidation($gateway): array
    {
        try {
            // Get or create a test user
            $user = $this->getOrCreateTestUser();

            if (!$user) {
                return [
                    'success' => false,
                    'errors' => ['No test user available and unable to create one'],
                ];
            }

            // Create a test order with proper attributes
            $order = $this->createTestOrder($user);

            $paymentRequest = new PaymentRequest(
                order: $order,
                user: $user,
                amount: (int) $this->option('amount'),
                currency: $this->option('currency'),
                description: 'Test payment'
            );

            $errors = $gateway->validatePaymentRequest($paymentRequest);

            return [
                'success' => empty($errors),
                'errors' => $errors,
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'errors' => [$e->getMessage()],
            ];
        }
    }

    /**
     * Get or create a test user for validation.
     */
    private function getOrCreateTestUser(): ?User
    {
        // Try to get an existing user first
        $user = User::first();

        if ($user) {
            return $user;
        }

        // If no users exist, try to create a test user
        try {
            return User::create([
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'is_active' => true,
                'registered_at' => now(),
            ]);
        } catch (\Exception) {
            // If we can't create a user (e.g., due to unique constraints),
            // try to find any existing user with a different email
            $existingUser = User::where('email', 'like', 'test%')->first();

            if ($existingUser) {
                return $existingUser;
            }

            // Try with a unique email
            try {
                return User::create([
                    'name' => 'Test User',
                    'email' => 'test-' . time() . '@example.com',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                    'is_active' => true,
                    'registered_at' => now(),
                ]);
            } catch (\Exception) {
                // If all else fails, return null
                return null;
            }
        }
    }

    /**
     * Create a test order for validation.
     */
    private function createTestOrder(User $user): Order
    {
        $order = new Order();

        // Set required attributes
        $order->id = (string) \Illuminate\Support\Str::uuid();
        $order->public_id = 'ORD-TEST' . strtoupper(\Illuminate\Support\Str::random(7));
        $order->user_id = $user->id;
        $order->status = 'pending';
        $order->total_amount = (int) $this->option('amount');
        $order->currency = $this->option('currency');
        $order->created_at = now();
        $order->updated_at = now();

        // Set the user relationship to avoid additional database queries
        $order->setRelation('user', $user);

        // Mark the model as existing to prevent save attempts during validation
        $order->exists = false;

        return $order;
    }

    /**
     * Display test results for all gateways.
     */
    private function displayTestResults(array $results): void
    {
        $this->info('Test Results Summary:');
        $this->line('');

        $headers = ['Gateway', 'Config', 'Available', 'Connectivity', 'Validation', 'Overall'];
        $rows = [];

        foreach ($results as $result) {
            $rows[] = [
                $result['gateway'],
                $result['configuration_valid'] ? '✓' : '✗',
                $result['gateway_available'] ? '✓' : '✗',
                $result['connectivity_test'] ? '✓' : '✗',
                $result['validation_test'] ? '✓' : '✗',
                $result['overall_success'] ? '✓' : '✗',
            ];
        }

        $this->table($headers, $rows);

        // Show errors
        foreach ($results as $result) {
            if (!empty($result['errors'])) {
                $this->line('');
                $this->error("Errors for {$result['gateway']}:");
                foreach ($result['errors'] as $error) {
                    $this->line("  - {$error}");
                }
            }
        }
    }

    /**
     * Display result for a single gateway.
     */
    private function displayGatewayResult(string $gateway, array $result): void
    {
        $this->line('');

        if ($result['overall_success']) {
            $this->info("✓ Gateway {$gateway} test completed successfully!");
        } else {
            $this->error("✗ Gateway {$gateway} test failed!");

            if (!empty($result['errors'])) {
                $this->line('');
                $this->error('Errors:');
                foreach ($result['errors'] as $error) {
                    $this->line("  - {$error}");
                }
            }
        }
    }
}
