<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\PaymentMethod;
use App\Services\OrderService;
use App\Services\PaymentService;
use Illuminate\Console\Command;

class TestPaymentSystem extends Command
{
    protected $signature = 'payment:test {gateway=free} {--amount=100}';
    protected $description = 'Test payment system with different gateways';

    public function handle(OrderService $orderService, PaymentService $paymentService): int
    {
        $gateway = $this->argument('gateway');
        $amount = (int) $this->option('amount');

        $this->info("Testing payment system with gateway: {$gateway}");

        try {
            // Get or create test user
            $user = User::firstOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'name' => 'Test User',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now(),
                ]
            );

            // Get or create test plan
            $planName = $gateway === 'free' ? 'Free Test Plan' : 'Test Plan';
            $planPrice = $gateway === 'free' ? 0 : $amount;

            $plan = SubscriptionPlan::firstOrCreate(
                ['name' => $planName],
                [
                    'description' => "Test subscription plan for {$gateway}",
                    'price' => $planPrice,
                    'duration_days' => 30,
                    'is_active' => true,
                ]
            );

            // Check if payment method exists
            $paymentMethod = PaymentMethod::where('code', $gateway)->first();
            if (!$paymentMethod) {
                $this->error("Payment method '{$gateway}' not found");
                return 1;
            }

            $this->info("Creating order for user: {$user->email}");
            $this->info("Plan: {$plan->name} (Price: {$plan->price} kopecks)");

            // Create order
            $order = $orderService->createVpnSubscriptionOrder($user, $plan, 'new');
            $this->info("Order created: {$order->public_id}");

            // Create payment
            $this->info("Creating payment with gateway: {$gateway}");
            $this->info("Order ID for URL testing: {$order->public_id}");
            $result = $paymentService->createPayment($order, $gateway);

            if ($result->isSuccess()) {
                $this->info("✅ Payment created successfully!");
                $this->info("Payment ID: {$result->paymentId}");
                $this->info("Status: {$result->status}");

                if ($result->paymentUrl) {
                    $this->info("Payment URL: {$result->paymentUrl}");
                }

                // Test webhook if supported
                $gatewayInstance = $paymentService->getGateway($gateway);
                if ($gatewayInstance->supportsWebhooks()) {
                    $this->info("Testing webhook handling...");

                    $webhookData = [
                        'PaymentId' => $result->paymentId,
                        'Status' => 'CONFIRMED',
                        'Success' => true,
                        'Amount' => $amount,
                    ];

                    $webhookResult = $paymentService->handleWebhook($gateway, $webhookData);

                    if ($webhookResult) {
                        $this->info("✅ Webhook processed successfully!");
                    } else {
                        $this->warn("⚠️ Webhook processing failed");
                    }
                }

                return 0;

            } else {
                $this->error("❌ Payment creation failed!");
                $this->error("Error: {$result->message}");
                $this->error("Error Code: {$result->errorCode}");
                return 1;
            }

        } catch (\Exception $e) {
            $this->error("❌ Test failed with exception:");
            $this->error($e->getMessage());
            return 1;
        }
    }
}
