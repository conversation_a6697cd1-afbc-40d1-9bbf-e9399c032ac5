<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class DisableTBankSignatureVerification extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'tbank:disable-signature {--enable : Enable signature verification instead of disabling}';

    /**
     * The console command description.
     */
    protected $description = 'Disable T-Bank webhook signature verification for testing';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $enable = $this->option('enable');
        $envFile = base_path('.env');
        
        if (!File::exists($envFile)) {
            $this->error('.env file not found');
            return 1;
        }

        $envContent = File::get($envFile);
        $newValue = $enable ? 'true' : 'false';
        $action = $enable ? 'Enabling' : 'Disabling';
        
        $this->info("{$action} T-Bank webhook signature verification...");

        // Check if the setting already exists
        if (preg_match('/^TBANK_WEBHOOK_VERIFY_SIGNATURE=(.*)$/m', $envContent, $matches)) {
            // Update existing setting
            $envContent = preg_replace(
                '/^TBANK_WEBHOOK_VERIFY_SIGNATURE=(.*)$/m',
                "TBANK_WEBHOOK_VERIFY_SIGNATURE={$newValue}",
                $envContent
            );
            $this->info("Updated existing TBANK_WEBHOOK_VERIFY_SIGNATURE setting");
        } else {
            // Add new setting
            $envContent .= "\n# T-Bank webhook signature verification\n";
            $envContent .= "TBANK_WEBHOOK_VERIFY_SIGNATURE={$newValue}\n";
            $this->info("Added TBANK_WEBHOOK_VERIFY_SIGNATURE setting");
        }

        // Write back to .env file
        File::put($envFile, $envContent);

        // Clear configuration cache
        $this->call('config:clear');

        $status = $enable ? 'enabled' : 'disabled';
        $this->info("✅ T-Bank webhook signature verification {$status}");
        
        if (!$enable) {
            $this->warn("⚠️  Signature verification is now disabled!");
            $this->warn("   This should only be used for testing/development.");
            $this->warn("   Remember to re-enable it for production:");
            $this->warn("   php artisan tbank:disable-signature --enable");
        }

        // Show current configuration
        $this->newLine();
        $this->info("Current configuration:");
        $this->line("Environment: " . app()->environment());
        $this->line("Signature verification: " . (config('payments.gateways.tbank.config.verify_signature') ? 'enabled' : 'disabled'));
        
        // Test webhook URL
        $webhookUrl = url('/webhook/tbank');
        $this->line("Webhook URL: {$webhookUrl}");

        $this->newLine();
        $this->info("You can now test the webhook with:");
        $this->line("curl -X POST {$webhookUrl} \\");
        $this->line("  -H 'Content-Type: application/json' \\");
        $this->line("  -d '{\"TerminalKey\":\"test\",\"OrderId\":\"ORD-TEST\",\"Status\":\"CONFIRMED\"}'");

        return 0;
    }
}
