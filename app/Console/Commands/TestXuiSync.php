<?php

namespace App\Console\Commands;

use App\Models\XuiServer;
use App\Services\XuiServerSyncService;
use Illuminate\Console\Command;

class TestXuiSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:xui-sync {server_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test XUI server synchronization functionality';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $serverId = $this->argument('server_id');

        if ($serverId) {
            return $this->testSingleServer($serverId);
        } else {
            return $this->testAllServers();
        }
    }

    private function testSingleServer(int $serverId): int
    {
        try {
            $server = XuiServer::findOrFail($serverId);
            $this->info("Testing sync for server: {$server->name} (ID: {$server->id})");

            $this->displayServerInfo($server);

            if (!$this->confirm('Do you want to proceed with the sync test?')) {
                $this->info('Test cancelled');
                return 0;
            }

            $syncService = new XuiServerSyncService();
            $result = $syncService->syncSingleServer($server);

            if ($result) {
                $this->info('✅ Server sync completed successfully!');
                $this->displayServerInfoAfterSync($server);
            } else {
                $this->error('❌ Server sync failed');
            }

            return $result ? 0 : 1;

        } catch (\Exception $e) {
            $this->error("❌ Test failed: {$e->getMessage()}");
            return 1;
        }
    }

    private function testAllServers(): int
    {
        $this->info('Testing sync for all eligible servers');

        $servers = XuiServer::active()->autoSync()->get();

        if ($servers->isEmpty()) {
            $this->warn('No eligible servers found for sync');
            return 0;
        }

        $this->info("Found {$servers->count()} eligible servers:");
        foreach ($servers as $server) {
            $this->line("- {$server->name} (ID: {$server->id})");
        }

        if (!$this->confirm('Do you want to proceed with sync test for all servers?')) {
            $this->info('Test cancelled');
            return 0;
        }

        $syncService = new XuiServerSyncService();
        $results = $syncService->syncServers(['force' => false]);

        $this->info('✅ Sync test completed!');
        $this->table(['Metric', 'Count'], [
            ['Synced Successfully', $results['synced'] ?? 0],
            ['Failed', $results['failed'] ?? 0],
            ['Skipped', $results['skipped'] ?? 0],
        ]);

        return 0;
    }

    private function displayServerInfo(XuiServer $server): void
    {
        $this->table(['Property', 'Value'], [
            ['Name', $server->name],
            ['Address', $server->address . ':' . $server->port],
            ['Active', $server->is_active ? 'Yes' : 'No'],
            ['Auto Sync', $server->auto_sync ? 'Yes' : 'No'],
            ['Last Sync', $server->last_sync_at ? $server->last_sync_at->diffForHumans() : 'Never'],
            ['Server Load', $server->server_load ? $server->server_load . '%' : 'Unknown'],
            ['Clients Count', $server->clients_count ?? 'Unknown'],
            ['Online Clients', $server->clients_online_count ?? 'Unknown'],
        ]);

        // Show pools
        $pools = $server->serverPools()->where('server_pools.is_active', true)->wherePivotNull('released_at')->get();
        if ($pools->isNotEmpty()) {
            $this->info("\n📊 Server Pools:");
            foreach ($pools as $pool) {
                $userCount = $pool->users()->wherePivotNull('released_at')->count();
                $this->line("- {$pool->name} (Users: {$userCount})");
            }
        }
    }

    private function displayServerInfoAfterSync(XuiServer $server): void
    {
        $server->refresh();
        
        $this->info("\n📊 Server Info After Sync:");
        $this->table(['Property', 'Value'], [
            ['Last Sync', $server->last_sync_at ? $server->last_sync_at->format('Y-m-d H:i:s') : 'Never'],
            ['Server Load', $server->server_load ? $server->server_load . '%' : 'Unknown'],
            ['Clients Count', $server->clients_count ?? 'Unknown'],
            ['Online Clients', $server->clients_online_count ?? 'Unknown'],
            ['Raw Status Updated', $server->raw_server_status_updated_at ? $server->raw_server_status_updated_at->format('Y-m-d H:i:s') : 'Never'],
            ['Raw Settings Updated', $server->raw_settings_all_updated_at ? $server->raw_settings_all_updated_at->format('Y-m-d H:i:s') : 'Never'],
            ['Raw Inbounds Updated', $server->raw_inbounds_list_updated_at ? $server->raw_inbounds_list_updated_at->format('Y-m-d H:i:s') : 'Never'],
            ['Raw Clients Updated', $server->raw_clients_online_updated_at ? $server->raw_clients_online_updated_at->format('Y-m-d H:i:s') : 'Never'],
        ]);

        // Show online clients if available
        if ($server->clients_online_list && is_array($server->clients_online_list)) {
            $this->info("\n👥 Online Clients:");
            foreach (array_slice($server->clients_online_list, 0, 10) as $clientEmail) {
                $this->line("- {$clientEmail}");
            }
            if (count($server->clients_online_list) > 10) {
                $this->line("... and " . (count($server->clients_online_list) - 10) . " more");
            }
        }
    }
}
