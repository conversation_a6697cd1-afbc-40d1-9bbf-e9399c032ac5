<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\PaymentMethod;
use App\Http\Controllers\PlanSelectionController;
use Illuminate\Console\Command;

class TestPlanSelection extends Command
{
    protected $signature = 'test:plan-selection {user_id}';
    protected $description = 'Test plan selection controller functionality';

    public function handle(): int
    {
        $userId = $this->argument('user_id');

        try {
            // Find user
            $user = User::findOrFail($userId);
            $this->info("Testing plan selection for user: {$user->name} ({$user->id})");

            // Test controller
            $controller = new PlanSelectionController();
            $view = $controller->show($user->id);

            // Extract data from view
            $data = $view->getData();

            $this->info("✅ Controller executed successfully");
            $this->info("📊 Data summary:");
            $this->info("- User: {$data['user']->name}");
            $this->info("- Available plans: {$data['availablePlans']->count()}");
            $this->info("- Payment methods: {$data['paymentMethods']->count()}");
            $this->info("- Current subscription: " . ($data['currentSubscription'] ? 'Yes' : 'No'));
            $this->info("- Subscription status: {$data['subscriptionStatus']['status']}");
            $this->info("- Status message: {$data['subscriptionStatus']['message']}");

            $this->info("\n📋 Available Plans:");
            foreach ($data['availablePlans'] as $plan) {
                $current = $data['currentPlan'] && $data['currentPlan']->id === $plan->id ? ' (CURRENT)' : '';
                $this->info("- {$plan->name}: {$plan->formatted_price}{$current}");
            }

            $this->info("\n💳 Payment Methods:");
            foreach ($data['paymentMethods'] as $method) {
                $this->info("- {$method->name} ({$method->code}) - {$method->type}");
            }

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Test failed: {$e->getMessage()}");
            return 1;
        }
    }
}
