<?php

namespace App\Console\Commands;

use App\Models\XuiServer;
use App\Models\User;
use App\Models\UserOnlineLog;
use App\Services\UserOnlineStatusSyncService;
use Illuminate\Console\Command;

class TestUserStatusSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:user-status-sync {server_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test user online status synchronization functionality';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $serverId = $this->argument('server_id');

        if ($serverId) {
            return $this->testSingleServer($serverId);
        } else {
            return $this->testAllServers();
        }
    }

    private function testSingleServer(int $serverId): int
    {
        try {
            $server = XuiServer::findOrFail($serverId);
            $this->info("Testing user status sync for server: {$server->name} (ID: {$server->id})");

            $this->displayServerInfo($server);

            if (!$this->confirm('Do you want to proceed with the user status sync test?')) {
                $this->info('Test cancelled');
                return 0;
            }

            $syncService = new UserOnlineStatusSyncService();
            $result = $syncService->syncSingleServerUserStatus($server);

            if ($result) {
                $this->info('✅ User status sync completed successfully!');
                $this->displayUserStatusAfterSync($server);
            } else {
                $this->error('❌ User status sync failed');
            }

            return $result ? 0 : 1;

        } catch (\Exception $e) {
            $this->error("❌ Test failed: {$e->getMessage()}");
            return 1;
        }
    }

    private function testAllServers(): int
    {
        $this->info('Testing user status sync for all eligible servers');

        $servers = XuiServer::active()->autoSync()->get();

        if ($servers->isEmpty()) {
            $this->warn('No eligible servers found for user status sync');
            return 0;
        }

        $this->info("Found {$servers->count()} eligible servers:");
        foreach ($servers as $server) {
            $this->line("- {$server->name} (ID: {$server->id})");
        }

        if (!$this->confirm('Do you want to proceed with user status sync test for all servers?')) {
            $this->info('Test cancelled');
            return 0;
        }

        $syncService = new UserOnlineStatusSyncService();
        $results = $syncService->syncUserStatus(['force' => false]);

        $this->info('✅ User status sync test completed!');
        $this->table(['Metric', 'Count'], [
            ['Synced Successfully', $results['synced'] ?? 0],
            ['Failed', $results['failed'] ?? 0],
            ['Skipped', $results['skipped'] ?? 0],
        ]);

        return 0;
    }

    private function displayServerInfo(XuiServer $server): void
    {
        $this->table(['Property', 'Value'], [
            ['Name', $server->name],
            ['Address', $server->address . ':' . $server->port],
            ['Active', $server->is_active ? 'Yes' : 'No'],
            ['Auto Sync', $server->auto_sync ? 'Yes' : 'No'],
            ['Online Clients', $server->clients_online_count ?? 'Unknown'],
        ]);

        // Show pools and users
        $pools = $server->serverPools()->where('server_pools.is_active', true)->whereNull('xui_server_pool_assignments.released_at')->get();
        if ($pools->isNotEmpty()) {
            $this->info("\n📊 Server Pools and Users:");
            foreach ($pools as $pool) {
                $users = $pool->users()->whereNull('user_server_assignments.released_at')->get();
                $this->line("- Pool: {$pool->name} (Users: {$users->count()})");

                foreach ($users->take(5) as $user) {
                    $lastOnline = $user->last_online_at ? $user->last_online_at->diffForHumans() : 'Never';
                    $this->line("  - {$user->email} (Last online: {$lastOnline})");
                }

                if ($users->count() > 5) {
                    $this->line("  ... and " . ($users->count() - 5) . " more users");
                }
            }
        }
    }

    private function displayUserStatusAfterSync(XuiServer $server): void
    {
        $this->info("\n👥 User Status After Sync:");

        // Get recent user online logs for this server
        $recentLogs = UserOnlineLog::where('xui_server_id', $server->id)
            ->where('created_at', '>=', now()->subMinutes(5))
            ->with('user')
            ->latest('status_timestamp')
            ->take(10)
            ->get();

        if ($recentLogs->isNotEmpty()) {
            $this->table(['User Email', 'Status', 'Timestamp', 'Created'],
                $recentLogs->map(function ($log) {
                    return [
                        $log->user->email ?? 'Unknown',
                        $log->status,
                        $log->status_timestamp->format('H:i:s'),
                        $log->created_at->format('H:i:s'),
                    ];
                })->toArray()
            );
        } else {
            $this->info('No recent user status changes found');
        }

        // Show current online users count
        $onlineUsersCount = User::whereNotNull('last_online_at')
            ->where('last_online_at', '>=', now()->subSeconds(60))
            ->whereHas('serverPools', function ($query) use ($server) {
                $query->whereHas('servers', function ($q) use ($server) {
                    $q->where('xui_servers.id', $server->id);
                });
            })
            ->count();

        $this->info("\n📈 Current Statistics:");
        $this->table(['Metric', 'Value'], [
            ['Users online in last 60s', $onlineUsersCount],
            ['Server online clients', $server->fresh()->clients_online_count ?? 0],
            ['Recent status changes', $recentLogs->count()],
        ]);
    }
}
