<?php

namespace App\Services;

use App\Models\Subscription;
use App\Models\User;
use App\Models\SubscriptionPlan;

use App\Events\Subscription\SubscriptionCancelled;
use App\Events\Subscription\SubscriptionCreated;
use App\Events\Subscription\SubscriptionExpired;
use App\Events\Subscription\SubscriptionExtended;
use App\Events\Subscription\SubscriptionManuallyChanged;
use App\Events\Subscription\SubscriptionRenewed;
use App\Events\Subscription\SubscriptionUpgraded;
use App\Exceptions\Subscription\SubscriptionIsAlreadyCancelled;
use App\Exceptions\Subscription\SubscriptionIsAlreadyExpired;
use App\Exceptions\Subscription\UserAlreadySubscribedException;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

class SubscriptionService
{
    public function __construct(
        protected SubscriptionHistoryService $subscriptionHistoryService,
        protected ServerPoolService $serverPoolService
    ) {
    }


    /**
     * Create a new subscription for a user.
     * Do not use for upgrades.
     *
     * @param User $user The user to create the subscription for.
     * @param SubscriptionPlan $plan The plan to subscribe to.
     * @param Carbon|null $startDate The start date of the subscription. If null, current date will be used.
     * @param string|null $notes Optional notes to add to the subscription history.
     *
     * @throws \App\Exceptions\UserAlreadySubscribedException
     * @throws \Exception
     */
    public function createSubscription(User $user, SubscriptionPlan $plan, ?Carbon $startDate, ?string $notes = null): Subscription
    {
        // log the result
        Log::info('Creating subscription for user ' . $user->id . ' on plan ' . $plan->id);

        // check if user already has an active subscription
        if ($user->currentSubscription) {
            throw new UserAlreadySubscribedException('Failed to create subscription. User already has an active subscription: #' . $user->currentSubscription->id);
        }

        $subscription = null;

        try {
            $subscription = DB::transaction(function () use ($user, $plan, $startDate, $notes) {
                // create the subscription
                if (! $startDate) {
                    $startDate = now();
                }
                $endDate = $plan->calculateEndDate($startDate);
                // create the subscription, will return exception if fails
                $subscription = Subscription::create([
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                    'start_date' => $startDate,
                    'end_date' => $endDate, // if null - subscription will be considered infinite
                    'notes' => $notes,
                ]);

                // record creation in history
                $this->subscriptionHistoryService->recordCreation($subscription, $notes);
                // assign user to a pool
                if (! $subscription->user->serverPool) {
                    $this->serverPoolService->autoAssignUserToPool($subscription->user);
                }

                return $subscription;
            });

        } catch (Exception $e) {
            Log::error('Error creating subscription for user ' . $user->id . ' on plan ' . $plan->id . ': ' . $e->getMessage());
            throw $e;
        }

        if ($subscription) {
            Log::info('Subscription created for user ' . $user->id . ' on plan ' . $plan->id . ' subscription ' . $subscription->id);
        }

        // dispatch events
        SubscriptionCreated::dispatch($subscription);

        return $subscription;
    }


    /**
     * Renew a subscription.
     * Can be used to renew a subscription based on current plan.
     *
     * @param Subscription $subscription The subscription to renew.
     *
     * @throws \Exception
     */
    public function renewSubscription(Subscription $subscription, ?string $notes = null): Subscription
    {
        // log the result
        Log::info('Renewing subscription ' . $subscription->id . ' for user ' . $subscription->user->id);

        try {
            $subscription = DB::transaction(function () use ($subscription, $notes) {
                // calculate new end date
                $newEndDate = $subscription->plan->calculateEndDate($subscription->end_date->copy());
                // renew the subscription data
                $subscription->renewTo($newEndDate);
                // record renewal in history
                $this->subscriptionHistoryService->recordRenewal($subscription, $notes);
                // check pool
                if (! $subscription->user->serverPool) {
                    $this->serverPoolService->autoAssignUserToPool($subscription->user);
                }

                return $subscription;
            });

        } catch (Exception $e) {
            Log::error('Error renewing subscription ' . $subscription->id . ' for user ' . $subscription->user->id . ': ' . $e->getMessage());
            throw $e;
        }

        Log::info('Subscription renewed for user ' . $subscription->user->id . ' subscription ' . $subscription->id);
        // dispatch events
        SubscriptionRenewed::dispatch($subscription);

        return $subscription;
    }


    /**
     * Extend a subscription.
     * Can be used to extend a subscription by a certain duration ex. by admin or by custom action via api.
     *
     * @param int $deltaDuration The duration to extend the subscription by.
     * @param string $deltaDurationUnits The unit of the duration.
     * @see \App\Models\SubscriptionPlan::DURATION_UNITS for possible values.
     *
     * @throws \InvalidArgumentException
     * @throws \Exception
     */
    public function extendSubscription(Subscription $subscription, int $deltaDuration, string $deltaDurationUnits, ?string $notes = null): Subscription
    {
        if (! in_array($deltaDurationUnits, SubscriptionPlan::DURATION_UNITS)) {
            throw new InvalidArgumentException("Invalid duration unit: {$deltaDurationUnits}");
        }

        if ($deltaDuration <= 0) {
            throw new InvalidArgumentException("Invalid duration: {$deltaDuration}");
        }

        // log the result
        Log::info('Extending subscription ' . $subscription->id . ' for user ' . $subscription->user->id);

        try {
            $subscription = DB::transaction(function () use ($subscription, $deltaDuration, $deltaDurationUnits, $notes) {
                // calculate new end date
                $baseDate = $subscription->end_date ?? now();
                $newEndDate = $baseDate->copy()->add($deltaDurationUnits, $deltaDuration);
                // update the subscription
                $subscription->extendTo($newEndDate);
                // record extension in history
                $this->subscriptionHistoryService->recordExtension($subscription, $deltaDuration, $deltaDurationUnits, $notes);
                // check pool
                if (! $subscription->user->serverPool) {
                    $this->serverPoolService->autoAssignUserToPool($subscription->user);
                }

                return $subscription;
            });

        } catch (Exception $e) {
            Log::error('Error extending subscription ' . $subscription->id . ' for user ' . $subscription->user->id . ': ' . $e->getMessage());
            throw $e;
        }

        Log::info('Subscription extended for user ' . $subscription->user->id . ' subscription ' . $subscription->id);
        // dispatch events
        SubscriptionExtended::dispatch($subscription);

        return $subscription;
    }


    /**
     * Upgrade a subscription to a new plan.
     * Can be used to upgrade a subscription to a new plan. After a user payed successfully
     *
     * @param Subscription $subscription The subscription to upgrade.
     * @param SubscriptionPlan $newPlan The new plan to upgrade to.
     * @param string|null $notes Optional notes to add to the subscription history.
     *
     * @throws \Exception
     */
    public function upgradeSubscription(Subscription $subscription, SubscriptionPlan $newPlan, ?string $notes = null): Subscription
    {
        // log the result
        Log::info('Upgrading subscription ' . $subscription->id . ' for user ' . $subscription->user->id);

        try {
            $subscription = DB::transaction(function () use ($subscription, $newPlan, $notes) {
                // calculate new end date
                $newEndDate = $newPlan->calculateEndDate($subscription->end_date->copy());
                $subscription->changePlan($newPlan, $newEndDate);
                // record upgrade in history
                $this->subscriptionHistoryService->recordUpgrade($subscription, $notes);
                // upgrade user
                $subscription->user->upgradeSubscription($newPlan);
                // check pool
                if (! $subscription->user->serverPool) {
                    $this->serverPoolService->autoAssignUserToPool($subscription->user);
                }

                return $subscription->fresh();
            });

        } catch (Exception $e) {
            Log::error('Error upgrading subscription ' . $subscription->id . ' for user ' . $subscription->user->id . ': ' . $e->getMessage());
            throw $e;
        }

        Log::info('Subscription upgraded for user ' . $subscription->user->id . ' subscription ' . $subscription->id);
        // dispatch events
        SubscriptionUpgraded::dispatch($subscription);

        return $subscription;
    }

    /**
     * Cancel a subscription.
     * Can be used to cancel a subscription. After a user requested to cancel their subscription.
     *
     * @param Subscription $subscription The subscription to cancel.
     * @param string|null $notes Optional notes to add to the subscription history.
     *
     * @throws \Exception
     * @throws \App\Exceptions\Subscription\SubscriptionIsAlreadyCancelled
     * @throws \App\Exceptions\Subscription\SubscriptionIsAlreadyExpired
     */
    public function cancelSubscription(Subscription $subscription, ?string $notes = null): Subscription
    {
        // log the result
        Log::info('Cancelling subscription ' . $subscription->id . ' for user ' . $subscription->user->id);

        if ($subscription->isCancelled()) {
            throw new SubscriptionIsAlreadyCancelled('Subscription #' . $subscription->id . ' is already cancelled');
        }

        if ($subscription->isExpired()) {
            throw new SubscriptionIsAlreadyExpired('Subscription #' . $subscription->id . ' is already expired');
        }

        try {
            $subscription = DB::transaction(function () use ($subscription, $notes) {
                // cancel the subscription
                $subscription->cancel();
                // record cancellation in history
                $this->subscriptionHistoryService->recordCancellation($subscription, $notes);

                // TODO: consider moving to queue
                // if (! $subscription->user->serverPool) {
                //     $this->serverPoolService->releaseUserFromPool($subscription->user);
                // }
                return $subscription->fresh();
            });

        } catch (Exception $e) {
            Log::error('Error cancelling subscription ' . $subscription->id . ' for user ' . $subscription->user->id . ': ' . $e->getMessage());
            throw $e;
        }

        Log::info('Subscription cancelled for user ' . $subscription->user->id . ' subscription ' . $subscription->id);
        // dispatch events
        SubscriptionCancelled::dispatch($subscription);

        return $subscription;
    }

    /**
     * Expire a subscription.
     * Can be used to expire a subscription. After a user requested to expire their subscription.
     *
     * @param Subscription $subscription The subscription to expire.
     * @param string|null $notes Optional notes to add to the subscription history.
     *
     * @throws \Exception
     * @throws \App\Exceptions\Subscription\SubscriptionIsAlreadyCancelled
     * @throws \App\Exceptions\Subscription\SubscriptionIsAlreadyExpired
     */
    public function expireSubscription(Subscription $subscription, ?string $notes = null): Subscription
    {
        // log the result
        Log::info('Expiring subscription ' . $subscription->id . ' for user ' . $subscription->user->id);

        if ($subscription->isExpired()) {
            throw new SubscriptionIsAlreadyExpired('Subscription #' . $subscription->id . ' is already expired');
        }

        if ($subscription->isCancelled()) {
            throw new SubscriptionIsAlreadyCancelled('Subscription #' . $subscription->id . ' is already cancelled');
        }

        try {
            $subscription = DB::transaction(function () use ($subscription, $notes) {
                // expire the subscription
                $subscription->expire();
                // record expiration in history
                $this->subscriptionHistoryService->recordExpiration($subscription, $notes);

                // TODO: consider moving to queue
                // if (! $subscription->user->serverPool) {
                //     $this->serverPoolService->releaseUserFromPool($subscription->user);
                // }
                return $subscription->fresh();
            });

        } catch (Exception $e) {
            Log::error('Error expiring subscription ' . $subscription->id . ' for user ' . $subscription->user->id . ': ' . $e->getMessage());
            throw $e;
        }

        Log::info('Subscription expired for user ' . $subscription->user->id . ' subscription ' . $subscription->id);
        // dispatch events
        SubscriptionExpired::dispatch($subscription);

        return $subscription;
    }


    /**
     * Manually extend a subscription.
     *
     * @param Subscription $subscription
     * @param int $deltaDuration
     * @param string $deltaDurationUnits Allowed units: minute, hour, day, week, month, year
     * @param string|null $notes Optional user-visible notes
     * @param string|null $adminNotes Optional internal admin notes
     *
     * @return Subscription
     *
     * @throws \Throwable
     * @throws \InvalidArgumentException
     */
    public function manualExtendSubscription(
        Subscription $subscription,
        int $deltaDuration,
        string $deltaDurationUnits = SubscriptionPlan::DEFAULT_DURATION_UNIT,
        ?string $notes = null,
        ?string $adminNotes = null
    ): Subscription {
        Log::info("Manually extending subscription {$subscription->id} for user {$subscription->user->id}");

        if (! in_array($deltaDurationUnits, SubscriptionPlan::DURATION_UNITS)) {
            throw new InvalidArgumentException("Invalid duration unit: {$deltaDurationUnits}");
        }

        if ($deltaDuration <= 0) {
            throw new InvalidArgumentException("Invalid duration: {$deltaDuration}");
        }

        try {
            $subscription = DB::transaction(function () use (
                $subscription,
                $deltaDuration,
                $deltaDurationUnits,
                $notes,
                $adminNotes
            ) {
                $baseDate = $subscription->end_date ?? now();
                $newEndDate = $baseDate->copy()->add($deltaDurationUnits, $deltaDuration);
                $subscription->changeEndDate($newEndDate);

                $finalNotes = $notes ?? "Manual extension by {$deltaDuration} {$deltaDurationUnits}, until " . $newEndDate->format('d M Y, H:i:s');

                $this->subscriptionHistoryService->recordManualExtension(
                    $subscription,
                    $deltaDuration,
                    $deltaDurationUnits,
                    $finalNotes,
                    $adminNotes
                );

                if (! $subscription->user->serverPool) {
                    $this->serverPoolService->autoAssignUserToPool($subscription->user);
                }

                return $subscription;
            });

        } catch (\Throwable $e) {
            Log::error("Error manually extending subscription {$subscription->id} for user {$subscription->user->id}: {$e->getMessage()}");
            throw $e;
        }

        Log::info("Subscription manually extended for user {$subscription->user->id}, subscription {$subscription->id}");
        SubscriptionManuallyChanged::dispatch($subscription);

        return $subscription;
    }

    /**
     * Manually upgrade a subscription.
     *
     * @param Subscription $subscription
     * @param SubscriptionPlan $newPlan
     * @param Carbon|null $newEndDate
     * @param string|null $notes
     * @param string|null $adminNotes
     *
     * @return Subscription
     *
     * @throws \Throwable
     */
    public function manualUpgradeSubscription(
        Subscription $subscription,
        SubscriptionPlan $newPlan,
        ?Carbon $newEndDate = null,
        ?string $notes = null,
        ?string $adminNotes = null
    ): Subscription {
        Log::info("Manually upgrading subscription {$subscription->id} for user {$subscription->user->id}");

        try {
            $subscription = DB::transaction(function () use (
                $subscription,
                $newPlan,
                $newEndDate,
                $notes,
                $adminNotes
            ) {
                $oldEndDate = $subscription->end_date;
                $newEndDate = $newEndDate ?? $newPlan->calculateEndDate($oldEndDate);

                $subscription->changePlan($newPlan, $newEndDate); // ранее upgrade()

                $finalNotes = $notes ?? "Manual upgrade to plan '{$newPlan->name}'";

                if ($oldEndDate && $newEndDate->gt($oldEndDate)) {
                    $finalNotes .= ', extended to ' . $newEndDate->toDateTimeString();
                }

                $this->subscriptionHistoryService->recordManualUpgrade(
                    $subscription,
                    $finalNotes,
                    $adminNotes
                );

                if (! $subscription->user->serverPool) {
                    $this->serverPoolService->autoAssignUserToPool($subscription->user);
                }

                return $subscription;
            });

        } catch (\Throwable $e) {
            Log::error("Error manually upgrading subscription {$subscription->id} for user {$subscription->user->id}: {$e->getMessage()}");
            throw $e;
        }

        Log::info("Subscription manually upgraded for user {$subscription->user->id}, subscription {$subscription->id}");
        SubscriptionManuallyChanged::dispatch($subscription);

        return $subscription;
    }


    /**
     * Manually cancel a subscription.
     *
     * Can be used by admin or API to cancel an active subscription manually.
     *
     * @param Subscription $subscription
     * @param string|null $notes Optional user-visible notes
     * @param string|null $adminNotes Optional internal admin notes
     *
     * @return Subscription
     *
     * @throws \Throwable
     */
    public function manualCancelSubscription(
        Subscription $subscription,
        ?string $notes = null,
        ?string $adminNotes = null
    ): Subscription {
        Log::info("Manually cancelling subscription {$subscription->id} for user {$subscription->user->id}");

        if ($subscription->isCancelled()) {
            throw new SubscriptionIsAlreadyCancelled("Subscription #{$subscription->id} is already cancelled.");
        }

        if ($subscription->isExpired()) {
            throw new SubscriptionIsAlreadyExpired("Subscription #{$subscription->id} is already expired.");
        }

        try {
            $subscription = DB::transaction(function () use (
                $subscription,
                $notes,
                $adminNotes
            ) {
                // Cancel the subscription
                $subscription->cancel();

                // Default notes if not provided
                $finalNotes = $notes ?? 'Manually cancelled by administrator';

                // Record cancellation in history
                $this->subscriptionHistoryService->recordManualCancellation(
                    $subscription,
                    $finalNotes,
                    $adminNotes
                );

                // TODO: consider moving to queue
                // if (! $subscription->user->serverPool) {
                //     $this->serverPoolService->releaseUserFromPool($subscription->user);
                // }

                return $subscription;
            });

        } catch (\Throwable $e) {
            Log::error("Error manually cancelling subscription {$subscription->id} for user {$subscription->user->id}: {$e->getMessage()}");
            throw $e;
        }

        Log::info("Subscription manually cancelled for user {$subscription->user->id}, subscription {$subscription->id}");
        SubscriptionManuallyChanged::dispatch($subscription);

        return $subscription;
    }

    /**
     * Manually renew a subscription.
     *
     * Used by admin or API to manually renew a subscription based on its current plan.
     *
     * @param Subscription $subscription
     * @param string|null $notes Optional user-visible notes
     * @param string|null $adminNotes Optional internal admin notes
     *
     * @return Subscription
     *
     * @throws \Throwable
     */
    public function manualRenewSubscription(
        Subscription $subscription,
        ?string $notes = null,
        ?string $adminNotes = null
    ): Subscription {
        Log::info("Manually renewing subscription {$subscription->id} for user {$subscription->user->id}");

        try {
            $subscription = DB::transaction(function () use (
                $subscription,
                $notes,
                $adminNotes
            ) {
                $oldEndDate = $subscription->end_date->copy();
                $newEndDate = $subscription->plan->calculateEndDate($oldEndDate);
                // Обновляем дату окончания
                $subscription->changeEndDate($newEndDate);
                $finalNotes = $notes ?? "Manually renewed until " . $newEndDate->format('d M Y, H:i:s');

                // Запись в историю
                $this->subscriptionHistoryService->recordManualRenewal(
                    $subscription,
                    $finalNotes,
                    $adminNotes
                );

                // Назначение пула (если отсутствует)
                if (! $subscription->user->serverPool) {
                    $this->serverPoolService->autoAssignUserToPool($subscription->user);
                }
                return $subscription;
            });

            SubscriptionManuallyChanged::dispatch($subscription);

            Log::info("Subscription manually renewed for user {$subscription->user->id}, subscription {$subscription->id}");

            return $subscription;

        } catch (\Throwable $e) {
            Log::error("Error manually renewing subscription {$subscription->id} for user {$subscription->user->id}: {$e->getMessage()}");
            throw $e;
        }
    }
    // manual expiration

    public function manualExpireSubscription(
        Subscription $subscription,
        ?string $notes = null,
        ?string $adminNotes = null
    ): Subscription {
        Log::info("Manually expiring subscription {$subscription->id} for user {$subscription->user->id}");

        try {
            $subscription = DB::transaction(function () use (
                $subscription,
                $notes,
                $adminNotes
            ) {
                // Expire the subscription
                $subscription->expire();
                // Default notes if not provided
                $finalNotes = $notes ?? 'Manually expired by administrator';
                // Record expiration in history
                $this->subscriptionHistoryService->recordManualExpiration(
                    $subscription,
                    $finalNotes,
                    $adminNotes
                );

                // TODO: consider moving to queue
                // if (! $subscription->user->serverPool) {
                //     $this->serverPoolService->releaseUserFromPool($subscription->user);
                // }
                return $subscription;
            });

        } catch (\Throwable $e) {
            Log::error("Error manually expiring subscription {$subscription->id} for user {$subscription->user->id}: {$e->getMessage()}");
            throw $e;
        }

        Log::info("Subscription manually expired for user {$subscription->user->id}, subscription {$subscription->id}");
        SubscriptionManuallyChanged::dispatch($subscription);

        return $subscription;
    }
}
