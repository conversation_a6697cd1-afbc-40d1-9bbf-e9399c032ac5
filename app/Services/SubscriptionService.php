<?php

namespace App\Services;

use App\Models\Subscription;
use App\Models\User;
use App\Models\SubscriptionPlan;

use App\Events\Subscription\SubscriptionCancelled;
use App\Events\Subscription\SubscriptionCreated;
use App\Events\Subscription\SubscriptionExpired;
use App\Events\Subscription\SubscriptionExtended;
use App\Events\Subscription\SubscriptionManuallyChanged;
use App\Events\Subscription\SubscriptionRenewed;
use App\Events\Subscription\SubscriptionUpgraded;

use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SubscriptionService
{
    public function __construct(
        protected SubscriptionHistoryService $subscriptionHistoryService,
        protected ServerPoolService $serverPoolService
    )
    {}

    /**
     * Create a new subscription for a user.
     * Can be used for both new subscriptions and subscription upgrades.
     */
    public function createSubscription(User $user, SubscriptionPlan $plan, ?Carbon $startDate, ?string $notes = null): Subscription
    {
        // log the result
        Log::info('Creating subscription for user ' . $user->id . ' on plan ' . $plan->id);

        try {

            DB::startTransaction();

            if (! $startDate) {
                $startDate = now();
            }

            $endDate = $plan->calculateEndDate($startDate);

            // cancel the current subscription if it exists
            if ($user->currentSubscription) {
                throw new Exception('User already has an active subscription');
            }

            // create the subscription
            $subscription = Subscription::create([
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'notes' => $notes,
            ]);

            if (! $subscription) {
                throw new Exception('Failed to create subscription');
            }

            Log::success('Subscription created for user ' . $user->id . ' on plan ' . $plan->id . ' subscription ' . $subscription->id);

            // record creation in history
            $this->subscriptionHistoryService->recordCreation($subscription, $notes);

            // assign user to a pool
            $this->serverPoolService->autoAssignUserToPool($user);

            DB::commit();

            // dispatch events
            SubscriptionCreated::dispatch($subscription);

        } catch (Exception $e)
        {
            DB::rollBack();
            Log::error('Error creating subscription for user ' . $user->id . ' on plan ' . $plan->id . ': ' . $e->getMessage());
            throw $e;
        }

        return $subscription;
    }

    /**
     * Renew a subscription.
     * Can be used to renew a subscription based on current plan.
     */
    public function renewSubscription(Subscription $subscription, ?string $notes = null): Subscription
    {
        // log the result
        Log::info('Renewing subscription ' . $subscription->id . ' for user ' . $subscription->user->id);

        try {

            DB::startTransaction();

            // calculate new end date
            $newEndDate = $subscription->plan->calculateEndDate($subscription->end_date->copy());

            // update the subscription
            $subscription->changeEndDate($newEndDate);

            if (! $subscription) {
                throw new Exception('Failed to renew subscription');
            }

            Log::success('Subscription renewed for user ' . $subscription->user->id . ' subscription ' . $subscription->id);

            // record renewal in history
            $this->subscriptionHistoryService->recordRenewal($subscription, $notes);

            DB::commit();

        } catch (Exception $e)
        {
            DB::rollBack();
            Log::error('Error renewing subscription ' . $subscription->id . ' for user ' . $subscription->user->id . ': ' . $e->getMessage());
            throw $e;
        }

        // dispatch events
        SubscriptionRenewed::dispatch($subscription);

        return $subscription;
    }


    /**
     * Extend a subscription.
     * Can be used to extend a subscription by a certain duration ex. by admin or by custom action via api.
     */
    public function extendSubscription(Subscription $subscription, int $deltaDuration, string $deltaDurationUnits, ?string $notes = null): Subscription
    {
        // log the result
        Log::info('Extending subscription ' . $subscription->id . ' for user ' . $subscription->user->id);

        try {

            DB::startTransaction();

            // calculate new end date
            $newEndDate = $subscription->end_date->copy()->add($deltaDurationUnits, $deltaDuration);

            // update the subscription
            $subscription->changeEndDate($newEndDate);

            if (! $subscription) {
                throw new Exception('Failed to extend subscription');
            }

            Log::success('Subscription extended for user ' . $subscription->user->id . ' subscription ' . $subscription->id);

            // record extension in history
            $this->subscriptionHistoryService->recordExtension($subscription, $deltaDuration, $deltaDurationUnits, $notes);

            // check pool
            if (! $subscription->user->serverPool) {
                $this->serverPoolService->autoAssignUserToPool($subscription->user);
            }


            DB::commit();

        } catch (Exception $e)
        {
            DB::rollBack();
            Log::error('Error extending subscription ' . $subscription->id . ' for user ' . $subscription->user->id . ': ' . $e->getMessage());
            throw $e;
        }

        // dispatch events
        SubscriptionExtended::dispatch($subscription);

        return $subscription;
    }

    /**
     * Upgrade a subscription to a new plan.
     * Can be used to upgrade a subscription to a new plan. After a user payed successfully, the payment service (e.g. Stripe or Paddle) will call this method.
     */
    public function upgradeSubscription(Subscription $subscription, SubscriptionPlan $newPlan, ?string $notes = null): Subscription
    {
        // log the result
        Log::info('Upgrading subscription ' . $subscription->id . ' for user ' . $subscription->user->id);

        try {

            DB::startTransaction();

            // calculate new end date
            $newEndDate = $newPlan->calculateEndDate($subscription->end_date->copy());

            // update the subscription
            $subscription->upgrade($newPlan, $newEndDate);

            if (! $subscription) {
                throw new Exception('Failed to upgrade subscription');
            }

            Log::success('Subscription upgraded for user ' . $subscription->user->id . ' subscription ' . $subscription->id);

            // record upgrade in history
            $this->subscriptionHistoryService->recordUpgrade($subscription, $notes);

            $subscription->user->upgradeSubscription($newPlan);

            DB::commit();

            // dispatch events
            SubscriptionUpgraded::dispatch($subscription);

        } catch (Exception $e)
        {
            DB::rollBack();
            Log::error('Error upgrading subscription ' . $subscription->id . ' for user ' . $subscription->user->id . ': ' . $e->getMessage());
            throw $e;
        }

        return $subscription;
    }

    /**
     * Cancel a subscription.
     * Can be used to cancel a subscription. After a user requested to cancel their subscription, the payment service (e.g. Stripe or Paddle) will call this method.
     */
    public function cancelSubscription(Subscription $subscription, ?string $notes = null): Subscription
    {
        // log the result
        Log::info('Cancelling subscription ' . $subscription->id . ' for user ' . $subscription->user->id);

        try {

            DB::startTransaction();

            // cancel the subscription
            $subscription->cancel();

            if (! $subscription) {
                throw new Exception('Failed to cancel subscription');
            }

            Log::success('Subscription cancelled for user ' . $subscription->user->id . ' subscription ' . $subscription->id);

            // record cancellation in history
            $this->subscriptionHistoryService->recordCancellation($subscription, $notes);

            DB::commit();

            // dispatch events
            SubscriptionCancelled::dispatch($subscription);

        } catch (Exception $e)
        {
            DB::rollBack();
            Log::error('Error cancelling subscription ' . $subscription->id . ' for user ' . $subscription->user->id . ': ' . $e->getMessage());
            throw $e;
        }

        return $subscription;
    }

    /**
     * Expire a subscription.
     * Can be used to expire a subscription. After a user requested to expire their subscription, the payment service (e.g. Stripe or Paddle) will call this method.
     */
    public function expireSubscription(Subscription $subscription, ?string $notes = null): Subscription
    {
        // log the result
        Log::info('Expiring subscription ' . $subscription->id . ' for user ' . $subscription->user->id);

        try {

            DB::startTransaction();

            // expire the subscription
            $subscription->expire();

            if (! $subscription) {
                throw new Exception('Failed to expire subscription');
            }

            Log::success('Subscription expired for user ' . $subscription->user->id . ' subscription ' . $subscription->id);

            // record expiration in history
            $this->subscriptionHistoryService->recordExpiration($subscription, $notes);

            DB::commit();

            // dispatch events
            SubscriptionExpired::dispatch($subscription);

        } catch (Exception $e)
        {
            DB::rollBack();
            Log::error('Error expiring subscription ' . $subscription->id . ' for user ' . $subscription->user->id . ': ' . $e->getMessage());
            throw $e;
        }

        return $subscription;
    }

    /**
     * Manually extend a subscription.
     */
    public function manualExtendSubscription(Subscription $subscription, int $deltaDuration, string $deltaDurationUnits = 'days', ?string $notes = null, ?string $adminNotes = null): Subscription
    {
        Log::info("Manually extending subscription {$subscription->id} for user {$subscription->user->id}");

        try {
            DB::startTransaction();

            $newEndDate = $subscription->end_date->copy()->add($deltaDuration, $deltaDurationUnits);
            $subscription->changeEndDate($newEndDate);

            $notes = $notes ?? "Manual extension by {$deltaDuration} {$deltaDurationUnits}, until " . $newEndDate->format('d M Y, H:i:s');

            $this->subscriptionHistoryService->recordManualExtension(
                $subscription,
                $deltaDuration,
                $deltaDurationUnits,
                $notes,
                $adminNotes
            );

            DB::commit();

            SubscriptionManuallyChanged::dispatch($subscription);

            Log::success("Subscription manually extended for user {$subscription->user->id}, subscription {$subscription->id}");

            return $subscription;

        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error("Error manually extending subscription {$subscription->id} for user {$subscription->user->id}: {$e->getMessage()}");
            throw $e;
        }
    }

    public function manualUpgradeSubscription(Subscription $subscription, SubscriptionPlan $newPlan, ?Carbon $newEndDate = null, ?string $notes = null, ?string $adminNotes = null): Subscription
    {
        Log::info("Manually upgrading subscription {$subscription->id} for user {$subscription->user->id}");

        try {
            DB::startTransaction();

            $oldEndDate = $subscription->end_date;
            $newEndDate = $newEndDate ?? $newPlan->calculateEndDate($oldEndDate);

            $subscription->upgrade($newPlan, $newEndDate);

            $notes = $notes ?? "Manual upgrade to plan '{$newPlan->name}'";

            if ($newEndDate->gt($oldEndDate)) {
                $notes .= ', extended to ' . $newEndDate->format('d M Y, H:i:s');
            }

            $this->subscriptionHistoryService->recordManualUpgrade(
                $subscription,
                $notes,
                $adminNotes
            );

            DB::commit();

            SubscriptionManuallyChanged::dispatch($subscription);

            Log::success("Subscription manually upgraded for user {$subscription->user->id}, subscription {$subscription->id}");

            return $subscription;

        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error("Error manually upgrading subscription {$subscription->id} for user {$subscription->user->id}: {$e->getMessage()}");
            throw $e;
        }
    }
}
