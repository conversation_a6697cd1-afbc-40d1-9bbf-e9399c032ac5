<?php

namespace App\Services;

use App\Models\Subscription;
use App\Models\User;
use App\Models\SubscriptionPlan;

use App\Events\Subscription\SubscriptionCancelled;
use App\Events\Subscription\SubscriptionCreated;
use App\Events\Subscription\SubscriptionExpired;
use App\Events\Subscription\SubscriptionExtended;
use App\Events\Subscription\SubscriptionManuallyChanged;
use App\Events\Subscription\SubscriptionUpgraded;

use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SubscriptionService
{
    public function __construct(
        protected SubscriptionHistoryService $subscriptionHistoryService,
        protected ServerPoolService $serverPoolService
    )
    {}

    /**
     * Create a new subscription for a user.
     * Can be used for both new subscriptions and subscription upgrades. After user payed for a new subscription, the payment service (e.g. Stripe or Paddle) will call this method.
     */
    public function createSubscription(User $user, SubscriptionPlan $plan, ?Carbon $startDate, ?string $notes = null): Subscription
    {
        // log the result
        Log::info('Creating subscription for user ' . $user->id . ' on plan ' . $plan->id);

        try {

            DB::startTransaction();

            if (! $startDate) {
                $startDate = now();
            }

            $endDate = $plan->calculateEndDate($startDate);

            // cancel the current subscription if it exists
            if ($user->currentSubscription) {
                // record expiration in history
                $this->subscriptionHistoryService->recordExpiration($user->subscription, 'Subscription expired due to new subscription creation.');
                // cancel the subscription
                $user->subscription->cancel();
            }

            // create the subscription
            $subscription = Subscription::create([
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'notes' => $notes,
            ]);

            if (! $subscription) {
                throw new Exception('Failed to create subscription');
            }

            Log::success('Subscription created for user ' . $user->id . ' on plan ' . $plan->id . ' subscription ' . $subscription->id);

            // record creation in history
            $this->subscriptionHistoryService->recordCreation($subscription, $notes);

            // assign user to a pool
            $this->serverPoolService->autoAssignUserToPool($user);

            DB::commit();

            // dispatch events
            SubscriptionCreated::dispatch($subscription);

        } catch (Exception $e)
        {
            DB::rollBack();
            Log::error('Error creating subscription for user ' . $user->id . ' on plan ' . $plan->id . ': ' . $e->getMessage());
            throw $e;
        }

        return $subscription;
    }


    /**
     * Extend a subscription.
     * Can be used to extend a subscription by a certain duration. After a user payed successfully, the payment service (e.g. Stripe or Paddle) will call this method.
     */
    public function extendSubscription(Subscription $subscription, int $deltaDuration, string $deltaDurationUnits, ?string $notes = null): Subscription
    {
        // log the result
        Log::info('Extending subscription ' . $subscription->id . ' for user ' . $subscription->user->id);

        try {

            DB::startTransaction();

            // calculate new end date
            $newEndDate = $subscription->end_date->add($deltaDuration, $deltaDurationUnits);

            // update the subscription
            $subscription->changeEndDate($newEndDate);

            if (! $subscription) {
                throw new Exception('Failed to extend subscription');
            }

            Log::success('Subscription extended for user ' . $subscription->user->id . ' subscription ' . $subscription->id);

            // record extension in history
            $this->subscriptionHistoryService->recordExtension($subscription, $deltaDuration, $deltaDurationUnits, $notes);

            // check pool
            if (! $subscription->user->serverPool) {
                $this->serverPoolService->autoAssignUserToPool($subscription->user);
            }


            DB::commit();

        } catch (Exception $e)
        {
            DB::rollBack();
            Log::error('Error extending subscription ' . $subscription->id . ' for user ' . $subscription->user->id . ': ' . $e->getMessage());
            throw $e;
        }

        // dispatch events
        SubscriptionExtended::dispatch($subscription);

        return $subscription;
    }

    /**
     * Upgrade a subscription to a new plan.
     * Can be used to upgrade a subscription to a new plan. After a user payed successfully, the payment service (e.g. Stripe or Paddle) will call this method.
     */
    public function upgradeSubscription(Subscription $subscription, SubscriptionPlan $newPlan, ?string $notes = null): Subscription
    {
        // log the result
        Log::info('Upgrading subscription ' . $subscription->id . ' for user ' . $subscription->user->id);

        try {

            DB::startTransaction();

            // calculate new end date
            $newEndDate = $newPlan->calculateEndDate($subscription->end_date);

            // update the subscription
            $subscription->upgrade($newPlan, $newEndDate);

            if (! $subscription) {
                throw new Exception('Failed to upgrade subscription');
            }

            Log::success('Subscription upgraded for user ' . $subscription->user->id . ' subscription ' . $subscription->id);

            // record upgrade in history
            $this->subscriptionHistoryService->recordUpgrade($subscription, $notes);

            $subscription->user->upgradeSubscription($newPlan);

            DB::commit();

            // dispatch events
            SubscriptionUpgraded::dispatch($subscription);

        } catch (Exception $e)
        {
            DB::rollBack();
            Log::error('Error upgrading subscription ' . $subscription->id . ' for user ' . $subscription->user->id . ': ' . $e->getMessage());
            throw $e;
        }

        return $subscription;
    }

    /**
     * Cancel a subscription.
     * Can be used to cancel a subscription. After a user requested to cancel their subscription, the payment service (e.g. Stripe or Paddle) will call this method.
     */
    public function cancelSubscription(Subscription $subscription, ?string $notes = null): Subscription
    {
        // log the result
        Log::info('Cancelling subscription ' . $subscription->id . ' for user ' . $subscription->user->id);

        try {

            DB::startTransaction();

            // cancel the subscription
            $subscription->cancel();

            if (! $subscription) {
                throw new Exception('Failed to cancel subscription');
            }

            Log::success('Subscription cancelled for user ' . $subscription->user->id . ' subscription ' . $subscription->id);

            // record cancellation in history
            $this->subscriptionHistoryService->recordCancellation($subscription, $notes);

            DB::commit();

            // dispatch events
            SubscriptionCancelled::dispatch($subscription);

        } catch (Exception $e)
        {
            DB::rollBack();
            Log::error('Error cancelling subscription ' . $subscription->id . ' for user ' . $subscription->user->id . ': ' . $e->getMessage());
            throw $e;
        }

        return $subscription;
    }

    /**
     * Expire a subscription.
     * Can be used to expire a subscription. After a user requested to expire their subscription, the payment service (e.g. Stripe or Paddle) will call this method.
     */
    public function expireSubscription(Subscription $subscription, ?string $notes = null): Subscription
    {
        // log the result
        Log::info('Expiring subscription ' . $subscription->id . ' for user ' . $subscription->user->id);

        try {

            DB::startTransaction();

            // expire the subscription
            $subscription->update([
                'status' => 'expired',
            ]);

            if (! $subscription) {
                throw new Exception('Failed to expire subscription');
            }

            Log::success('Subscription expired for user ' . $subscription->user->id . ' subscription ' . $subscription->id);

            // record expiration in history
            $this->subscriptionHistoryService->recordExpiration($subscription, $notes);

            DB::commit();

            // dispatch events
            SubscriptionExpired::dispatch($subscription);

        } catch (Exception $e)
        {
            DB::rollBack();
            Log::error('Error expiring subscription ' . $subscription->id . ' for user ' . $subscription->user->id . ': ' . $e->getMessage());
            throw $e;
        }

        return $subscription;
    }

    public function manualChangeSubscription(Subscription $subscription, ?SubscriptionPlan $newPlan = null, ?Carbon $newEndDate = null, ?int $deltaDuration, ?string $deltaDurationUnits = 'days', ?string $notes = null, ?string $adminNotes = null): Subscription
    {
        // log the result
        Log::info('Manually changing subscription ' . $subscription->id . ' for user ' . $subscription->user->id);

        if (! $newEndDate && ! $deltaDuration) {
            throw new Exception('Either new end date or delta duration must be provided');
        }

        try {

            DB::startTransaction();

            $notes = $notes ?? 'Manual change. ';

            // if new plan is provided, upgrade the subscription
            if ($newPlan) {
                // if new end date is not provided, calculate it based on the new plan
                $newEndDate = $newEndDate ?? $newPlan->calculateEndDate($subscription->end_date);
                // upgrade the subscription
                $subscription->upgrade($newPlan, $newEndDate);

                $notes .= ' Plan upgraded to ' . $newPlan->name;
                if ($newEndDate && $newEndDate->diffInDays($subscription->end_date) > 0) {
                    $notes .= ' and end date extended by ' . $newEndDate->diffInDays($subscription->end_date) . ' days, till ' . $newEndDate->format('d M Y, H:i:s');
                } else {
                    $notes .= ' and end date set to ' . $newEndDate->format('d M Y, H:i:s') . ' based on plan duration';
                }
            } else {
                // if new end date is not provided, calculate it based on the delta duration
                $newEndDate = $newEndDate ?? $subscription->end_date->add($deltaDuration, $deltaDurationUnits);
                $subscription->changeEndDate($newEndDate);

                if ($newEndDate && $newEndDate->diffInDays($subscription->end_date) > 0) {
                    $notes .= ' End date extended by ' . $newEndDate->diffInDays($subscription->end_date) . ' days, till ' . $newEndDate->format('d M Y, H:i:s');
                } else {
                    $notes .= ' End date set to ' . $newEndDate->format('d M Y, H:i:s') . ' based on delta duration';
                }

            }

            if (! $subscription) {
                throw new Exception('Failed to manually change subscription');
            }

            Log::success('Subscription manually changed for user ' . $subscription->user->id . ' subscription ' . $subscription->id);

            // record manual change in history
            $this->subscriptionHistoryService->recordManualChange($subscription, $notes, $adminNotes);

            DB::commit();

            // dispatch events
            SubscriptionManuallyChanged::dispatch($subscription);

        } catch (Exception $e)
        {
            DB::rollBack();
            Log::error('Error manually changing subscription ' . $subscription->id . ' for user ' . $subscription->user->id . ': ' . $e->getMessage());
            throw $e;
        }

        return $subscription;
    }
}
