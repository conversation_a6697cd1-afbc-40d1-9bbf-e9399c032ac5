<?php

namespace App\Services;

use App\Models\ServerPool;
use App\Models\User;
use App\Models\UserServerAssignment;
use Illuminate\Support\Facades\DB;

class ServerPoolService
{
    /**
     * Get the first available pool with capacity.
     */
    public function getAvailablePool(): ?ServerPool
    {
        return UserServerAssignment::findBestAvailablePool();
    }

    /**
     * Assign user to a pool.
     */
    public function assignUserToPool(User $user, ServerPool $pool): bool
    {
        // Check if user is already assigned to this pool
        if (UserServerAssignment::isUserAssignedToPool($user->id, $pool->id)) {
            return true;
        }

        try {
            DB::beginTransaction();

            // Check if pool has capacity
            if (! $pool->hasAvailableCapacity()) {
                throw new \Exception('Pool is full');
            }

            // Assign user to pool
            UserServerAssignment::assignUserToPool($user->id, $pool->id);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Auto-assign user to the best available pool.
     */
    public function autoAssignUserToPool(User $user): bool
    {
        try {
            DB::beginTransaction();

            // Auto-assign user to pool
            UserServerAssignment::autoAssignUser($user->id);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Release user from a pool.
     */
    public function releaseUserFromPool(User $user, ServerPool $pool): bool
    {
        try {
            DB::beginTransaction();

            // Release user from pool
            UserServerAssignment::releaseUserFromPool($user->id, $pool->id);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Release user from all pools.
     */
    public function releaseUserFromAllPools(User $user): int
    {
        try {
            DB::beginTransaction();

            // Release user from all pools
            $releasedCount = UserServerAssignment::releaseAllForUser($user->id);

            DB::commit();
            return $releasedCount;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

}
