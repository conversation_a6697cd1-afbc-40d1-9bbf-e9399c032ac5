<?php

namespace App\Services;

use App\DTOs\Xui\CreateVpnClientDTO;
use App\DTOs\Xui\UpdateVpnClientDTO;
use App\DTOs\Xui\UpdateVpnClientOnlyFieldsDTO;
use App\DTOs\Xui\XuiResponseDTO;
use App\Models\XuiServer;
use Exception;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

class VpnXuiServerManager
{
    protected $server;
    protected $xuiManagementService;

    public function __construct(XuiServer $server)
    {
        $this->server = $server;
        $this->xuiManagementService = new XuiManagementService($server);
    }

    public function getServer(): XuiServer
    {
        return $this->server;
    }

    protected function logXuiError(string $method, array $context = [], XuiResponseDTO $response): void
    {
        Log::error("{$method}: XUI error: " . $response->getMessage(), array_merge($context, [
            'response' => $response->getData(),
        ]));
    }

    /**
     * Validate the inboundId.
     */
    protected function validateInboundId(string|int $inboundId): void
    {
        if (empty($inboundId)) {
            throw new InvalidArgumentException(
                '{inboundId} is required as a string or int and cannot be empty. ' . var_export($inboundId, true) . ' is received.'
            );
        }
    }

    /**
     * Validate the clientUuid.
     */
    protected function validateClientUuid(string $clientUuid): void
    {
        if (empty($clientUuid)) {
            throw new InvalidArgumentException(
                '{clientUuid} is required as a string and cannot be empty. ' . var_export($clientUuid, true) . ' is received.'
            );
        }
    }

    /**
     * Validate the expiryTimeMs.
     */
    protected function validateExpiryTimeMs(int $expiryTimeMs): void
    {
        if ($expiryTimeMs < 0) {
            throw new InvalidArgumentException(
                '{expiryTimeMs} is required as an int and cannot be less than 0. ' . var_export($expiryTimeMs, true) . ' is received.'
            );
        }

        if ($expiryTimeMs < 9999999999) {
            throw new InvalidArgumentException(
                '{expiryTimeMs} is required as an int and cannot be in milliseconds ' . var_export($expiryTimeMs, true) . ' is received.'
            );
        }
    }

    /**
     * Validate the trafficLimitInBytes.
     */
    protected function validateTrafficLimitInBytes(int $trafficLimitInBytes): void
    {
        if ($trafficLimitInBytes < 0) {
            throw new InvalidArgumentException(
                '{trafficLimitInBytes} is required as an int and cannot be less than 0. ' . var_export($trafficLimitInBytes, true) . ' is received.'
            );
        }
    }

    /**
     * Create a new VPN Client on the XUI server.
     */
    public function createVpnClient(string | int $inboundId, CreateVpnClientDTO $dto): XuiResponseDTO
    {
        $this->validateInboundId($inboundId);

        $result = $this->xuiManagementService->addNewClient(
            id: $inboundId,

            // data to be created
            uuid: $dto->uuid,
            email: $dto->email,
            subId: $dto->subId,
            tgId: $dto->tgId,
            flow: $dto->flow,
            totalgb: $dto->totalGB,
            eT: $dto->expiryTime,
            limitIp: $dto->limitIp,
            fingerprint: $dto->fingerprint,
            isTrojan: $dto->isTrojan
        );

        $responseDto = XuiResponseDTO::fromXuiResponse($result);

        if (! $responseDto->isSuccess()) {
            $this->logXuiError(__METHOD__, [
                'server_id' => $this->server->id,
                'inbound_id' => $inboundId,
                'client_uuid' => $dto->uuid,
                'email' => $dto->email,
            ], $responseDto);
        }

        return $responseDto;
    }

    /**
     * Update an existing VPN Client on the XUI server.
     */
    public function updateVpnClient(string | int $inboundId, string $clientUuid, UpdateVpnClientDTO $dto): XuiResponseDTO
    {
        $this->validateInboundId($inboundId);
        $this->validateClientUuid($clientUuid);

        // сначала запросим данные по клиенту с сервера xui
        // затем создадим объект данных для изменения, но сначала сравним с текущим клиентом на сервере, если какое-то поле не совпадает с прежним, то заменяем его новые данные
        $existingClient = $this->xuiManagementService->getClientData($inboundId, $clientUuid);
        if (! $existingClient) {
            throw new Exception("Client with uuid {$clientUuid} not found on inbound {$inboundId}");
        }

        // сравним с текущим клиентом на сервере, если какое-то поле не совпадает с прежним, то заменяем его новые данные
        $dataToUpdate = array_merge($existingClient, $dto->toArray());

        $result = $this->xuiManagementService->editClient(
            inboundId: $inboundId,
            clientUuid: $clientUuid,

            enableClient: $dataToUpdate['enable'],
            email: $dataToUpdate['email'],
            uuid: $dataToUpdate['id'],
            isTrojan: $dataToUpdate['isTrojan'],
            totalGB: $dataToUpdate['totalGB'],
            expiryTime: $dataToUpdate['expiryTime'],
            tgId: $dataToUpdate['tgId'],
            subId: $dataToUpdate['subId'],
            limitIp: $dataToUpdate['limitIp'],
            fingerprint: $dataToUpdate['fingerprint'],
            flow: $dataToUpdate['flow']
        );

        $responseDto = XuiResponseDTO::fromXuiResponse($result);

        if (! $responseDto->isSuccess()) {
            $this->logXuiError(__METHOD__, [
                'server_id' => $this->server->id,
                'inbound_id' => $inboundId,
                'client_uuid' => $clientUuid,
                'email' => $dataToUpdate['email'],
            ], $responseDto);
        }

        return $responseDto;
    }

    /**
     * Remove a VPN Client from the XUI server.
     */
    public function removeVpnClient(string | int  $inboundId, string $clientUuid): XuiResponseDTO
    {
        $this->validateInboundId($inboundId);
        $this->validateClientUuid($clientUuid);

        $result = $this->xuiManagementService->removeClient($inboundId, $clientUuid);

        $responseDto = XuiResponseDTO::fromXuiResponse($result);

        if (! $responseDto->isSuccess()) {
            $this->logXuiError(__METHOD__, [
                'server_id' => $this->server->id,
                'inbound_id' => $inboundId,
                'client_uuid' => $clientUuid,
            ], $responseDto);
        }

        return $responseDto;
    }

    /**
     * Update a VPN Client's data on the XUI server.
     */
    public function updateVpnClientData(string | int  $inboundId, string $clientUuid, UpdateVpnClientOnlyFieldsDTO $dto): XuiResponseDTO
    {
        $this->validateInboundId($inboundId);
        $this->validateClientUuid($clientUuid);

        $existingClient = $this->xuiManagementService->getClientData($inboundId, $clientUuid);
        if (! $existingClient) {
            throw new Exception("Client with uuid {$clientUuid} not found on inbound {$inboundId}");
        }

        // сравним с текущим клиентом на сервере, если какое-то поле не совпадает с прежним, то заменяем его новые данные
        $dataToUpdate = array_merge($existingClient, $dto->toArray());

        $result = $this->xuiManagementService->editClient(
            inboundId: $inboundId,
            clientUuid: $clientUuid,

            enableClient: $dataToUpdate['enable'],
            email: $dataToUpdate['email'],
            uuid: $dataToUpdate['id'],
            isTrojan: $dataToUpdate['isTrojan'],
            totalGB: $dataToUpdate['totalGB'],
            expiryTime: $dataToUpdate['expiryTime'],
            tgId: $dataToUpdate['tgId'],
            subId: $dataToUpdate['subId'],
            limitIp: $dataToUpdate['limitIp'],
            fingerprint: $dataToUpdate['fingerprint'],
            flow: $dataToUpdate['flow']
        );

        $responseDto = XuiResponseDTO::fromXuiResponse($result);

        if (! $responseDto->isSuccess()) {
            $this->logXuiError(__METHOD__, [
                'server_id' => $this->server->id,
                'inbound_id' => $inboundId,
                'client_uuid' => $clientUuid,
                'email' => $dataToUpdate['email'],
            ], $responseDto);
        }

        return $responseDto;
    }

    /**
     * Update a VPN Client's expire time and traffic limit on the XUI server.
     */
    public function updateVpnClientExpireTimeAndTrafficLimit(string | int  $inboundId, string $clientUuid, int $expiryTimeMs, int $trafficLimitInBytes): XuiResponseDTO
    {
        $this->validateInboundId($inboundId);
        $this->validateClientUuid($clientUuid);
        $this->validateExpiryTimeMs($expiryTimeMs);
        $this->validateTrafficLimitInBytes($trafficLimitInBytes);

        // создадим DTO с полями, которые нужно обновить
        $dto = new UpdateVpnClientOnlyFieldsDTO([
            'expiryTime' => $expiryTimeMs,
            'totalGB' => $trafficLimitInBytes,
        ]);

        return $this->updateVpnClientData($inboundId, $clientUuid, $dto);
    }

    /**
     * Update a VPN Client's expire time on the XUI server.
     */
    public function updateVpnClientExpireTime(string | int  $inboundId, string $clientUuid, int $expiryTimeMs): XuiResponseDTO
    {
        $this->validateInboundId($inboundId);
        $this->validateClientUuid($clientUuid);
        $this->validateExpiryTimeMs($expiryTimeMs);

        // создадим DTO с полями, которые нужно обновить
        $dto = new UpdateVpnClientOnlyFieldsDTO([
            'expiryTime' => $expiryTimeMs,
        ]);

        return $this->updateVpnClientData($inboundId, $clientUuid, $dto);
    }

    /**
     * Update a VPN Client's traffic limit on the XUI server.
     */
    public function updateVpnClientTrafficLimit(string | int  $inboundId, string $clientUuid, int $trafficLimitInBytes): XuiResponseDTO
    {
        $this->validateInboundId($inboundId);
        $this->validateClientUuid($clientUuid);
        $this->validateTrafficLimitInBytes($trafficLimitInBytes);

        // создадим DTO с полями, которые нужно обновить
        $dto = new UpdateVpnClientOnlyFieldsDTO([
            'totalGB' => $trafficLimitInBytes,
        ]);

        return $this->updateVpnClientData($inboundId, $clientUuid, $dto);
    }

    /**
     * Reset a VPN Client's traffic on the XUI server.
     */
    public function resetVpnClientTrafficUsage(string | int  $inboundId, string $clientUuid): XuiResponseDTO
    {
        $this->validateInboundId($inboundId);
        $this->validateClientUuid($clientUuid);

        $result = $this->xuiManagementService->resetClientTraffic($inboundId, $clientUuid);

        $responseDto = XuiResponseDTO::fromXuiResponse($result);

        if (! $responseDto->isSuccess()) {
            $this->logXuiError(__METHOD__, [
                'server_id' => $this->server->id,
                'inbound_id' => $inboundId,
                'client_uuid' => $clientUuid,
            ], $responseDto);
        }

        return $responseDto;
    }

    /**
     * Get a VPN Client's traffic on the XUI server.
     */
    public function getVpnClientTrafficUsage(string | int  $inboundId, string $clientUuid): XuiResponseDTO
    {
        $this->validateInboundId($inboundId);
        $this->validateClientUuid($clientUuid);

        $result = $this->xuiManagementService->getClientTraffics($inboundId, $clientUuid);

        $responseDto = XuiResponseDTO::fromXuiResponse($result);

        if (! $responseDto->isSuccess()) {
            $this->logXuiError(__METHOD__, [
                'server_id' => $this->server->id,
                'inbound_id' => $inboundId,
                'client_uuid' => $clientUuid,
            ], $responseDto);
        }

        return $responseDto;
    }

    /**
     * Get a VPN Client's data on the XUI server.
     */
    public function getVpnClientData(string | int  $inboundId, string $clientUuid): XuiResponseDTO
    {
        $this->validateInboundId($inboundId);
        $this->validateClientUuid($clientUuid);

        $result = $this->xuiManagementService->getClientData($inboundId, $clientUuid);

        $responseDto = XuiResponseDTO::fromXuiResponse($result);

        if (! $responseDto->isSuccess()) {
            $this->logXuiError(__METHOD__, [
                'server_id' => $this->server->id,
                'inbound_id' => $inboundId,
                'client_uuid' => $clientUuid,
            ], $responseDto);
        }

        return $responseDto;
    }
}
