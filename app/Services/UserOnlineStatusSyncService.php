<?php

namespace App\Services;

use App\Models\XuiServer;
use App\Models\User;
use App\Models\UserOnlineLog;
use App\Jobs\SyncUserOnlineStatusJob;
use App\Models\XuiServerPoolAssignment;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class UserOnlineStatusSyncService
{
    private int $defaultBatchSize;
    private int $onlineThresholdSeconds;
    private int $offlineThresholdSeconds;

    public function __construct()
    {
        $this->defaultBatchSize = config('xui_sync.user_status.default_batch_size', 10);
        $this->onlineThresholdSeconds = config('xui_sync.online_threshold_seconds', 20);
        $this->offlineThresholdSeconds = config('xui_sync.offline_threshold_seconds', 15);
    }

    /**
     * Synchronize user online status with options for async processing and filtering.
     */
    public function syncUserStatus(array $options = []): array
    {
        $async = $options['async'] ?? false;
        $batchSize = $options['batch_size'] ?? $this->defaultBatchSize;
        $serverIds = $options['server_ids'] ?? null;
        $force = $options['force'] ?? false;

        Log::info('Starting user online status synchronization', [
            'async' => $async,
            'batch_size' => $batchSize,
            'server_ids' => $serverIds,
            'force' => $force,
        ]);

        // Get servers to sync user status for
        $servers = $this->getServersToSync($serverIds, $force);

        if ($servers->isEmpty()) {
            Log::info('No servers found for user status synchronization');
            return ['synced' => 0, 'failed' => 0, 'skipped' => 0];
        }

        Log::info("Found {$servers->count()} servers for user status synchronization");

        if ($async) {
            return $this->syncUserStatusAsync($servers, $batchSize);
        } else {
            return $this->syncUserStatusSync($servers, $batchSize);
        }
    }

    /**
     * Get servers that should be synchronized for user status.
     */
    private function getServersToSync(?array $serverIds, bool $force): Collection
    {
        $query = XuiServer::where('is_active', true);

        if ($serverIds) {
            $query->whereIn('id', $serverIds);
        }

        if (!$force) {
            $query->where('auto_sync', true);
        }

        return $query->get();
    }

    /**
     * Synchronize user status asynchronously using jobs.
     */
    private function syncUserStatusAsync(Collection $servers, int $batchSize): array
    {
        $batches = $servers->chunk($batchSize);
        $totalJobs = 0;

        foreach ($batches as $batch) {
            foreach ($batch as $server) {
                SyncUserOnlineStatusJob::dispatch($server->id);
                $totalJobs++;
            }
        }

        Log::info("Dispatched {$totalJobs} user status sync jobs for servers");

        return [
            'synced' => 0,
            'failed' => 0,
            'skipped' => 0,
            'jobs_dispatched' => $totalJobs,
        ];
    }

    /**
     * Synchronize user status synchronously.
     */
    private function syncUserStatusSync(Collection $servers, int $batchSize): array
    {
        $results = ['synced' => 0, 'failed' => 0, 'skipped' => 0];
        $batches = $servers->chunk($batchSize);

        foreach ($batches as $batchIndex => $batch) {
            Log::info("Processing user status batch " . ($batchIndex + 1) . " with {$batch->count()} servers");

            foreach ($batch as $server) {
                try {
                    $syncResult = $this->syncSingleServerUserStatus($server);
                    if ($syncResult) {
                        $results['synced']++;
                    } else {
                        $results['skipped']++;
                    }
                } catch (\Exception $e) {
                    $results['failed']++;
                    Log::error("Failed to sync user status for server {$server->id}: {$e->getMessage()}");
                }
            }
        }

        return $results;
    }

    /**
     * Synchronize user online status for a single server.
     */
    public function syncSingleServerUserStatus(XuiServer $server): bool
    {
        Log::info("Syncing user status for server: {$server->name} (ID: {$server->id})");

        try {
            DB::transaction(function () use ($server) {
                $now = Carbon::now();

                // Get online clients from server
                $onlineClients = $this->getOnlineClientsFromServer($server);

                // Extract emails from online clients
                $onlineEmails = collect($onlineClients ?? [])
                    ->filter()
                    ->map(function ($clientEmail) {
                        // Extract user email from client email (remove inbound suffix)
                        return User::extractEmailFromClientEmail($clientEmail);
                    })
                    ->unique()
                    ->toArray();

                // Update raw clients data in database
                $server->update([
                    'raw_clients_online' => $onlineClients,
                    'raw_clients_online_updated_at' => $now,
                    'clients_online_count' => count($onlineEmails),
                    'clients_online_list' => $onlineEmails,
                ]);

                Log::debug("Updated raw_clients_online for server {$server->id} with " . count($onlineClients) . " clients");

                if ($onlineClients === null) {
                    Log::warning("Could not retrieve online clients for server {$server->id}");
                    return false;
                }

                if (empty($onlineClients)) {
                    // dd("No online clients found for server {$server->id}");
                    Log::debug("No online clients found for server {$server->id}");
                    return;
                }

                if (empty($onlineEmails)) {
                    Log::debug("No valid user emails found in online clients for server {$server->id}");
                    return;
                }

                Log::debug("Found " . count($onlineEmails) . " unique online user emails for server {$server->id}");

                // Update user online status
                $this->updateUserOnlineStatus($server, $onlineEmails, $now);
            });

            Log::info("Successfully synced user status for server {$server->id}");
            return true;

        } catch (\Exception $e) {
            Log::error("Error syncing user status for server {$server->id}: {$e->getMessage()}", [
                'server_id' => $server->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return false;
        }
    }

    /**
     * Get online clients from server.
     */
    private function getOnlineClientsFromServer(XuiServer $server): ?array
    {
        try {
            $serverInfoService = new ServerInfoService($server);
            $onlineClients = $serverInfoService->getOnlineClients();

            return is_array($onlineClients) ? $onlineClients : null;
        } catch (\Exception $e) {
            Log::warning("Failed to get online clients for server {$server->id}: {$e->getMessage()}");
            return null;
        }
    }

    /**
     * Update user online status based on server data.
     */
    private function updateUserOnlineStatus(XuiServer $server, array $onlineEmails, Carbon $now): void
    {

        // Get users from this server's pools only
        $poolUserIds = XuiServerPoolAssignment::getActiveAssignmentsForServer($server->id)
            ->pluck('user_id')
            ->unique()
            ->toArray();

        // dd($onlineClients, $onlineEmails, $poolUserIds, "Found " . count($onlineEmails) . " unique online user emails for server {$server->id}");

        if (empty($poolUserIds)) {
            Log::debug("No users found in server pools for server {$server->id}");
            return;
        }

        // Get online users from server pools
        $onlineUsers = User::whereIn('email', $onlineEmails)
            ->whereIn('id', $poolUserIds)
            ->get()
            ->keyBy('id');

        Log::debug("Found " . $onlineUsers->count() . " online users in server pools for server {$server->id}");

        // === 1. Process ONLINE users ===
        $this->processOnlineUsers($onlineUsers, $server, $now);

        // === 2. Process OFFLINE users ===
        $this->processOfflineUsers($onlineUsers->keys()->toArray(), $poolUserIds, $server, $now);
    }

    /**
     * Get user IDs from server pools.
     */
    private function getServerPoolUserIds(XuiServer $server): array
    {
        return $server->serverPools()
            ->where('server_pools.is_active', true)
            ->whereNull('xui_server_pool_assignments.released_at')
            ->with(['users' => function ($query) {
                $query->whereNull('user_server_assignments.released_at');
            }])
            ->get()
            ->pluck('users')
            ->flatten()
            ->pluck('id')
            ->unique()
            ->toArray();
    }

    /**
     * Process users who are currently online.
     */
    private function processOnlineUsers(Collection $onlineUsers, XuiServer $server, Carbon $now): void
    {
        $onlineUsers->each(function ($user) use ($now, $server) {
            $wasOfflineLongAgo = $user->last_online_at === null ||
                $user->last_online_at->lt($now->subSeconds($this->onlineThresholdSeconds));

            // Only update if user was offline for a while
            if ($wasOfflineLongAgo) {
                $user->updateQuietly(['last_online_at' => $now]);

                UserOnlineLog::create([
                    'user_id' => $user->id,
                    'xui_server_id' => $server->id,
                    'status' => UserOnlineLog::STATUS_ONLINE,
                    'status_timestamp' => $now,
                    'created_at' => $now,
                ]);

                Log::debug("User {$user->id} marked as online on server {$server->id}");
            }
        });
    }

    /**
     * Process users who should be marked as offline.
     */
    private function processOfflineUsers(array $onlineUserIds, array $poolUserIds, XuiServer $server, Carbon $now): void
    {
        User::whereIn('id', $poolUserIds)
            ->whereNotIn('id', $onlineUserIds)
            ->whereNotNull('last_online_at')
            ->where('last_online_at', '<', $now->subSeconds($this->offlineThresholdSeconds))
            ->get()
            ->each(function ($user) use ($now, $server) {
                // Get last log for this user on this server
                $lastLog = UserOnlineLog::where('user_id', $user->id)
                    ->where('xui_server_id', $server->id)
                    ->latest('status_timestamp')
                    ->first();

                if (!$lastLog || $lastLog->status !== UserOnlineLog::STATUS_OFFLINE) {
                    UserOnlineLog::create([
                        'user_id' => $user->id,
                        'xui_server_id' => $server->id,
                        'status' => UserOnlineLog::STATUS_OFFLINE,
                        'status_timestamp' => $user->last_online_at->copy()->addSeconds(10),
                        'created_at' => $now,
                    ]);

                    Log::debug("User {$user->id} marked as offline on server {$server->id}");
                }
            });
    }

    /**
     * Detect and log offline users (for scheduled job).
     */
    public function detectOfflineUsers(): void
    {
        $now = Carbon::now();
        $offlineThreshold = $now->subSeconds($this->offlineThresholdSeconds);

        Log::info('Starting offline user detection');

        $offlineUsers = User::whereNotNull('last_online_at')
            ->where('last_online_at', '<', $offlineThreshold)
            ->whereHas('serverPools', function ($query) {
                $query->whereNull('user_server_assignments.released_at');
            })
            ->get();

        $processedCount = 0;

        foreach ($offlineUsers as $user) {
            // Get user's server pools
            $serverIds = $user->serverPools()
                ->whereNull('user_server_assignments.released_at')
                ->with('servers')
                ->get()
                ->pluck('servers')
                ->flatten()
                ->pluck('id')
                ->unique()
                ->toArray();

            foreach ($serverIds as $serverId) {
                // Check if we already have an offline log for this user on this server
                $lastLog = UserOnlineLog::where('user_id', $user->id)
                    ->where('xui_server_id', $serverId)
                    ->latest('status_timestamp')
                    ->first();

                if (!$lastLog || $lastLog->status !== UserOnlineLog::STATUS_OFFLINE) {
                    UserOnlineLog::create([
                        'user_id' => $user->id,
                        'xui_server_id' => $serverId,
                        'status' => UserOnlineLog::STATUS_OFFLINE,
                        'status_timestamp' => $user->last_online_at->copy()->addSeconds(10),
                        'created_at' => $now,
                    ]);
                    $processedCount++;
                }
            }
        }

        Log::info("Offline detection completed. Processed {$processedCount} offline logs for {$offlineUsers->count()} users");
    }
}
