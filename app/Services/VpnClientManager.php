<?php
// этот класс сервис будет реализовывать работу с vpn аккаунтами на пулах серверов

namespace App\Services;

use App\DTOs\Xui\InboundDTO;
use App\Models\User;
use App\DTOs\Xui\CreateVpnClientDTO;
use App\DTOs\Xui\UpdateVpnClientDTO;

use Exception;
use Illuminate\Support\Facades\Log;

class VpnClientManager
{
    protected $xuiServerManagers = [];
    protected User $user;

    function __construct(?User $user)
    {
        if ($user) {
            $this->setupServerManagers($user);
        }
    }

    /**
     * Setup server managers for a user.
     */
    protected function setupServerManagers(User $user)
    {
        try {
            // save user
            $this->user = $user;

            $servers = [];
            // get all active servers for user from active pools
            $user->serverPools()
                ->where('server_pools.is_active', true)
                ->wherePivotNull('released_at')
                ->each(function ($pool) use (&$servers) {
                    // Get only active servers that are not released from the pool
                    $activeServers = $pool->servers()
                        ->where('xui_servers.is_active', true)
                        ->wherePivotNull('released_at')
                        ->get();

                    $servers = array_merge($servers, $activeServers->all());
                });

            if (! count($servers)) {
                throw new Exception('No servers found for user ' . $user->id);
            }

            // create XUI server managers for all servers
            foreach ($servers as $server)
            {
                if (!isset($this->servers[$server->id])) {
                    $this->xuiServerManagers[$server->id] = new VpnXuiServerManager($server);
                }
            }

        } catch (Exception $e)
        {
            Log::error('Error creating VpnClientManager for user ' . $user->id . ': ' . $e->getMessage());
        }
    }


    /**
     * Create VPN clients for a user on all servers in all pools.
     */
    public function createVpnClientsOnPools(?User $user)
    {
        if ($user) {
            $this->setupServerManagers($user);
        }

        // log the result
        Log::info('Creating VPN clients for user ' . $user->id);

        $clientCreationResults = [];

        /** @var VpnXuiServerManager $manager */
        foreach ($this->xuiServerManagers as $serverId => $manager)
        {
            try {
                $server = $manager->getServer();
                if (! $server->isOnline()) {
                    Log::warning('Server ' . $serverId . ' is not online, skipping');
                    continue;
                }

                if (! $server->isOnline()) {
                    Log::warning('Server ' . $serverId . ' is not online, skipping');
                    continue;
                }

                foreach ($manager->getServer()->inbounds() as /** @var InboundDTO */ $inbound) {
                    // создадим DTO с полями, которые нужно обновить
                    $dto = CreateVpnClientDTO::makeDtoFromUserAndInbound($user, $inbound);
                    $isClientCreated = $manager->createVpnClient($inbound->id, $dto);
                    $clientCreationResults[$serverId][$inbound->id] = $isClientCreated;
                }
            } catch (Exception $e)
            {
                Log::error('Error creating VPN clients for user ' . $user->id . ' on server ' . $serverId . ', inbound ' . $inbound->id . ': ' . $e->getMessage());
            }
        }

        if (count($clientCreationResults)) {
            Log::info('VPN clients created for user ' . $user->id, [
                'results' => $clientCreationResults
            ]);
        } else {
            Log::error('No VPN clients created for user ' . $user->id, [
                'results' => $clientCreationResults
            ]);
        }

        return $clientCreationResults;
    }


    /**
     * Update VPN clients for a user on all servers in all pools.
     */
    public function updateVpnClientsOnPools(?User $user)
    {
        if ($user) {
            $this->setupServerManagers($user);
        }

        // log the result
        Log::info('Updating VPN clients for user ' . $user->id);

        $clientUpdateResults = [];

        /** @var VpnXuiServerManager $manager */
        foreach ($this->xuiServerManagers as $serverId => $manager)
        {
            try {
                foreach ($manager->getServer()->inbounds() as /** @var InboundDTO */ $inbound) {
                    // создадим DTO с полями, которые нужно обновить
                    $dto = UpdateVpnClientDTO::makeDtoFromUserAndInbound($user, $inbound);
                    $isClientUpdated = $manager->updateVpnClient($inbound->id, $user->getClientId(), $dto);
                    $clientUpdateResults[$serverId][$inbound->id] = $isClientUpdated;
                }
            } catch (Exception $e)
            {
                Log::error('Error updating VPN clients for user ' . $user->id . ' on server ' . $serverId . ', inbound ' . $inbound->id . ': ' . $e->getMessage());
            }
        }

        if (count($clientUpdateResults)) {
            Log::info('VPN clients updated for user ' . $user->id, [
                'results' => $clientUpdateResults
            ]);
        } else {
            Log::error('No VPN clients updated for user ' . $user->id, [
                'results' => $clientUpdateResults
            ]);
        }

        return $clientUpdateResults;
    }


    /**
     * Remove VPN clients for a user on all servers in all pools.
     */
    public function removeVpnClientsOnPools(?User $user)
    {
        if ($user) {
            $this->setupServerManagers($user);
        }

        // log the result
        Log::info('Removing VPN clients for user ' . $user->id . ' on pool ' . $user->serverPool->id);

        $clientRemovalResults = [];

        /** @var VpnXuiServerManager $manager */
        foreach ($this->xuiServerManagers as $serverId => $manager)
        {
            try {
                foreach ($manager->getServer()->inbounds() as /** @var InboundDTO */ $inbound) {
                    $isClientRemoved = $manager->removeVpnClient($inbound->id, $user->getClientId());
                    $clientRemovalResults[$serverId][$inbound->id] = $isClientRemoved;
                }
            } catch (Exception $e)
            {
                Log::error('Error removing VPN clients for user ' . $user->id . ' on server ' . $serverId . ', inbound ' . $inbound->id . ': ' . $e->getMessage());
            }
        }

        if (count($clientRemovalResults)) {
            Log::info('VPN clients removed for user ' . $user->id . ' on pool ' . $user->serverPool->id, [
                'results' => $clientRemovalResults
            ]);
        } else {
            Log::error('No VPN clients removed for user ' . $user->id . ' on pool ' . $user->serverPool->id, [
                'results' => $clientRemovalResults
            ]);
        }

        return $clientRemovalResults;
    }


    /**
     * ReCreate VPN clients for a user on all servers in all pools.
     */
    public function recreateVpnClientsOnPools(?User $user)
    {
        if ($user) {
            $this->setupServerManagers($user);
        }

        // log the result
        Log::info('ReCreating VPN clients for user ' . $user->id . ' on pool ' . $user->serverPool->id);

        $clientRecreationResults = [];

        /** @var VpnXuiServerManager $manager */
        foreach ($this->xuiServerManagers as $serverId => $manager)
        {
            try {
                foreach ($manager->getServer()->inbounds() as /** @var InboundDTO */ $inbound) {
                    $isClientRemoved = $manager->removeVpnClient($inbound->id, $user->getClientId());
                    $isClientCreated = $manager->createVpnClient($inbound->id, CreateVpnClientDTO::makeDtoFromUserAndInbound($user, $inbound));
                    $clientRecreationResults[$serverId][$inbound->id] = [
                        'removed' => $isClientRemoved,
                        'created' => $isClientCreated
                    ];
                }
            } catch (Exception $e)
            {
                Log::error('Error reCreating VPN clients for user ' . $user->id . ' on server ' . $serverId . ', inbound ' . $inbound->id . ': ' . $e->getMessage());
            }
        }

        if (count($clientRecreationResults)) {
            Log::info('VPN clients reCreated for user ' . $user->id . ' on pool ' . $user->serverPool->id, [
                'results' => $clientRecreationResults
            ]);
        } else {
            Log::error('No VPN clients reCreated for user ' . $user->id . ' on pool ' . $user->serverPool->id, [
                'results' => $clientRecreationResults
            ]);
        }

        return $clientRecreationResults;
    }

}
