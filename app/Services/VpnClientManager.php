<?php
// этот класс сервис будет реализовывать работу с vpn аккаунтами на пулах серверов

namespace App\Services;

use App\Models\User;
use App\DTOs\Xui\CreateVpnClientDTO;
use App\DTOs\Xui\UpdateVpnClientDTO;

use Exception;
use Illuminate\Support\Facades\Log;

class VpnClientManager
{
    protected $xuiServerManagers = [];
    protected ?User $user;

    function __construct(?User $user = null)
    {
        $this->user = $user;
        if ($user) {
            $this->setupServerManagers($user);
        }
    }

    /**
     * Check if the server managers are already setup for a user.
     */
    protected function isServerManagerSetup(?User $user = null): bool
    {
        return $user && $this->user?->id === $user->id && !empty($this->xuiServerManagers);
    }

    protected function checkUserAndSetupServerManagers(?User $user = null)
    {
        if ($user && !$this->isServerManagerSetup($user)) {
            $this->setupServerManagers($user);
        } else if (!$user) {
            throw new Exception(__METHOD__ . ": User not provided in constructor and not provided in this method call");
        }
    }

    /**
     * Setup server managers for a user.
     */
    protected function setupServerManagers(?User $user = null)
    {
        try {
            // save user
            $this->user = $user;

            $servers = [];
            // get all active servers for user from active pools
            $user->activeServerPools()
                ->where('server_pools.is_active', true)
                ->wherePivotNull('released_at')
                ->each(function ($pool) use (&$servers) {
                    // Get only active servers that are not released from the pool
                    $activeServers = $pool->servers()
                        ->where('xui_servers.is_active', true)
                        ->wherePivotNull('released_at')
                        ->get();

                    $servers = array_merge($servers, $activeServers->all());
                });

            if (! count($servers)) {
                throw new Exception(__METHOD__ . ': No servers found for user ' . $user->id);
            }

            // create XUI server managers for all servers
            foreach ($servers as $server)
            {
                if (!isset($this->servers[$server->id])) {
                    $this->xuiServerManagers[$server->id] = new VpnXuiServerManager($server);
                }
            }

        } catch (Exception $e)
        {
            Log::error(__METHOD__ . ': Error creating VpnClientManager for user ' . $user->id . ': ' . $e->getMessage());
        }
    }

    /**
     * Apply a callback to all inbounds for a user on all servers in all pools.
     *
     * @param User $user
     * @param callable $callback
     * @param bool $onlyOnlineServer If true, skips offline servers
     * @return array of XuiResponseDTO
     */
    private function applyToInbounds(User $user, callable $callback, bool $onlyOnlineServer = false): array
    {
        $this->checkUserAndSetupServerManagers($user);
        $results = [];

        foreach ($this->xuiServerManagers as $serverId => $manager) {
            try {
                $server = $manager->getServer();

                if ($onlyOnlineServer && ! $server->isOnline()) {
                    Log::warning(__METHOD__ . ": Server {$serverId} is offline. Skipping.");
                    continue;
                }

                foreach ($server->inbounds() as $inbound) {
                    $dto = $callback($manager, $inbound); // ← возвращает XuiResponseDTO
                    $results[$serverId][$inbound->id] = $dto;

                    if (!$dto->isSuccess()) {
                        Log::warning(__METHOD__ . ": XUI error on server {$serverId}, inbound {$inbound->id}: {$dto->getMessage()}");
                    }
                }
            } catch (Exception $e) {
                Log::error(__METHOD__ . ": Exception on user {$user->id}, server {$serverId}, inbound {$inbound->id}: {$e->getMessage()}");
            }
        }

        return $results; // возвращает массив XuiResponseDTO
    }


    /**
     * Create VPN clients for a user on all servers in all pools.
     */
    public function createVpnClientsOnPools(?User $user)
    {
        Log::info(__METHOD__ . ": Creating VPN clients for user {$user->id}");

        $results = $this->applyToInbounds($user, function ($manager, $inbound) use ($user) {
            $dto = CreateVpnClientDTO::makeDtoFromUserAndInbound($user, $inbound);
            return $manager->createVpnClient($inbound->id, $dto);
        }, onlyOnlineServer: true);

        if (!empty($results)) {
            Log::info(__METHOD__ . ": VPN clients created for user {$user->id}", ['results' => $results]);
        } else {
            Log::error(__METHOD__ . ": No VPN clients created for user {$user->id}", ['results' => $results]);
        }

        return $results;
    }


    /**
     * Update VPN clients for a user on all servers in all pools.
     */
    public function updateVpnClientsOnPools(?User $user)
    {
        Log::info(__METHOD__ . ": Updating VPN clients for user {$user->id}");

        $results = $this->applyToInbounds($user, function ($manager, $inbound) use ($user) {
            $dto = UpdateVpnClientDTO::makeDtoFromUserAndInbound($user, $inbound);
            return $manager->updateVpnClient($inbound->id, $user->getClientId(), $dto);
        }, onlyOnlineServer: true);

        if (!empty($results)) {
            Log::info(__METHOD__ . ": VPN clients updated for user {$user->id}", ['results' => $results]);
        } else {
            Log::error(__METHOD__ . ": No VPN clients updated for user {$user->id}", ['results' => $results]);
        }

        return $results;
    }


    /**
     * Remove VPN clients for a user on all servers in all pools.
     */
    public function removeVpnClientsOnPools(?User $user)
    {
        $poolIds = $user->activeServerPools()->pluck('server_pools.id')->implode(',');
        Log::info(__METHOD__ . ": Removing VPN clients for user {$user->id} on pools {$poolIds}");

        $results = $this->applyToInbounds($user, function ($manager, $inbound) use ($user) {
            return $manager->removeVpnClient($inbound->id, $user->getClientId());
        }, onlyOnlineServer: true);

        if (!empty($results)) {
            Log::info(__METHOD__ . ": VPN clients removed for user {$user->id} on pools {$poolIds}", ['results' => $results]);
        } else {
            Log::error(__METHOD__ . ": No VPN clients removed for user {$user->id} on pools {$poolIds}", ['results' => $results]);
        }

        return $results;
    }


    /**
     * ReCreate VPN clients for a user on all servers in all pools.
     */
    public function recreateVpnClientsOnPools(?User $user)
    {
        $poolIds = $user->activeServerPools()->pluck('server_pools.id')->implode(',');
        Log::info(__METHOD__ . ": ReCreating VPN clients for user {$user->id} on pools {$poolIds}");

        $results = $this->applyToInbounds($user, function ($manager, $inbound) use ($user) {
            $removedDto = $manager->removeVpnClient($inbound->id, $user->getClientId());
            $createdDto = $manager->createVpnClient($inbound->id, CreateVpnClientDTO::makeDtoFromUserAndInbound($user, $inbound));

            if (!$removedDto->isSuccess()) {
                Log::warning("Remove failed: " . $removedDto->getMessage());
            }

            if (!$createdDto->isSuccess()) {
                Log::warning("Create failed: " . $createdDto->getMessage());
            }

            return [
                'removed' => $removedDto,
                'created' => $createdDto,
            ];
        }, onlyOnlineServer: true);


        if (!empty($results)) {
            Log::info(__METHOD__ . ": VPN clients reCreated for user {$user->id} on pools {$poolIds}", ['results' => $results]);
        } else {
            Log::error(__METHOD__ . ": No VPN clients reCreated for user {$user->id} on pools {$poolIds}", ['results' => $results]);
        }

        return $results;
    }

}
