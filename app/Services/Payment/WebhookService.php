<?php

namespace App\Services\Payment;

use App\Services\Payment\DTOs\WebhookData;
use App\Services\Payment\Exceptions\PaymentException;
use App\Models\PaymentWebhook;
use App\Models\PaymentMethod;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class WebhookService
{
    private PaymentGatewayManager $gatewayManager;

    public function __construct(PaymentGatewayManager $gatewayManager)
    {
        $this->gatewayManager = $gatewayManager;
    }

    /**
     * Process incoming webhook.
     */
    public function processWebhook(Request $request, string $gatewayName): array
    {
        try {
            DB::beginTransaction();

            // Create webhook data object
            $webhookData = $this->createWebhookData($request);

            // Validate webhook
            $this->validateWebhook($webhookData, $gatewayName);

            // Get the payment method
            $paymentMethod = $this->getPaymentMethod($gatewayName);

            // Store webhook record
            $webhookRecord = $this->storeWebhookRecord($webhookData, $paymentMethod);

            // Get gateway and process webhook
            $gateway = $this->gatewayManager->getGateway($gatewayName);
            $processed = $gateway->handleWebhook($webhookData);

            // Update webhook record with processing result
            $webhookRecord->update([
                'processed_at' => now(),
                'processing_result' => $processed ? 'success' : 'failed',
            ]);

            DB::commit();

            Log::info('Webhook processed successfully', [
                'gateway' => $gatewayName,
                'webhook_id' => $webhookRecord->id,
                'payment_id' => $webhookData->getPaymentId(),
                'processed' => $processed,
            ]);

            return [
                'success' => true,
                'processed' => $processed,
                'webhook_id' => $webhookRecord->id,
                'message' => 'Webhook processed successfully',
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Webhook processing failed', [
                'gateway' => $gatewayName,
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return [
                'success' => false,
                'processed' => false,
                'error' => $e->getMessage(),
                'message' => 'Webhook processing failed',
            ];
        }
    }

    /**
     * Verify webhook signature.
     */
    public function verifyWebhookSignature(Request $request, string $gatewayName): bool
    {
        try {
            $gateway = $this->gatewayManager->getGateway($gatewayName);

            if (!$gateway->supportsWebhooks()) {
                return false;
            }

            $headers = $request->headers->all();
            $payload = $request->getContent();

            return $gateway->verifyWebhookSignature($headers, $payload);

        } catch (\Exception $e) {
            Log::error('Webhook signature verification failed', [
                'gateway' => $gatewayName,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Get webhook statistics.
     */
    public function getWebhookStatistics(?string $gatewayName = null, int $days = 30): array
    {
        $query = PaymentWebhook::query();

        if ($gatewayName) {
            $paymentMethod = $this->getPaymentMethod($gatewayName);
            $query->where('method_id', $paymentMethod->id);
        }

        $query->where('created_at', '>=', now()->subDays($days));

        $total = $query->count();
        $processed = $query->whereNotNull('processed_at')->count();
        $successful = $query->where('processing_result', 'success')->count();
        $failed = $query->where('processing_result', 'failed')->count();

        return [
            'total_webhooks' => $total,
            'processed_webhooks' => $processed,
            'successful_webhooks' => $successful,
            'failed_webhooks' => $failed,
            'processing_rate' => $total > 0 ? round(($processed / $total) * 100, 2) : 0,
            'success_rate' => $processed > 0 ? round(($successful / $processed) * 100, 2) : 0,
            'period_days' => $days,
        ];
    }

    /**
     * Retry failed webhooks.
     */
    public function retryFailedWebhooks(?string $gatewayName = null, int $maxAge = 24): array
    {
        $query = PaymentWebhook::where('processing_result', 'failed')
            ->where('created_at', '>=', now()->subHours($maxAge));

        if ($gatewayName) {
            $paymentMethod = $this->getPaymentMethod($gatewayName);
            $query->where('method_id', $paymentMethod->id);
        }

        $failedWebhooks = $query->get();
        $results = [];

        foreach ($failedWebhooks as $webhook) {
            try {
                $webhookData = WebhookData::fromRequest(
                    json_decode($webhook->headers ?? '{}', true) ?: [],
                    $webhook->raw_payload,
                    'POST'
                );

                $gateway = $this->gatewayManager->getGateway($webhook->method->code);
                $processed = $gateway->handleWebhook($webhookData);

                $webhook->update([
                    'processed_at' => now(),
                    'processing_result' => $processed ? 'success' : 'failed',
                    'retry_count' => ($webhook->retry_count ?? 0) + 1,
                ]);

                $results[] = [
                    'webhook_id' => $webhook->id,
                    'success' => $processed,
                    'payment_id' => $webhookData->getPaymentId(),
                ];

            } catch (\Exception $e) {
                Log::error('Webhook retry failed', [
                    'webhook_id' => $webhook->id,
                    'error' => $e->getMessage(),
                ]);

                $results[] = [
                    'webhook_id' => $webhook->id,
                    'success' => false,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    /**
     * Clean up old webhook records.
     */
    public function cleanupOldWebhooks(int $daysToKeep = 90): int
    {
        $cutoffDate = now()->subDays($daysToKeep);

        $deleted = PaymentWebhook::where('created_at', '<', $cutoffDate)->delete();

        Log::info('Old webhook records cleaned up', [
            'deleted_count' => $deleted,
            'cutoff_date' => $cutoffDate->toDateString(),
        ]);

        return $deleted;
    }

    /**
     * Create webhook data from request.
     */
    private function createWebhookData(Request $request): WebhookData
    {
        return WebhookData::fromRequest(
            headers: $request->headers->all(),
            payload: $request->getContent(),
            method: $request->method(),
            userAgent: $request->userAgent(),
            ipAddress: $request->ip()
        );
    }

    /**
     * Validate webhook data.
     */
    private function validateWebhook(WebhookData $webhookData, string $gatewayName): void
    {
        // Basic validation
        $errors = $webhookData->validate();
        if (!empty($errors)) {
            throw new PaymentException('Webhook validation failed: ' . implode(', ', $errors));
        }

        // Check if webhook is from allowed IP
        $allowedIps = config("payments.gateways.{$gatewayName}.webhook.allowed_ips", []);
        if (!empty($allowedIps) && !$webhookData->isFromAllowedIp($allowedIps)) {
            throw new PaymentException('Webhook from unauthorized IP address');
        }

        // Check webhook age
        $maxAge = config('payments.webhook.max_age_seconds', 300);
        if ($webhookData->timestamp && $webhookData->timestamp->diffInSeconds(now()) > $maxAge) {
            throw new PaymentException('Webhook is too old');
        }
    }

    /**
     * Store webhook record in database.
     */
    private function storeWebhookRecord(WebhookData $webhookData, PaymentMethod $paymentMethod): PaymentWebhook
    {
        return PaymentWebhook::create([
            'method_id' => $paymentMethod->id,
            'external_payment_id' => $webhookData->getPaymentId(),
            'raw_payload' => $webhookData->payload,
            'headers' => json_encode($webhookData->headers),
            'ip_address' => $webhookData->ipAddress,
            'user_agent' => $webhookData->userAgent,
            'signature' => $webhookData->signature,
        ]);
    }

    /**
     * Get payment method by gateway name.
     */
    private function getPaymentMethod(string $gatewayName): PaymentMethod
    {
        $paymentMethod = PaymentMethod::where('code', $gatewayName)->first();

        if (!$paymentMethod) {
            throw new PaymentException("Payment method '{$gatewayName}' not found");
        }

        return $paymentMethod;
    }

    /**
     * Get recent webhook activity.
     */
    public function getRecentWebhookActivity(int $limit = 50): array
    {
        return PaymentWebhook::with('method')
            ->latest()
            ->limit($limit)
            ->get()
            ->map(function ($webhook) {
                return [
                    'id' => $webhook->id,
                    'method' => $webhook->method->name,
                    'payment_id' => $webhook->external_payment_id,
                    'processed' => $webhook->processed_at !== null,
                    'success' => $webhook->processing_result === 'success',
                    'created_at' => $webhook->created_at->toISOString(),
                    'processed_at' => $webhook->processed_at?->toISOString(),
                ];
            })
            ->toArray();
    }
}
