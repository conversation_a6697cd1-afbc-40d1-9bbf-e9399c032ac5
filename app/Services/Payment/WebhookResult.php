<?php

namespace App\Services\Payment;

class WebhookResult
{
    public function __construct(
        public readonly bool $success,
        public readonly ?string $paymentId = null,
        public readonly ?string $status = null,
        public readonly array $additionalData = [],
        public readonly ?string $message = null,
        public readonly ?string $errorCode = null
    ) {}

    public static function success(
        string $paymentId,
        string $status,
        array $additionalData = []
    ): self {
        return new self(
            success: true,
            paymentId: $paymentId,
            status: $status,
            additionalData: $additionalData
        );
    }

    public static function failure(
        string $message,
        ?string $errorCode = null,
        array $additionalData = []
    ): self {
        return new self(
            success: false,
            message: $message,
            errorCode: $errorCode,
            additionalData: $additionalData
        );
    }

    public static function ignored(
        string $message,
        array $additionalData = []
    ): self {
        return new self(
            success: true,
            message: $message,
            additionalData: $additionalData
        );
    }

    public static function duplicate(
        string $paymentId,
        string $currentStatus,
        string $message,
        array $additionalData = []
    ): self {
        return new self(
            success: true,
            paymentId: $paymentId,
            status: $currentStatus,
            message: $message,
            additionalData: array_merge($additionalData, [
                'is_duplicate' => true,
                'current_status' => $currentStatus,
            ])
        );
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function isFailure(): bool
    {
        return !$this->success;
    }

    public function hasPaymentUpdate(): bool
    {
        return $this->success && $this->paymentId && $this->status && !$this->isDuplicate();
    }

    public function isDuplicate(): bool
    {
        return $this->additionalData['is_duplicate'] ?? false;
    }

    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'payment_id' => $this->paymentId,
            'status' => $this->status,
            'additional_data' => $this->additionalData,
            'message' => $this->message,
            'error_code' => $this->errorCode,
        ];
    }
}
