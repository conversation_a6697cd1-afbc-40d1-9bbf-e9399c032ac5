<?php

namespace App\Services\Payment;

use App\Models\Payment;

interface PaymentGatewayInterface
{
    /**
     * Initialize the gateway with configuration.
     */
    public function initialize(array $config): void;

    /**
     * Create a payment.
     */
    public function createPayment(array $data): PaymentResult;

    /**
     * Cancel a payment.
     */
    public function cancelPayment(string $paymentId): PaymentResult;

    /**
     * Refund a payment.
     */
    public function refundPayment(string $paymentId, ?int $amount = null): PaymentResult;

    /**
     * Get payment status.
     */
    public function getPaymentStatus(string $paymentId): PaymentResult;

    /**
     * Handle webhook notification.
     */
    public function handleWebhook(array $data): bool;

    /**
     * Check if gateway supports recurring payments.
     */
    public function supportsRecurring(): bool;

    /**
     * Check if gateway supports webhooks.
     */
    public function supportsWebhooks(): bool;

    /**
     * Check if gateway supports refunds.
     */
    public function supportsRefunds(): bool;

    /**
     * Get gateway name.
     */
    public function getName(): string;
}
