<?php

namespace App\Services\Payment;

use App\Services\Payment\Contracts\PaymentGatewayInterface;
use App\Services\Payment\Exceptions\GatewayNotFoundException;
use App\Services\Payment\Gateways\ManualGateway;
use App\Services\Payment\Gateways\CashGateway;
use App\Services\Payment\Gateways\FreeGateway;
use App\Services\Payment\Gateways\TBankGateway;
use Illuminate\Support\Facades\Log;

class PaymentGatewayManager
{
    private array $gateways = [];
    private array $config;

    public function __construct()
    {
        $this->config = config('payments.gateways', []);
        $this->registerDefaultGateways();
    }

    /**
     * Get a payment gateway by name.
     */
    public function getGateway(string $name): PaymentGatewayInterface
    {
        if (!isset($this->gateways[$name])) {
            $this->createGateway($name);
        }

        return $this->gateways[$name];
    }

    /**
     * Register a custom gateway.
     */
    public function registerGateway(string $name, PaymentGatewayInterface $gateway): void
    {
        $this->gateways[$name] = $gateway;
    }

    /**
     * Check if a gateway is registered.
     */
    public function hasGateway(string $name): bool
    {
        return isset($this->gateways[$name]) || isset($this->config[$name]);
    }

    /**
     * Get all available gateway names.
     */
    public function getAvailableGateways(): array
    {
        return array_keys($this->config);
    }

    /**
     * Get gateway configuration.
     */
    public function getGatewayConfig(string $name): array
    {
        if (!isset($this->config[$name])) {
            throw new GatewayNotFoundException("Gateway configuration for '{$name}' not found");
        }

        return $this->config[$name];
    }

    /**
     * Create a gateway instance.
     */
    private function createGateway(string $name): void
    {
        if (!isset($this->config[$name])) {
            throw new GatewayNotFoundException("Gateway '{$name}' not found in configuration");
        }

        $config = $this->config[$name];
        $driver = $config['driver'] ?? $name;

        $gateway = $this->createGatewayInstance($driver);

        if (!$gateway) {
            throw new GatewayNotFoundException("Gateway driver '{$driver}' not found");
        }

        // Initialize gateway with configuration
        // Pass the gateway-specific config, not the entire gateway definition
        $gatewayConfig = $config['config'] ?? [];

        // Add some gateway metadata to the config
        $gatewayConfig['enabled'] = $config['enabled'] ?? false;
        $gatewayConfig['name'] = $config['name'] ?? $name;
        $gatewayConfig['driver'] = $config['driver'] ?? $name;

        $gateway->initialize($gatewayConfig);

        $this->gateways[$name] = $gateway;

        Log::debug("Payment gateway '{$name}' created and initialized", [
            'driver' => $driver,
            'enabled' => $config['enabled'] ?? false,
        ]);
    }

    /**
     * Create gateway instance by driver name.
     */
    private function createGatewayInstance(string $driver): ?PaymentGatewayInterface
    {
        return match ($driver) {
            'manual' => new ManualGateway(),
            'cash' => new CashGateway(),
            'free' => new FreeGateway(),
            'tbank' => new TBankGateway(),
            default => null,
        };
    }

    /**
     * Register default gateways.
     */
    private function registerDefaultGateways(): void
    {
        // Default gateways are created on-demand
        // This method can be used to pre-register gateways if needed
    }

    /**
     * Get gateway statistics.
     */
    public function getGatewayStatistics(): array
    {
        $stats = [];

        foreach ($this->getAvailableGateways() as $gatewayName) {
            try {
                $gateway = $this->getGateway($gatewayName);
                $config = $this->getGatewayConfig($gatewayName);

                $stats[$gatewayName] = [
                    'name' => $gateway->getName(),
                    'enabled' => $config['enabled'] ?? false,
                    'available' => $gateway->isAvailable(),
                    'supports_recurring' => $gateway->supportsRecurring(),
                    'supports_webhooks' => $gateway->supportsWebhooks(),
                    'supports_refunds' => $gateway->supportsRefunds(),
                    'supported_currencies' => $gateway->getSupportedCurrencies(),
                    'minimum_amount' => $gateway->getMinimumAmount(),
                    'maximum_amount' => $gateway->getMaximumAmount(),
                ];
            } catch (\Exception $e) {
                $stats[$gatewayName] = [
                    'name' => $gatewayName,
                    'enabled' => false,
                    'available' => false,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $stats;
    }

    /**
     * Test gateway connectivity.
     */
    public function testGateway(string $name): array
    {
        try {
            $gateway = $this->getGateway($name);
            $config = $this->getGatewayConfig($name);

            $result = [
                'gateway' => $name,
                'available' => $gateway->isAvailable(),
                'enabled' => $config['enabled'] ?? false,
                'configuration_valid' => true,
                'error' => null,
            ];

            // Additional connectivity tests can be added here
            // For example, for TBank, we could test API connectivity

            return $result;

        } catch (\Exception $e) {
            return [
                'gateway' => $name,
                'available' => false,
                'enabled' => false,
                'configuration_valid' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Reload gateway configuration.
     */
    public function reloadConfiguration(): void
    {
        $this->config = config('payments.gateways', []);
        $this->gateways = []; // Clear cached gateways to force recreation

        Log::info('Payment gateway configuration reloaded');
    }

    /**
     * Get enabled gateways only.
     */
    public function getEnabledGateways(): array
    {
        $enabled = [];

        foreach ($this->getAvailableGateways() as $gatewayName) {
            $config = $this->getGatewayConfig($gatewayName);
            if ($config['enabled'] ?? false) {
                try {
                    $gateway = $this->getGateway($gatewayName);
                    if ($gateway->isAvailable()) {
                        $enabled[] = $gatewayName;
                    }
                } catch (\Exception $e) {
                    Log::warning("Enabled gateway '{$gatewayName}' is not available", [
                        'error' => $e->getMessage(),
                    ]);
                }
            }
        }

        return $enabled;
    }

    /**
     * Get default gateway.
     */
    public function getDefaultGateway(): PaymentGatewayInterface
    {
        $defaultGatewayName = config('payments.default', 'manual');
        return $this->getGateway($defaultGatewayName);
    }

    /**
     * Validate all gateway configurations.
     */
    public function validateConfigurations(): array
    {
        $results = [];

        foreach ($this->getAvailableGateways() as $gatewayName) {
            try {
                $this->getGateway($gatewayName);
                $results[$gatewayName] = [
                    'valid' => true,
                    'error' => null,
                ];
            } catch (\Exception $e) {
                $results[$gatewayName] = [
                    'valid' => false,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }
}
