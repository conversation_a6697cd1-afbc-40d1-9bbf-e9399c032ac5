<?php

namespace App\Services\Payment;

use Illuminate\Support\Facades\Log;

abstract class AbstractPaymentGateway implements PaymentGatewayInterface
{
    protected array $config = [];

    public function initialize(array $config): void
    {
        $this->config = $config;
    }

    protected function log(string $level, string $message, array $context = []): void
    {
        $context['gateway'] = $this->getName();
        Log::log($level, "[Payment Gateway] {$message}", $context);
    }

    protected function getConfig(string $key, $default = null)
    {
        return data_get($this->config, $key, $default);
    }

    protected function isEnabled(): bool
    {
        return $this->getConfig('enabled', false);
    }

    protected function validateConfig(array $required): void
    {
        foreach ($required as $key) {
            if (empty($this->getConfig($key))) {
                throw new \InvalidArgumentException("Missing required config: {$key}");
            }
        }
    }

    public function supportsRecurring(): bool
    {
        return false;
    }

    public function supportsWebhooks(): bool
    {
        return false;
    }

    public function supportsRefunds(): bool
    {
        return false;
    }

    public function refundPayment(string $paymentId, ?int $amount = null): PaymentResult
    {
        if (!$this->supportsRefunds()) {
            return PaymentResult::failure('Refunds not supported by this gateway');
        }

        throw new \BadMethodCallException('Refund method not implemented');
    }

    public function handleWebhook(array $data): WebhookResult
    {
        if (!$this->supportsWebhooks()) {
            $this->log('warning', 'Webhook received but not supported', ['data' => $data]);
            return WebhookResult::failure('Webhooks not supported by this gateway');
        }

        throw new \BadMethodCallException('Webhook handling not implemented');
    }
}
