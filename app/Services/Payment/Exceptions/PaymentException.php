<?php

namespace App\Services\Payment\Exceptions;

use Exception;

class PaymentException extends Exception
{
    protected string $paymentId;
    protected array $context;

    public function __construct(
        string $message = '',
        int $code = 0,
        ?Exception $previous = null,
        string $paymentId = '',
        array $context = []
    ) {
        parent::__construct($message, $code, $previous);
        $this->paymentId = $paymentId;
        $this->context = $context;
    }

    /**
     * Get the payment ID associated with this exception.
     */
    public function getPaymentId(): string
    {
        return $this->paymentId;
    }

    /**
     * Get additional context information.
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Set the payment ID.
     */
    public function setPaymentId(string $paymentId): self
    {
        $this->paymentId = $paymentId;
        return $this;
    }

    /**
     * Set additional context.
     */
    public function setContext(array $context): self
    {
        $this->context = $context;
        return $this;
    }

    /**
     * Add context information.
     */
    public function addContext(string $key, $value): self
    {
        $this->context[$key] = $value;
        return $this;
    }

    /**
     * Create a payment exception with payment ID.
     */
    public static function withPaymentId(string $message, string $paymentId, array $context = []): self
    {
        return new self($message, 0, null, $paymentId, $context);
    }

    /**
     * Create a payment exception from another exception.
     */
    public static function fromException(Exception $exception, string $paymentId = '', array $context = []): self
    {
        return new self(
            $exception->getMessage(),
            $exception->getCode(),
            $exception,
            $paymentId,
            $context
        );
    }

    /**
     * Convert to array for logging.
     */
    public function toArray(): array
    {
        return [
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'payment_id' => $this->paymentId,
            'context' => $this->context,
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'trace' => $this->getTraceAsString(),
        ];
    }
}
