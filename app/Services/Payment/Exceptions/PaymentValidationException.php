<?php

namespace App\Services\Payment\Exceptions;

class PaymentValidationException extends PaymentException
{
    protected array $validationErrors;

    public function __construct(
        string $message = '',
        array $validationErrors = [],
        int $code = 0,
        ?\Exception $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->validationErrors = $validationErrors;
    }

    /**
     * Get validation errors.
     */
    public function getValidationErrors(): array
    {
        return $this->validationErrors;
    }

    /**
     * Check if there are validation errors.
     */
    public function hasValidationErrors(): bool
    {
        return !empty($this->validationErrors);
    }

    /**
     * Add a validation error.
     */
    public function addValidationError(string $error): self
    {
        $this->validationErrors[] = $error;
        return $this;
    }

    /**
     * Set validation errors.
     */
    public function setValidationErrors(array $errors): self
    {
        $this->validationErrors = $errors;
        return $this;
    }

    /**
     * Create exception from validation errors.
     */
    public static function fromErrors(array $errors): self
    {
        $message = 'Payment validation failed';
        if (!empty($errors)) {
            $message .= ': ' . implode(', ', $errors);
        }

        return new self($message, $errors);
    }

    /**
     * Create exception for invalid amount.
     */
    public static function invalidAmount(int $amount, int $minimum, int $maximum): self
    {
        $message = "Invalid payment amount: {$amount}. Must be between {$minimum} and {$maximum}";
        return new self($message, ['amount' => $message]);
    }

    /**
     * Create exception for unsupported currency.
     */
    public static function unsupportedCurrency(string $currency, array $supportedCurrencies): self
    {
        $supported = implode(', ', $supportedCurrencies);
        $message = "Unsupported currency: {$currency}. Supported currencies: {$supported}";
        return new self($message, ['currency' => $message]);
    }

    /**
     * Create exception for missing required field.
     */
    public static function missingRequiredField(string $field): self
    {
        $message = "Required field missing: {$field}";
        return new self($message, [$field => $message]);
    }

    /**
     * Convert to array for logging.
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'validation_errors' => $this->validationErrors,
        ]);
    }
}
