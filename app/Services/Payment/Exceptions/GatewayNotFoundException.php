<?php

namespace App\Services\Payment\Exceptions;

class GatewayNotFoundException extends PaymentException
{
    protected string $gatewayName;

    public function __construct(
        string $message = '',
        string $gatewayName = '',
        int $code = 0,
        ?\Exception $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->gatewayName = $gatewayName;
    }

    /**
     * Get the gateway name that was not found.
     */
    public function getGatewayName(): string
    {
        return $this->gatewayName;
    }

    /**
     * Create exception for missing gateway.
     */
    public static function forGateway(string $gatewayName): self
    {
        return new self("Payment gateway '{$gatewayName}' not found", $gatewayName);
    }

    /**
     * Create exception for disabled gateway.
     */
    public static function gatewayDisabled(string $gatewayName): self
    {
        return new self("Payment gateway '{$gatewayName}' is disabled", $gatewayName);
    }

    /**
     * Create exception for unavailable gateway.
     */
    public static function gatewayUnavailable(string $gatewayName, string $reason = ''): self
    {
        $message = "Payment gateway '{$gatewayName}' is unavailable";
        if ($reason) {
            $message .= ": {$reason}";
        }
        return new self($message, $gatewayName);
    }

    /**
     * Convert to array for logging.
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'gateway_name' => $this->gatewayName,
        ]);
    }
}
