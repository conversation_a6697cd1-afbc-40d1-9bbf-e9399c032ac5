<?php

namespace App\Services\Payment\Exceptions;

class WebhookException extends PaymentException
{
    protected string $gatewayName;
    protected array $webhookData;

    public function __construct(
        string $message = '',
        string $gatewayName = '',
        array $webhookData = [],
        int $code = 0,
        ?\Exception $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->gatewayName = $gatewayName;
        $this->webhookData = $webhookData;
    }

    /**
     * Get the gateway name.
     */
    public function getGatewayName(): string
    {
        return $this->gatewayName;
    }

    /**
     * Get webhook data.
     */
    public function getWebhookData(): array
    {
        return $this->webhookData;
    }

    /**
     * Create exception for invalid signature.
     */
    public static function invalidSignature(string $gatewayName, array $webhookData = []): self
    {
        return new self(
            "Invalid webhook signature for {$gatewayName}",
            $gatewayName,
            $webhookData
        );
    }

    /**
     * Create exception for unauthorized IP.
     */
    public static function unauthorizedIp(string $gatewayName, string $ip, array $webhookData = []): self
    {
        return new self(
            "Webhook from unauthorized IP {$ip} for {$gatewayName}",
            $gatewayName,
            $webhookData
        );
    }

    /**
     * Create exception for expired webhook.
     */
    public static function expired(string $gatewayName, array $webhookData = []): self
    {
        return new self(
            "Webhook expired for {$gatewayName}",
            $gatewayName,
            $webhookData
        );
    }

    /**
     * Create exception for malformed webhook.
     */
    public static function malformed(string $gatewayName, string $reason, array $webhookData = []): self
    {
        return new self(
            "Malformed webhook for {$gatewayName}: {$reason}",
            $gatewayName,
            $webhookData
        );
    }

    /**
     * Convert to array for logging.
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'gateway_name' => $this->gatewayName,
            'webhook_data' => $this->webhookData,
        ]);
    }
}
