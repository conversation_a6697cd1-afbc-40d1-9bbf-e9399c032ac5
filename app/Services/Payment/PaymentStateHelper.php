<?php

namespace App\Services\Payment;

class PaymentStateHelper
{
    /**
     * Final payment states that should not be changed by webhooks.
     */
    public const FINAL_STATES = [
        'paid',
        'failed', 
        'cancelled',
        'refunded',
        'disputed'
    ];

    /**
     * Transitional states that can be updated by webhooks.
     */
    public const TRANSITIONAL_STATES = [
        'pending',
        'processing',
        'authorized'
    ];

    /**
     * Check if a payment status is in a final state.
     */
    public static function isFinalState(string $status): bool
    {
        return in_array($status, self::FINAL_STATES);
    }

    /**
     * Check if a payment status is transitional.
     */
    public static function isTransitionalState(string $status): bool
    {
        return in_array($status, self::TRANSITIONAL_STATES);
    }

    /**
     * Check if a status transition is allowed.
     */
    public static function isTransitionAllowed(string $fromStatus, string $toStatus): bool
    {
        // If current status is final, no transitions are allowed except specific cases
        if (self::isFinalState($fromStatus)) {
            return self::isExceptionalTransition($fromStatus, $toStatus);
        }

        // From transitional states, any transition is allowed
        if (self::isTransitionalState($fromStatus)) {
            return true;
        }

        // Unknown states - allow transition for safety
        return true;
    }

    /**
     * Check for exceptional transitions from final states.
     */
    private static function isExceptionalTransition(string $fromStatus, string $toStatus): bool
    {
        // Allow specific transitions from final states
        $allowedTransitions = [
            'paid' => ['refunded', 'disputed'],
            'failed' => ['paid'], // Retry scenarios
            'cancelled' => ['paid'], // Manual approval after cancellation
        ];

        return in_array($toStatus, $allowedTransitions[$fromStatus] ?? []);
    }

    /**
     * Get a human-readable description of why a transition was blocked.
     */
    public static function getTransitionBlockReason(string $fromStatus, string $toStatus): string
    {
        if (self::isFinalState($fromStatus)) {
            return "Payment is already in final state '{$fromStatus}' and cannot transition to '{$toStatus}'";
        }

        return "Transition from '{$fromStatus}' to '{$toStatus}' is not allowed";
    }

    /**
     * Check if a webhook should be processed based on current payment state.
     */
    public static function shouldProcessWebhook(string $currentStatus, string $webhookStatus): bool
    {
        // Always process if statuses are different and transition is allowed
        if ($currentStatus !== $webhookStatus) {
            return self::isTransitionAllowed($currentStatus, $webhookStatus);
        }

        // Don't process if statuses are the same (duplicate webhook)
        return false;
    }

    /**
     * Get all possible states.
     */
    public static function getAllStates(): array
    {
        return array_merge(self::FINAL_STATES, self::TRANSITIONAL_STATES);
    }
}
