<?php

namespace App\Services\Payment\Gateways;

use App\Services\Payment\DTOs\PaymentRequest;
use App\Services\Payment\DTOs\PaymentResponse;
use App\Services\Payment\DTOs\RefundRequest;
use App\Services\Payment\DTOs\RefundResponse;
use App\Services\Payment\DTOs\PaymentStatusResponse;
use App\Models\Payment;

class FreeGateway extends AbstractPaymentGateway
{
    protected string $name = 'free';
    protected bool $supportsRefunds = false; // No refunds for free payments
    protected array $supportedCurrencies = ['RUB', 'USD', 'EUR'];
    protected int $minimumAmount = 0; // Free payments can be 0
    protected int $maximumAmount = 0; // Free payments should always be 0

    /**
     * Create a new payment.
     */
    public function createPayment(PaymentRequest $request): PaymentResponse
    {
        try {
            $this->log('info', 'Creating free payment', [
                'order_id' => $request->order->id,
                'amount' => $request->amount,
                'currency' => $request->currency,
            ]);

            // Validate that amount is 0 for free payments
            if ($request->amount !== 0) {
                return PaymentResponse::failed(
                    'invalid_amount',
                    'Free payments must have zero amount'
                );
            }

            $paymentId = $this->generatePaymentReference();

            // Free payments are always automatically confirmed
            $this->log('info', 'Free payment created and confirmed', [
                'payment_id' => $paymentId,
                'order_id' => $request->order->id, // Internal logging uses real ID
            ]);

            return PaymentResponse::success(
                status: 'paid',
                paymentId: $paymentId,
                data: [
                    'confirmed_at' => now()->toISOString(),
                    'payment_type' => 'promotional',
                    'instructions' => 'Your promotional subscription has been activated immediately.',
                ]
            );

        } catch (\Exception $e) {
            $this->log('error', 'Failed to create free payment', [
                'error' => $e->getMessage(),
                'order_id' => $request->order->id,
            ]);

            return PaymentResponse::failed(
                'failed',
                'Failed to create free payment: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get payment status.
     */
    public function getPaymentStatus(string $paymentId): PaymentStatusResponse
    {
        try {
            $payment = Payment::where('public_id', $paymentId)->first();

            if (!$payment) {
                return PaymentStatusResponse::failed(
                    'not_found',
                    'Payment not found'
                );
            }

            // Free payments are always paid immediately
            return PaymentStatusResponse::success(
                status: 'paid',
                paymentId: $paymentId,
                amount: 0,
                currency: $payment->currency,
                orderId: $payment->order->public_id, // Use public order ID for external response
                createdAt: $payment->created_at,
                confirmedAt: $payment->created_at, // Confirmed immediately
            );

        } catch (\Exception $e) {
            $this->log('error', 'Failed to get payment status', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
            ]);

            return PaymentStatusResponse::failed(
                'error',
                'Failed to get payment status: ' . $e->getMessage()
            );
        }
    }

    /**
     * Cancel a payment.
     */
    public function cancelPayment(string $paymentId): PaymentResponse
    {
        // Free payments cannot be cancelled once created since they're immediately confirmed
        return PaymentResponse::failed(
            'not_allowed',
            'Free payments cannot be cancelled as they are immediately confirmed'
        );
    }

    /**
     * Refund a payment.
     */
    public function refundPayment(RefundRequest $request): RefundResponse
    {
        // Free payments cannot be refunded
        return RefundResponse::failed(
            'not_supported',
            'Free payments cannot be refunded'
        );
    }

    /**
     * Get gateway-specific payment form data.
     */
    public function getPaymentFormData(Payment $payment): array
    {
        return array_merge(parent::getPaymentFormData($payment), [
            'payment_type' => 'promotional',
            'instructions' => 'This is a promotional subscription. No payment is required.',
            'auto_confirmed' => true,
        ]);
    }

    /**
     * Validate payment request before processing.
     */
    public function validatePaymentRequest(PaymentRequest $request): array
    {
        $errors = parent::validatePaymentRequest($request);

        // Additional validation for free payments
        if ($request->amount !== 0) {
            $errors[] = 'Free payments must have zero amount';
        }

        if ($request->isRecurring()) {
            $errors[] = 'Free payments cannot be recurring';
        }

        // Check if user is eligible for free payment
        if (!$this->isUserEligibleForFreePayment($request)) {
            $errors[] = 'User is not eligible for free payment';
        }

        return $errors;
    }

    /**
     * Check if user is eligible for free payment.
     */
    private function isUserEligibleForFreePayment(PaymentRequest $request): bool
    {
        // Implement your business logic here
        // For example:
        // - Check if user has already used free trial
        // - Check if this is a promotional campaign
        // - Check user's subscription history

        $user = $request->user;

        // Example: Check if user has any previous paid orders
        $hasPaidOrders = $user->orders()->paid()->exists();

        // Example: Allow free payment if user has no paid orders (first-time user)
        // or if this is explicitly marked as promotional
        $isPromotional = $request->metadata['promotional'] ?? false;

        return !$hasPaidOrders || $isPromotional;
    }

    /**
     * Get minimum payment amount for the gateway.
     */
    public function getMinimumAmount(string $currency = 'RUB'): int
    {
        return 0; // Free payments are always 0
    }

    /**
     * Get maximum payment amount for the gateway.
     */
    public function getMaximumAmount(string $currency = 'RUB'): int
    {
        return 0; // Free payments are always 0
    }

    /**
     * Validate gateway configuration.
     */
    protected function validateConfig(): void
    {
        if (!isset($this->config['enabled'])) {
            throw new \InvalidArgumentException('Free gateway enabled flag is required');
        }

        // Ensure max_amount is 0 for free gateway
        $maxAmount = $this->getConfigValue('max_amount', 0);
        if ($maxAmount !== 0) {
            throw new \InvalidArgumentException('Free gateway max_amount must be 0');
        }
    }
}
