<?php

namespace App\Services\Payment\Gateways;

use App\Services\Payment\AbstractPaymentGateway;
use App\Services\Payment\PaymentResult;
use App\Services\Payment\WebhookResult;

class FreeGateway extends AbstractPaymentGateway
{
    public function getName(): string
    {
        return 'free';
    }

    public function createPayment(array $data): PaymentResult
    {
        $this->log('info', 'Creating free payment', $data);

        // Free payments should always be 0 amount
        $amount = $data['amount'] ?? 0;
        if ($amount > 0) {
            return PaymentResult::failure('Free payments must have 0 amount');
        }

        // Generate a fake payment ID for consistency
        $paymentId = 'free_' . uniqid();

        return PaymentResult::success(
            paymentId: $paymentId,
            status: 'paid',
            data: [
                'order_id' => $data['order_id'] ?? null,
                'amount' => 0,
                'auto_confirmed' => true,
            ]
        );
    }

    public function cancelPayment(string $paymentId): PaymentResult
    {
        $this->log('info', 'Cancelling free payment', ['payment_id' => $paymentId]);

        return PaymentResult::success(
            paymentId: $paymentId,
            status: 'cancelled'
        );
    }

    public function getPaymentStatus(string $paymentId): PaymentResult
    {
        $this->log('info', 'Getting free payment status', ['payment_id' => $paymentId]);

        return PaymentResult::success(
            paymentId: $paymentId,
            status: 'paid'
        );
    }

    public function supportsRefunds(): bool
    {
        return false; // Can't refund free payments
    }

    public function handleWebhook(array $data): WebhookResult
    {
        $this->log('info', 'Free gateway webhook received', $data);

        return WebhookResult::ignored(
            'Free gateway does not process webhooks',
            ['webhook_data' => $data]
        );
    }
}
