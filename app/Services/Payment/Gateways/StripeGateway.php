<?php

namespace App\Services\Payment\Gateways;

use App\Services\Payment\AbstractPaymentGateway;
use App\Services\Payment\PaymentResult;
use App\Services\Payment\WebhookResult;

/**
 * Example Stripe Gateway implementation to demonstrate extensibility
 * This is a skeleton implementation showing how to add new gateways
 */
class StripeGateway extends AbstractPaymentGateway
{
    public function getName(): string
    {
        return 'stripe';
    }

    public function initialize(array $config): void
    {
        parent::initialize($config);
        
        $this->validateConfig(['secret_key', 'publishable_key']);
    }

    public function createPayment(array $data): PaymentResult
    {
        try {
            $this->log('info', 'Creating Stripe payment', $data);

            // This would integrate with Stripe API
            // For now, return a mock response
            return PaymentResult::success(
                paymentId: 'pi_' . uniqid(),
                paymentUrl: 'https://checkout.stripe.com/pay/mock_session',
                status: 'pending',
                data: [
                    'order_id' => $data['order_id'] ?? null,
                    'amount' => $data['amount'] ?? 0,
                    'currency' => $data['currency'] ?? 'USD',
                ]
            );

        } catch (\Exception $e) {
            $this->log('error', 'Stripe payment creation exception', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            return PaymentResult::failure(
                message: 'Payment creation failed: ' . $e->getMessage(),
                errorCode: 'STRIPE_EXCEPTION'
            );
        }
    }

    public function cancelPayment(string $paymentId): PaymentResult
    {
        $this->log('info', 'Cancelling Stripe payment', ['payment_id' => $paymentId]);

        // Mock implementation
        return PaymentResult::success(
            paymentId: $paymentId,
            status: 'cancelled'
        );
    }

    public function getPaymentStatus(string $paymentId): PaymentResult
    {
        $this->log('info', 'Getting Stripe payment status', ['payment_id' => $paymentId]);

        // Mock implementation
        return PaymentResult::success(
            paymentId: $paymentId,
            status: 'paid'
        );
    }

    public function supportsRecurring(): bool
    {
        return true;
    }

    public function supportsWebhooks(): bool
    {
        return true;
    }

    public function supportsRefunds(): bool
    {
        return true;
    }

    public function handleWebhook(array $data): WebhookResult
    {
        $this->log('info', 'Processing Stripe webhook', $data);

        // Validate Stripe webhook structure
        if (!isset($data['type']) || !isset($data['data']['object']['id'])) {
            $this->log('warning', 'Invalid Stripe webhook data', $data);
            return WebhookResult::failure(
                'Invalid webhook data: missing type or object.id',
                'INVALID_WEBHOOK_DATA',
                ['received_data' => $data]
            );
        }

        $eventType = $data['type'];
        $paymentIntent = $data['data']['object'];
        $paymentId = $paymentIntent['id'];

        // Map Stripe event types to our system statuses
        $mappedStatus = $this->mapStripeEventToStatus($eventType);

        if (!$mappedStatus) {
            return WebhookResult::ignored(
                "Stripe event type '{$eventType}' does not require payment status update",
                ['event_type' => $eventType, 'payment_id' => $paymentId]
            );
        }

        // Prepare additional data to store
        $additionalData = [
            'webhook_data' => $data,
            'webhook_received_at' => now()->toISOString(),
            'stripe_event_type' => $eventType,
            'stripe_payment_intent' => $paymentIntent,
        ];

        // Add payment method details if available
        if (isset($paymentIntent['payment_method_details'])) {
            $additionalData['payment_method_details'] = $paymentIntent['payment_method_details'];
        }

        $this->log('info', 'Stripe webhook processed', [
            'payment_id' => $paymentId,
            'event_type' => $eventType,
            'mapped_status' => $mappedStatus,
        ]);

        return WebhookResult::success(
            paymentId: $paymentId,
            status: $mappedStatus,
            additionalData: $additionalData
        );
    }

    /**
     * Map Stripe event types to our system statuses.
     */
    private function mapStripeEventToStatus(string $eventType): ?string
    {
        return match ($eventType) {
            'payment_intent.succeeded' => 'paid',
            'payment_intent.payment_failed' => 'failed',
            'payment_intent.canceled' => 'cancelled',
            'charge.dispute.created' => 'disputed',
            'invoice.payment_succeeded' => 'paid',
            'invoice.payment_failed' => 'failed',
            // Events that don't require status updates
            'payment_intent.created',
            'payment_intent.processing',
            'payment_method.attached' => null,
            default => null,
        };
    }

    public function refundPayment(string $paymentId, ?int $amount = null): PaymentResult
    {
        $this->log('info', 'Processing Stripe refund', [
            'payment_id' => $paymentId,
            'amount' => $amount
        ]);

        // Mock implementation
        return PaymentResult::success(
            paymentId: $paymentId,
            status: 'refunded',
            data: [
                'refund_amount' => $amount,
                'refund_id' => 're_' . uniqid(),
            ]
        );
    }
}
