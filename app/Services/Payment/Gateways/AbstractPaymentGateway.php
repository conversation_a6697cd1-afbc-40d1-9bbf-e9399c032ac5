<?php

namespace App\Services\Payment\Gateways;

use App\Services\Payment\Contracts\PaymentGatewayInterface;
use App\Services\Payment\DTOs\PaymentRequest;
use App\Services\Payment\DTOs\PaymentResponse;
use App\Services\Payment\DTOs\RefundRequest;
use App\Services\Payment\DTOs\RefundResponse;
use App\Services\Payment\DTOs\PaymentStatusResponse;
use App\Services\Payment\DTOs\RecurringPaymentRequest;
use App\Services\Payment\DTOs\WebhookData;
use App\Models\Payment;
use Illuminate\Support\Facades\Log;

abstract class AbstractPaymentGateway implements PaymentGatewayInterface
{
    protected array $config = [];
    protected string $name = '';
    protected bool $supportsRecurring = false;
    protected bool $supportsWebhooks = false;
    protected bool $supportsRefunds = false;
    protected array $supportedCurrencies = ['RUB'];
    protected int $minimumAmount = 100; // 1 RUB in kopecks
    protected int $maximumAmount = 100000000; // 1,000,000 RUB in kopecks

    /**
     * Initialize the gateway with configuration.
     */
    public function initialize(array $config): void
    {
        $this->config = $config;
        $this->validateConfig();
    }

    /**
     * Get the gateway name.
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * Get the gateway configuration.
     */
    public function getConfig(): array
    {
        return $this->config;
    }

    /**
     * Check if the gateway supports recurring payments.
     */
    public function supportsRecurring(): bool
    {
        return $this->supportsRecurring;
    }

    /**
     * Check if the gateway supports webhooks.
     */
    public function supportsWebhooks(): bool
    {
        return $this->supportsWebhooks;
    }

    /**
     * Check if the gateway supports refunds.
     */
    public function supportsRefunds(): bool
    {
        return $this->supportsRefunds;
    }

    /**
     * Get supported currencies.
     */
    public function getSupportedCurrencies(): array
    {
        return $this->supportedCurrencies;
    }

    /**
     * Get minimum payment amount for the gateway.
     */
    public function getMinimumAmount(string $currency = 'RUB'): int
    {
        return $this->minimumAmount;
    }

    /**
     * Get maximum payment amount for the gateway.
     */
    public function getMaximumAmount(string $currency = 'RUB'): int
    {
        return $this->maximumAmount;
    }

    /**
     * Check if the gateway is available.
     */
    public function isAvailable(): bool
    {
        return $this->config['enabled'] ?? false;
    }

    /**
     * Validate payment request before processing.
     */
    public function validatePaymentRequest(PaymentRequest $request): array
    {
        $errors = [];

        // Basic validation
        $requestErrors = $request->validate();
        $errors = array_merge($errors, $requestErrors);

        // Currency validation
        if (!in_array($request->currency, $this->getSupportedCurrencies())) {
            $errors[] = "Currency {$request->currency} is not supported by {$this->getName()}";
        }

        // Amount validation
        if ($request->amount < $this->getMinimumAmount($request->currency)) {
            $errors[] = "Amount is below minimum for {$this->getName()}";
        }

        if ($request->amount > $this->getMaximumAmount($request->currency)) {
            $errors[] = "Amount exceeds maximum for {$this->getName()}";
        }

        // Recurring validation
        if ($request->isRecurring() && !$this->supportsRecurring()) {
            $errors[] = "{$this->getName()} does not support recurring payments";
        }

        return $errors;
    }

    /**
     * Process a recurring payment.
     */
    public function processRecurringPayment(RecurringPaymentRequest $request): PaymentResponse
    {
        if (!$this->supportsRecurring()) {
            return PaymentResponse::failed(
                'failed',
                'Recurring payments are not supported by this gateway'
            );
        }

        return $this->createPayment($request->toPaymentRequest());
    }

    /**
     * Handle webhook data.
     */
    public function handleWebhook(WebhookData $webhookData): bool
    {
        if (!$this->supportsWebhooks()) {
            Log::warning("Webhook received for gateway that doesn't support webhooks", [
                'gateway' => $this->getName(),
                'data' => $webhookData->toArray(),
            ]);
            return false;
        }

        return $this->processWebhook($webhookData);
    }

    /**
     * Verify webhook signature.
     */
    public function verifyWebhookSignature(array $headers, string $payload): bool
    {
        if (!$this->supportsWebhooks()) {
            return false;
        }

        return $this->doVerifyWebhookSignature($headers, $payload);
    }

    /**
     * Get gateway-specific payment form data.
     */
    public function getPaymentFormData(Payment $payment): array
    {
        return [
            'gateway' => $this->getName(),
            'payment_id' => $payment->id,
            'amount' => $payment->amount,
            'currency' => $payment->currency,
        ];
    }

    /**
     * Process payment confirmation (for two-stage payments).
     */
    public function confirmPayment(string $paymentId): PaymentResponse
    {
        return PaymentResponse::failed(
            'failed',
            'Payment confirmation is not supported by this gateway'
        );
    }

    /**
     * Get payment redirect URL.
     */
    public function getPaymentUrl(Payment $payment): ?string
    {
        return null;
    }

    /**
     * Handle payment callback/return from gateway.
     */
    public function handlePaymentCallback(array $data): PaymentResponse
    {
        return PaymentResponse::failed(
            'failed',
            'Payment callbacks are not supported by this gateway'
        );
    }

    /**
     * Log gateway activity.
     */
    protected function log(string $level, string $message, array $context = []): void
    {
        $context['gateway'] = $this->getName();
        Log::log($level, "[{$this->getName()}] {$message}", $context);
    }

    /**
     * Validate gateway configuration.
     */
    protected function validateConfig(): void
    {
        // Override in child classes for specific validation
    }

    /**
     * Process webhook data (override in child classes).
     */
    protected function processWebhook(WebhookData $webhookData): bool
    {
        return false;
    }

    /**
     * Verify webhook signature (override in child classes).
     */
    protected function doVerifyWebhookSignature(array $headers, string $payload): bool
    {
        return true;
    }

    /**
     * Generate unique payment reference.
     */
    protected function generatePaymentReference(): string
    {
        return uniqid($this->getName() . '_', true);
    }

    /**
     * Format amount for gateway (some gateways expect different formats).
     */
    protected function formatAmount(int $amount): int
    {
        return $amount; // Default: amount in kopecks
    }

    /**
     * Parse amount from gateway response.
     */
    protected function parseAmount($amount): int
    {
        return (int) $amount; // Default: assume amount in kopecks
    }

    /**
     * Get configuration value.
     */
    protected function getConfigValue(string $key, $default = null)
    {
        return $this->config[$key] ?? $default;
    }

    /**
     * Check if configuration has value.
     */
    protected function hasConfigValue(string $key): bool
    {
        return array_key_exists($key, $this->config);
    }
}
