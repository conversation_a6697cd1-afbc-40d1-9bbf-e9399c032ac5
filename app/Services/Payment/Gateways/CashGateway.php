<?php

namespace App\Services\Payment\Gateways;

use App\Services\Payment\AbstractPaymentGateway;
use App\Services\Payment\PaymentResult;
use App\Services\Payment\WebhookResult;

class CashGateway extends AbstractPaymentGateway
{
    public function getName(): string
    {
        return 'cash';
    }

    public function createPayment(array $data): PaymentResult
    {
        $this->log('info', 'Creating cash payment', $data);

        // Generate a cash payment ID
        $paymentId = 'cash_' . uniqid();

        $autoConfirm = $this->getConfig('auto_confirm', false);
        $requireReceipt = $this->getConfig('require_receipt', true);
        $status = $autoConfirm ? 'paid' : 'pending';

        return PaymentResult::success(
            paymentId: $paymentId,
            status: $status,
            data: [
                'order_id' => $data['order_id'] ?? null,
                'amount' => $data['amount'] ?? 0,
                'auto_confirmed' => $autoConfirm,
                'requires_receipt' => $requireReceipt,
                'requires_admin_confirmation' => !$autoConfirm,
            ]
        );
    }

    public function cancelPayment(string $paymentId): PaymentResult
    {
        $this->log('info', 'Cancelling cash payment', ['payment_id' => $paymentId]);

        return PaymentResult::success(
            paymentId: $paymentId,
            status: 'cancelled'
        );
    }

    public function getPaymentStatus(string $paymentId): PaymentResult
    {
        $this->log('info', 'Getting cash payment status', ['payment_id' => $paymentId]);

        // Cash payments require admin confirmation, so status depends on config
        $autoConfirm = $this->getConfig('auto_confirm', false);
        $status = $autoConfirm ? 'paid' : 'pending';

        return PaymentResult::success(
            paymentId: $paymentId,
            status: $status
        );
    }

    public function supportsRefunds(): bool
    {
        return true;
    }

    public function refundPayment(string $paymentId, ?int $amount = null): PaymentResult
    {
        $this->log('info', 'Processing cash refund', [
            'payment_id' => $paymentId,
            'amount' => $amount
        ]);

        return PaymentResult::success(
            paymentId: $paymentId,
            status: 'refunded',
            data: [
                'refund_amount' => $amount,
                'requires_admin_action' => true,
                'refund_method' => 'cash',
            ]
        );
    }

    public function handleWebhook(array $data): WebhookResult
    {
        $this->log('info', 'Cash gateway webhook received', $data);

        // Cash gateway could potentially handle POS system webhooks
        // For now, we'll just log and ignore
        return WebhookResult::ignored(
            'Cash gateway webhooks require manual processing',
            ['webhook_data' => $data]
        );
    }
}
