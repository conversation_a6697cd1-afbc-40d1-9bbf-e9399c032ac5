<?php

namespace App\Services\Payment\Gateways;

use App\Services\Payment\DTOs\PaymentRequest;
use App\Services\Payment\DTOs\PaymentResponse;
use App\Services\Payment\DTOs\RefundRequest;
use App\Services\Payment\DTOs\RefundResponse;
use App\Services\Payment\DTOs\PaymentStatusResponse;
use App\Models\Payment;

class CashGateway extends AbstractPaymentGateway
{
    protected string $name = 'cash';
    protected bool $supportsRefunds = true;
    protected array $supportedCurrencies = ['RUB', 'USD', 'EUR'];
    protected int $minimumAmount = 100; // 1 RUB in kopecks
    protected int $maximumAmount = 10000000; // 100,000 RUB in kopecks (reasonable cash limit)

    /**
     * Create a new payment.
     */
    public function createPayment(PaymentRequest $request): PaymentResponse
    {
        try {
            $this->log('info', 'Creating cash payment', [
                'order_id' => $request->order->id,
                'amount' => $request->amount,
                'currency' => $request->currency,
            ]);

            $paymentId = $this->generatePaymentReference();

            // Cash payments are always pending until receipt is provided
            $autoConfirm = $this->getConfigValue('auto_confirm', false);
            $status = $autoConfirm ? 'paid' : 'pending';

            $this->log('info', 'Cash payment created', [
                'payment_id' => $paymentId,
                'status' => $status,
                'auto_confirm' => $autoConfirm,
            ]);

            return PaymentResponse::success(
                status: $status,
                paymentId: $paymentId,
                data: [
                    'instructions' => $this->getPaymentInstructions($request),
                    'requires_receipt' => $this->getConfigValue('require_receipt', true),
                    'payment_locations' => $this->getPaymentLocations(),
                ]
            );

        } catch (\Exception $e) {
            $this->log('error', 'Failed to create cash payment', [
                'error' => $e->getMessage(),
                'order_id' => $request->order->id,
            ]);

            return PaymentResponse::failed(
                'failed',
                'Failed to create cash payment: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get payment status.
     */
    public function getPaymentStatus(string $paymentId): PaymentStatusResponse
    {
        try {
            $payment = Payment::where('public_id', $paymentId)->first();

            if (!$payment) {
                return PaymentStatusResponse::failed(
                    'not_found',
                    'Payment not found'
                );
            }

            return PaymentStatusResponse::success(
                status: $payment->status,
                paymentId: $paymentId,
                amount: $payment->amount,
                currency: $payment->currency,
                orderId: $payment->order_id,
                createdAt: $payment->created_at,
                confirmedAt: $payment->paid_at,
            );

        } catch (\Exception $e) {
            $this->log('error', 'Failed to get payment status', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
            ]);

            return PaymentStatusResponse::failed(
                'error',
                'Failed to get payment status: ' . $e->getMessage()
            );
        }
    }

    /**
     * Cancel a payment.
     */
    public function cancelPayment(string $paymentId): PaymentResponse
    {
        try {
            $this->log('info', 'Cancelling cash payment', [
                'payment_id' => $paymentId,
            ]);

            return PaymentResponse::success(
                status: 'cancelled',
                paymentId: $paymentId,
                data: ['cancelled_at' => now()->toISOString()]
            );

        } catch (\Exception $e) {
            $this->log('error', 'Failed to cancel cash payment', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
            ]);

            return PaymentResponse::failed(
                'failed',
                'Failed to cancel payment: ' . $e->getMessage()
            );
        }
    }

    /**
     * Refund a payment.
     */
    public function refundPayment(RefundRequest $request): RefundResponse
    {
        try {
            $this->log('info', 'Processing cash refund', [
                'payment_id' => $request->paymentId,
                'amount' => $request->amount,
                'reason' => $request->reason,
            ]);

            $refundId = $this->generateRefundReference();

            return RefundResponse::success(
                status: 'pending',
                refundId: $refundId,
                paymentId: $request->paymentId,
                amount: $request->amount,
                currency: $request->currency,
                data: [
                    'reason' => $request->reason,
                    'refund_method' => 'cash',
                    'requires_manual_processing' => true,
                    'instructions' => 'Cash refund will be processed at our office. Please bring your receipt.',
                ]
            );

        } catch (\Exception $e) {
            $this->log('error', 'Failed to process cash refund', [
                'error' => $e->getMessage(),
                'payment_id' => $request->paymentId,
            ]);

            return RefundResponse::failed(
                'failed',
                'Failed to process refund: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get gateway-specific payment form data.
     */
    public function getPaymentFormData(Payment $payment): array
    {
        return array_merge(parent::getPaymentFormData($payment), [
            'instructions' => $this->getPaymentInstructions($payment),
            'payment_locations' => $this->getPaymentLocations(),
            'requires_receipt' => $this->getConfigValue('require_receipt', true),
            'office_hours' => $this->getOfficeHours(),
        ]);
    }

    /**
     * Confirm payment with receipt (admin action).
     */
    public function confirmPayment(string $paymentId): PaymentResponse
    {
        try {
            $this->log('info', 'Confirming cash payment with receipt', [
                'payment_id' => $paymentId,
            ]);

            return PaymentResponse::success(
                status: 'paid',
                paymentId: $paymentId,
                data: [
                    'confirmed_at' => now()->toISOString(),
                    'confirmation_method' => 'cash_receipt',
                ]
            );

        } catch (\Exception $e) {
            $this->log('error', 'Failed to confirm cash payment', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
            ]);

            return PaymentResponse::failed(
                'failed',
                'Failed to confirm payment: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get payment instructions for the user.
     */
    private function getPaymentInstructions(PaymentRequest|Payment $payment): string
    {
        $amount = $payment instanceof Payment ? $payment->getFormattedAmountAttribute() : $payment->getFormattedAmount();
        $orderId = $payment instanceof Payment ? $payment->order_id : $payment->order->id;

        $instructions = "Please visit our office to make a cash payment of {$amount} for order #{$orderId}. ";
        
        if ($this->getConfigValue('require_receipt', true)) {
            $instructions .= "Please keep your receipt as proof of payment. ";
        }
        
        $instructions .= "Your payment will be confirmed immediately upon receipt.";

        return $instructions;
    }

    /**
     * Get payment locations.
     */
    private function getPaymentLocations(): array
    {
        return $this->getConfigValue('payment_locations', [
            [
                'name' => 'Main Office',
                'address' => 'Please contact support for address',
                'hours' => 'Mon-Fri 9:00-18:00',
                'phone' => 'Please contact support for phone',
            ]
        ]);
    }

    /**
     * Get office hours.
     */
    private function getOfficeHours(): array
    {
        return $this->getConfigValue('office_hours', [
            'monday' => '9:00-18:00',
            'tuesday' => '9:00-18:00',
            'wednesday' => '9:00-18:00',
            'thursday' => '9:00-18:00',
            'friday' => '9:00-18:00',
            'saturday' => 'Closed',
            'sunday' => 'Closed',
        ]);
    }

    /**
     * Generate unique refund reference.
     */
    private function generateRefundReference(): string
    {
        return uniqid('cash_refund_', true);
    }

    /**
     * Validate gateway configuration.
     */
    protected function validateConfig(): void
    {
        if (!isset($this->config['enabled'])) {
            throw new \InvalidArgumentException('Cash gateway enabled flag is required');
        }
    }
}
