<?php

namespace App\Services\Payment\Gateways;

use App\Services\Payment\AbstractPaymentGateway;
use App\Services\Payment\PaymentResult;

class ManualGateway extends AbstractPaymentGateway
{
    public function getName(): string
    {
        return 'manual';
    }

    public function createPayment(array $data): PaymentResult
    {
        $this->log('info', 'Creating manual payment', $data);

        // Generate a manual payment ID
        $paymentId = 'manual_' . uniqid();

        $autoConfirm = $this->getConfig('auto_confirm', false);
        $status = $autoConfirm ? 'paid' : 'pending';

        return PaymentResult::success(
            paymentId: $paymentId,
            status: $status,
            data: [
                'order_id' => $data['order_id'] ?? null,
                'amount' => $data['amount'] ?? 0,
                'auto_confirmed' => $autoConfirm,
                'requires_admin_confirmation' => !$autoConfirm,
            ]
        );
    }

    public function cancelPayment(string $paymentId): PaymentResult
    {
        $this->log('info', 'Cancelling manual payment', ['payment_id' => $paymentId]);

        return PaymentResult::success(
            paymentId: $paymentId,
            status: 'cancelled'
        );
    }

    public function getPaymentStatus(string $paymentId): PaymentResult
    {
        $this->log('info', 'Getting manual payment status', ['payment_id' => $paymentId]);

        // Manual payments require admin confirmation, so status depends on config
        $autoConfirm = $this->getConfig('auto_confirm', false);
        $status = $autoConfirm ? 'paid' : 'pending';

        return PaymentResult::success(
            paymentId: $paymentId,
            status: $status
        );
    }

    public function supportsRefunds(): bool
    {
        return true;
    }

    public function refundPayment(string $paymentId, ?int $amount = null): PaymentResult
    {
        $this->log('info', 'Processing manual refund', [
            'payment_id' => $paymentId,
            'amount' => $amount
        ]);

        return PaymentResult::success(
            paymentId: $paymentId,
            status: 'refunded',
            data: [
                'refund_amount' => $amount,
                'requires_admin_action' => true,
            ]
        );
    }
}
