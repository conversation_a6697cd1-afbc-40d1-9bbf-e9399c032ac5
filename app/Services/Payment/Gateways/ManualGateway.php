<?php

namespace App\Services\Payment\Gateways;

use App\Services\Payment\DTOs\PaymentRequest;
use App\Services\Payment\DTOs\PaymentResponse;
use App\Services\Payment\DTOs\RefundRequest;
use App\Services\Payment\DTOs\RefundResponse;
use App\Services\Payment\DTOs\PaymentStatusResponse;
use App\Models\Payment;

class ManualGateway extends AbstractPaymentGateway
{
    protected string $name = 'manual';
    protected bool $supportsRefunds = true;
    protected array $supportedCurrencies = ['RUB', 'USD', 'EUR'];
    protected int $minimumAmount = 1; // 0.01 RUB in kopecks
    protected int $maximumAmount = PHP_INT_MAX; // No limit for manual payments

    /**
     * Create a new payment.
     */
    public function createPayment(PaymentRequest $request): PaymentResponse
    {
        try {
            $this->log('info', 'Creating manual payment', [
                'order_id' => $request->order->id,
                'amount' => $request->amount,
                'currency' => $request->currency,
            ]);

            // For manual payments, we create the payment in pending status
            // and wait for admin confirmation
            $paymentId = $this->generatePaymentReference();

            // If auto-confirm is enabled, mark as paid immediately
            $autoConfirm = $this->getConfigValue('auto_confirm', false);
            $status = $autoConfirm ? 'paid' : 'pending';

            $this->log('info', 'Manual payment created', [
                'payment_id' => $paymentId,
                'status' => $status,
                'auto_confirm' => $autoConfirm,
            ]);

            // Send admin notification if enabled
            if ($this->getConfigValue('admin_notification', true)) {
                $this->sendAdminNotification($request, $paymentId);
            }

            return PaymentResponse::success(
                status: $status,
                paymentId: $paymentId,
                data: [
                    'instructions' => $this->getPaymentInstructions($request),
                    'auto_confirm' => $autoConfirm,
                ]
            );

        } catch (\Exception $e) {
            $this->log('error', 'Failed to create manual payment', [
                'error' => $e->getMessage(),
                'order_id' => $request->order->id,
            ]);

            return PaymentResponse::failed(
                'failed',
                'Failed to create manual payment: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get payment status.
     */
    public function getPaymentStatus(string $paymentId): PaymentStatusResponse
    {
        try {
            // For manual payments, we need to check the database status
            // since there's no external API to query
            $payment = Payment::where('public_id', $paymentId)->first();

            if (!$payment) {
                return PaymentStatusResponse::failed(
                    'not_found',
                    'Payment not found'
                );
            }

            return PaymentStatusResponse::success(
                status: $payment->status,
                paymentId: $paymentId,
                amount: $payment->amount,
                currency: $payment->currency,
                orderId: $payment->order->public_id, // Use public order ID for external response
                createdAt: $payment->created_at,
                confirmedAt: $payment->paid_at,
            );

        } catch (\Exception $e) {
            $this->log('error', 'Failed to get payment status', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
            ]);

            return PaymentStatusResponse::failed(
                'error',
                'Failed to get payment status: ' . $e->getMessage()
            );
        }
    }

    /**
     * Cancel a payment.
     */
    public function cancelPayment(string $paymentId): PaymentResponse
    {
        try {
            $this->log('info', 'Cancelling manual payment', [
                'payment_id' => $paymentId,
            ]);

            // For manual payments, cancellation is always possible if not yet paid
            return PaymentResponse::success(
                status: 'cancelled',
                paymentId: $paymentId,
                data: ['cancelled_at' => now()->toISOString()]
            );

        } catch (\Exception $e) {
            $this->log('error', 'Failed to cancel manual payment', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
            ]);

            return PaymentResponse::failed(
                'failed',
                'Failed to cancel payment: ' . $e->getMessage()
            );
        }
    }

    /**
     * Refund a payment.
     */
    public function refundPayment(RefundRequest $request): RefundResponse
    {
        try {
            $this->log('info', 'Processing manual refund', [
                'payment_id' => $request->paymentId,
                'amount' => $request->amount,
                'reason' => $request->reason,
            ]);

            // For manual payments, refunds are processed manually by admin
            $refundId = $this->generateRefundReference();

            return RefundResponse::success(
                status: 'pending',
                refundId: $refundId,
                paymentId: $request->paymentId,
                amount: $request->amount,
                currency: $request->currency,
                data: [
                    'reason' => $request->reason,
                    'requires_manual_processing' => true,
                ]
            );

        } catch (\Exception $e) {
            $this->log('error', 'Failed to process manual refund', [
                'error' => $e->getMessage(),
                'payment_id' => $request->paymentId,
            ]);

            return RefundResponse::failed(
                'failed',
                'Failed to process refund: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get gateway-specific payment form data.
     */
    public function getPaymentFormData(Payment $payment): array
    {
        return array_merge(parent::getPaymentFormData($payment), [
            'instructions' => $this->getPaymentInstructions($payment),
            'requires_manual_confirmation' => !$this->getConfigValue('auto_confirm', false),
            'admin_contact' => $this->getConfigValue('admin_contact', '<EMAIL>'),
        ]);
    }

    /**
     * Confirm payment manually (admin action).
     */
    public function confirmPayment(string $paymentId): PaymentResponse
    {
        try {
            $this->log('info', 'Manually confirming payment', [
                'payment_id' => $paymentId,
            ]);

            return PaymentResponse::success(
                status: 'paid',
                paymentId: $paymentId,
                data: [
                    'confirmed_at' => now()->toISOString(),
                    'confirmation_method' => 'manual',
                ]
            );

        } catch (\Exception $e) {
            $this->log('error', 'Failed to confirm manual payment', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
            ]);

            return PaymentResponse::failed(
                'failed',
                'Failed to confirm payment: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get payment instructions for the user.
     */
    private function getPaymentInstructions(PaymentRequest|Payment $payment): string
    {
        $amount = $payment instanceof Payment ? $payment->getFormattedAmountAttribute() : $payment->getFormattedAmount();
        $orderId = $payment instanceof Payment ? $payment->order->public_id : $payment->order->public_id;

        return "Please contact our administrator to complete your payment of {$amount} for order #{$orderId}. " .
               "Your payment will be processed manually and confirmed within 24 hours.";
    }

    /**
     * Send notification to admin about new manual payment.
     */
    private function sendAdminNotification(PaymentRequest $request, string $paymentId): void
    {
        try {
            // Here you would implement actual notification sending
            // For example, using Laravel's notification system
            $this->log('info', 'Admin notification sent for manual payment', [
                'payment_id' => $paymentId,
                'order_id' => $request->order->id,
            ]);
        } catch (\Exception $e) {
            $this->log('warning', 'Failed to send admin notification', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
            ]);
        }
    }

    /**
     * Generate unique refund reference.
     */
    private function generateRefundReference(): string
    {
        return uniqid('manual_refund_', true);
    }

    /**
     * Validate gateway configuration.
     */
    protected function validateConfig(): void
    {
        // Manual gateway has minimal configuration requirements
        if (!isset($this->config['enabled'])) {
            throw new \InvalidArgumentException('Manual gateway enabled flag is required');
        }
    }
}
