<?php

namespace App\Services\Payment\Gateways;

use App\Services\Payment\DTOs\PaymentRequest;
use App\Services\Payment\DTOs\PaymentResponse;
use App\Services\Payment\DTOs\RefundRequest;
use App\Services\Payment\DTOs\RefundResponse;
use App\Services\Payment\DTOs\PaymentStatusResponse;
use App\Services\Payment\DTOs\WebhookData;
use App\Models\Payment;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Client\Response;

class TBankGateway extends AbstractPaymentGateway
{
    protected string $name = 'tbank';
    protected bool $supportsRecurring = true;
    protected bool $supportsWebhooks = true;
    protected bool $supportsRefunds = true;
    protected array $supportedCurrencies = ['RUB', 'USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KZT', 'BYN', 'UAH'];
    protected int $minimumAmount = 100; // 1 RUB in kopecks
    protected int $maximumAmount = ********; // 999,999.99 RUB in kopecks

    private string $apiUrl;
    private string $terminalKey;
    private string $password;

    /**
     * Currency code mapping from ISO 4217 alpha codes to numeric codes.
     * T-Bank API requires numeric currency codes.
     */
    private const CURRENCY_CODES = [
        'RUB' => 643,  // Russian Ruble
        'USD' => 840,  // US Dollar
        'EUR' => 978,  // Euro
        'GBP' => 826,  // British Pound
        'CNY' => 156,  // Chinese Yuan
        'JPY' => 392,  // Japanese Yen
        'KZT' => 398,  // Kazakhstani Tenge
        'BYN' => 933,  // Belarusian Ruble
        'UAH' => 980,  // Ukrainian Hryvnia
    ];

    /**
     * Initialize the gateway with configuration.
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->terminalKey = $this->getConfigValue('terminal_key');
        $this->password = $this->getConfigValue('password');

        // Use test API URL if in test environment
        $environment = $this->getConfigValue('environment', 'production');
        $this->apiUrl = $environment === 'test'
            ? $this->getConfigValue('test_api_url', 'https://rest-api-test.tinkoff.ru/v2')
            : $this->getConfigValue('api_url', 'https://securepay.tinkoff.ru/v2');
    }

    /**
     * Create a new payment.
     */
    public function createPayment(PaymentRequest $request): PaymentResponse
    {
        try {
            $this->log('info', 'Creating T-Bank payment', [
                'order_id' => $request->order->id,
                'amount' => $request->amount,
                'currency' => $request->currency,
            ]);

            $paymentData = $this->buildPaymentData($request);
            $response = $this->makeApiRequest('/Init', $paymentData);

            if (!$response->successful()) {
                return $this->handleApiError($response, 'Failed to create payment');
            }

            $responseData = $response->json();

            if (!($responseData['Success'] ?? false)) {
                return PaymentResponse::failed(
                    'failed',
                    $responseData['Message'] ?? 'Payment creation failed',
                    $responseData['ErrorCode'] ?? null
                );
            }

            $this->log('info', 'T-Bank payment created successfully', [
                'payment_id' => $responseData['PaymentId'],
                'order_id' => $request->order->id,
            ]);

            return PaymentResponse::success(
                status: 'pending',
                paymentId: $responseData['PaymentId'],
                externalPaymentId: $responseData['PaymentId'],
                paymentUrl: $responseData['PaymentURL'] ?? null,
                data: $responseData
            );

        } catch (\Exception $e) {
            $this->log('error', 'Failed to create T-Bank payment', [
                'error' => $e->getMessage(),
                'order_id' => $request->order->id,
            ]);

            return PaymentResponse::failed(
                'failed',
                'Failed to create payment: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get payment status.
     */
    public function getPaymentStatus(string $paymentId): PaymentStatusResponse
    {
        try {
            $data = [
                'TerminalKey' => $this->terminalKey,
                'PaymentId' => $paymentId,
            ];

            $data['Token'] = $this->generateToken($data);
            $response = $this->makeApiRequest('/GetState', $data);

            if (!$response->successful()) {
                return PaymentStatusResponse::failed(
                    'error',
                    'Failed to get payment status'
                );
            }

            $responseData = $response->json();

            if (!($responseData['Success'] ?? false)) {
                return PaymentStatusResponse::failed(
                    $responseData['ErrorCode'] ?? 'error',
                    $responseData['Message'] ?? 'Failed to get payment status'
                );
            }

            return PaymentStatusResponse::success(
                status: $this->mapTBankStatus($responseData['Status']),
                paymentId: $paymentId,
                externalPaymentId: $responseData['PaymentId'],
                amount: $responseData['Amount'] ?? null,
                currency: 'RUB',
                orderId: $responseData['OrderId'] ?? null,
                data: $responseData,
                cardMask: $responseData['Pan'] ?? null,
                cardType: $responseData['CardType'] ?? null,
                rebillId: $responseData['RebillId'] ?? null,
            );

        } catch (\Exception $e) {
            $this->log('error', 'Failed to get T-Bank payment status', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
            ]);

            return PaymentStatusResponse::failed(
                'error',
                'Failed to get payment status: ' . $e->getMessage()
            );
        }
    }

    /**
     * Cancel a payment.
     */
    public function cancelPayment(string $paymentId): PaymentResponse
    {
        try {
            $data = [
                'TerminalKey' => $this->terminalKey,
                'PaymentId' => $paymentId,
            ];

            $data['Token'] = $this->generateToken($data);
            $response = $this->makeApiRequest('/Cancel', $data);

            if (!$response->successful()) {
                return $this->handleApiError($response, 'Failed to cancel payment');
            }

            $responseData = $response->json();

            if (!($responseData['Success'] ?? false)) {
                return PaymentResponse::failed(
                    'failed',
                    $responseData['Message'] ?? 'Payment cancellation failed',
                    $responseData['ErrorCode'] ?? null
                );
            }

            return PaymentResponse::success(
                status: 'cancelled',
                paymentId: $paymentId,
                data: $responseData
            );

        } catch (\Exception $e) {
            $this->log('error', 'Failed to cancel T-Bank payment', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
            ]);

            return PaymentResponse::failed(
                'failed',
                'Failed to cancel payment: ' . $e->getMessage()
            );
        }
    }

    /**
     * Refund a payment.
     */
    public function refundPayment(RefundRequest $request): RefundResponse
    {
        try {
            $data = [
                'TerminalKey' => $this->terminalKey,
                'PaymentId' => $request->paymentId,
                'Amount' => $request->amount,
            ];

            $data['Token'] = $this->generateToken($data);
            $response = $this->makeApiRequest('/Cancel', $data); // T-Bank uses Cancel for refunds

            if (!$response->successful()) {
                return RefundResponse::failed(
                    'failed',
                    'Failed to process refund'
                );
            }

            $responseData = $response->json();

            if (!($responseData['Success'] ?? false)) {
                return RefundResponse::failed(
                    'failed',
                    $responseData['Message'] ?? 'Refund failed',
                    $responseData['ErrorCode'] ?? null
                );
            }

            return RefundResponse::success(
                status: 'completed',
                refundId: $responseData['PaymentId'] . '_refund',
                paymentId: $request->paymentId,
                amount: $request->amount,
                currency: $request->currency,
                data: $responseData
            );

        } catch (\Exception $e) {
            $this->log('error', 'Failed to process T-Bank refund', [
                'error' => $e->getMessage(),
                'payment_id' => $request->paymentId,
            ]);

            return RefundResponse::failed(
                'failed',
                'Failed to process refund: ' . $e->getMessage()
            );
        }
    }

    /**
     * Handle webhook data.
     */
    protected function processWebhook(WebhookData $webhookData): bool
    {
        try {
            $this->log('info', 'Processing T-Bank webhook', [
                'payment_id' => $webhookData->getPaymentId(),
                'status' => $webhookData->getStatus(),
            ]);

            // Verify webhook signature
            if (!$this->doVerifyWebhookSignature($webhookData->headers, $webhookData->payload)) {
                $this->log('warning', 'T-Bank webhook signature verification failed');
                return false;
            }

            // Process the webhook based on status
            $status = $webhookData->getStatus();
            $paymentId = $webhookData->getPaymentId();

            if (!$paymentId) {
                $this->log('warning', 'T-Bank webhook missing payment ID');
                return false;
            }

            // Update payment status in database
            $payment = Payment::where('external_payment_id', $paymentId)->first();
            if ($payment) {
                $mappedStatus = $this->mapTBankStatus($status);

                $this->log('info', 'Found payment for webhook', [
                    'payment_id' => $payment->id,
                    'payment_public_id' => $payment->public_id,
                    'current_status' => $payment->status,
                    'new_status' => $mappedStatus,
                    'order_id' => $payment->order_id,
                ]);

                if (in_array($mappedStatus, ['paid', 'confirmed'])) {
                    $payment->markAsPaid();
                    $this->log('info', 'Payment marked as paid', [
                        'payment_id' => $payment->id,
                        'order_id' => $payment->order_id,
                    ]);
                } elseif (in_array($mappedStatus, ['failed', 'cancelled'])) {
                    $payment->markAsFailed();
                    $this->log('info', 'Payment marked as failed', [
                        'payment_id' => $payment->id,
                        'order_id' => $payment->order_id,
                    ]);
                }
            } else {
                $this->log('warning', 'Payment not found for webhook', [
                    'external_payment_id' => $paymentId,
                    'status' => $status,
                ]);
            }

            return true;

        } catch (\Exception $e) {
            $this->log('error', 'Failed to process T-Bank webhook', [
                'error' => $e->getMessage(),
                'webhook_data' => $webhookData->toArray(),
            ]);

            return false;
        }
    }

    /**
     * Verify webhook signature.
     */
    protected function doVerifyWebhookSignature(array $headers, string $payload): bool
    {
        // Check if signature verification is disabled
        $verifySignature = $this->getConfigValue('verify_signature', env('APP_ENV') !== 'local');
        if (!$verifySignature) {
            $this->log('debug', 'T-Bank webhook signature verification disabled', [
                'environment' => env('APP_ENV'),
                'config_value' => $this->getConfigValue('verify_signature'),
            ]);
            return true;
        }

        try {
            $data = json_decode($payload, true);
            if (!$data) {
                $this->log('warning', 'T-Bank webhook invalid JSON payload', [
                    'payload' => $payload,
                ]);
                return false;
            }

            $receivedToken = $data['Token'] ?? '';
            if (empty($receivedToken)) {
                $this->log('warning', 'T-Bank webhook missing Token field', [
                    'data_keys' => array_keys($data),
                    'payload_sample' => substr($payload, 0, 200) . '...',
                ]);
                return false;
            }

            // Create copy for token generation (don't modify original)
            $dataForToken = $data;
            unset($dataForToken['Token']);

            $expectedToken = $this->generateToken($dataForToken);

            $this->log('debug', 'T-Bank webhook signature verification', [
                'received_token' => $receivedToken,
                'expected_token' => $expectedToken,
                'data_keys' => array_keys($dataForToken),
                'terminal_key' => $this->terminalKey,
                'has_password' => !empty($this->password),
            ]);

            $isValid = hash_equals($expectedToken, $receivedToken);

            if (!$isValid) {
                $this->log('warning', 'T-Bank webhook signature mismatch', [
                    'received_token' => $receivedToken,
                    'expected_token' => $expectedToken,
                    'payload_data' => $dataForToken,
                ]);
            }

            return $isValid;

        } catch (\Exception $e) {
            $this->log('error', 'T-Bank webhook signature verification error', [
                'error' => $e->getMessage(),
                'payload' => $payload,
            ]);

            return false;
        }
    }

    /**
     * Build payment data for T-Bank API.
     */
    private function buildPaymentData(PaymentRequest $request): array
    {
        $data = [
            'TerminalKey' => $this->terminalKey,
            'Amount' => $request->amount,
            'OrderId' => $request->order->public_id,
            'Description' => $request->description,
            'Currency' => $this->getCurrencyCode($request->currency),
            'Language' => $this->getConfigValue('language', 'ru'),
            'PayType' => $this->getConfigValue('pay_type', 'O'),
        ];

        // Add URLs if configured
        if ($request->successUrl || $this->getConfigValue('success_url')) {
            $data['SuccessURL'] = $request->successUrl ?? $this->getConfigValue('success_url');
        }

        if ($request->failUrl || $this->getConfigValue('fail_url')) {
            $data['FailURL'] = $request->failUrl ?? $this->getConfigValue('fail_url');
        }

        if ($request->notificationUrl || $this->getConfigValue('notification_url')) {
            $notificationUrl = $request->notificationUrl ?? $this->getConfigValue('notification_url');

            // If notification URL contains placeholders, resolve them
            if ($notificationUrl && str_contains($notificationUrl, '{')) {
                $notificationUrl = $this->resolveNotificationUrl($notificationUrl);
            }

            $data['NotificationURL'] = $notificationUrl;
        }

        // Add customer data for recurring payments
        if ($request->isRecurring() && $request->customerKey) {
            $data['CustomerKey'] = $request->customerKey;
            $data['Recurrent'] = 'Y';
        }

        // Add rebill ID for recurring payments
        if ($request->isRebill() && $request->rebillId) {
            $data['RebillId'] = $request->rebillId;
        }

        // Add receipt data if provided
        if ($request->receipt) {
            $data['Receipt'] = $request->receipt;
        }

        // Generate token
        $data['Token'] = $this->generateToken($data);

        return $data;
    }

    /**
     * Convert currency code from ISO 4217 alpha to numeric format.
     */
    private function getCurrencyCode(string $currency): int
    {
        $currencyUpper = strtoupper($currency);

        if (!isset(self::CURRENCY_CODES[$currencyUpper])) {
            throw new \InvalidArgumentException("Unsupported currency: {$currency}. Supported currencies: " . implode(', ', array_keys(self::CURRENCY_CODES)));
        }

        return self::CURRENCY_CODES[$currencyUpper];
    }

    /**
     * Resolve notification URL by replacing placeholders with actual values.
     */
    private function resolveNotificationUrl(string $url): string
    {
        // If URL contains placeholders, resolve them
        if (str_contains($url, '{APP_URL}')) {
            $url = str_replace('{APP_URL}', rtrim(config('app.url'), '/'), $url);
        }

        if (str_contains($url, '{TBANK_NOTIFICATION_ROUTE}')) {
            $url = str_replace('{TBANK_NOTIFICATION_ROUTE}', 'webhook/tbank', $url);
        }

        // If the URL still contains placeholders or is invalid, build from scratch
        if (str_contains($url, '{') || !filter_var($url, FILTER_VALIDATE_URL)) {
            try {
                // Try to use named route first
                $url = route('webhook.tbank');
            } catch (\Exception $e) {
                // Fallback to URL helper
                $url = url('/webhook/tbank');
            }
        }

        $this->log('debug', 'Resolved T-Bank notification URL', [
            'original_url' => $url,
            'resolved_url' => $url,
        ]);

        return $url;
    }

    /**
     * Generate T-Bank API token according to T-Bank documentation.
     *
     * Algorithm:
     * 1. Collect root-level parameters (excluding nested objects like DATA, Receipt)
     * 2. Remove Token field if present
     * 3. Add Password parameter
     * 4. Sort parameters alphabetically by key
     * 5. Concatenate values into a string
     * 6. Apply SHA-256 hash with UTF-8 support
     */
    private function generateToken(array $data): string
    {
        // Remove Token if present
        unset($data['Token']);

        // Remove nested objects according to T-Bank documentation
        $excludedFields = ['DATA', 'Receipt', 'Data', 'receipt'];
        foreach ($excludedFields as $field) {
            unset($data[$field]);
        }

        // Add password
        $data['Password'] = $this->password;

        // Sort by key alphabetically
        ksort($data);

        // Create concatenated string from values only
        $concatenated = '';
        foreach ($data as $key => $value) {
            // Convert arrays/objects to JSON, but this should not happen for root-level fields
            if (is_array($value) || is_object($value)) {
                $this->log('warning', 'T-Bank token generation: unexpected array/object value', [
                    'key' => $key,
                    'value' => $value,
                ]);
                $value = json_encode($value);
            }

            // Convert to string and concatenate
            $concatenated .= (string) $value;
        }

        $token = hash('sha256', $concatenated);

        $this->log('debug', 'T-Bank token generation', [
            'data_keys' => array_keys($data),
            'concatenated_length' => strlen($concatenated),
            'token' => $token,
        ]);

        return $token;
    }

    /**
     * Make API request to T-Bank.
     */
    private function makeApiRequest(string $endpoint, array $data): Response
    {
        $url = $this->apiUrl . $endpoint;

        $this->log('debug', 'Making T-Bank API request', [
            'url' => $url,
            'data' => $this->maskSensitiveData($data),
        ]);

        $response = Http::timeout($this->getConfigValue('timeout', 30))
            ->withOptions([
                'verify' => $this->getConfigValue('verify_ssl', true),
            ])
            ->post($url, $data);

        $this->log('debug', 'T-Bank API response', [
            'status' => $response->status(),
            'response' => $this->maskSensitiveData($response->json() ?? []),
        ]);

        return $response;
    }

    /**
     * Handle API error response.
     */
    private function handleApiError(Response $response, string $defaultMessage): PaymentResponse
    {
        $responseData = $response->json() ?? [];

        return PaymentResponse::failed(
            'failed',
            $responseData['Message'] ?? $defaultMessage,
            $responseData['ErrorCode'] ?? (string) $response->status()
        );
    }

    /**
     * Map T-Bank status to internal status.
     */
    private function mapTBankStatus(string $tbankStatus): string
    {
        return match (strtoupper($tbankStatus)) {
            'NEW' => 'pending',
            'FORM_SHOWED' => 'pending',
            'AUTHORIZING' => 'pending',
            'AUTHORIZED' => 'authorized',
            'CONFIRMED' => 'paid',
            'REVERSED' => 'reversed',
            'PARTIAL_REVERSED' => 'partial_reversed',
            'REFUNDED' => 'refunded',
            'PARTIAL_REFUNDED' => 'partial_refunded',
            'REJECTED' => 'failed',
            'DEADLINE_EXPIRED' => 'failed',
            'CANCELED' => 'cancelled',
            'AUTH_FAIL' => 'failed',
            default => 'unknown',
        };
    }

    /**
     * Mask sensitive data for logging.
     */
    private function maskSensitiveData(array $data): array
    {
        $sensitiveFields = ['Password', 'Token', 'TerminalKey'];

        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '***MASKED***';
            }
        }

        return $data;
    }

    /**
     * Validate gateway configuration.
     */
    protected function validateConfig(): void
    {
        $requiredFields = ['terminal_key', 'password'];

        foreach ($requiredFields as $field) {
            if (empty($this->getConfigValue($field))) {
                throw new \InvalidArgumentException("T-Bank gateway requires {$field}");
            }
        }
    }
}
