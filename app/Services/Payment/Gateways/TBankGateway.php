<?php

namespace App\Services\Payment\Gateways;

use App\Services\Payment\AbstractPaymentGateway;
use App\Services\Payment\PaymentResult;
use Illuminate\Support\Facades\Http;

class TBankGateway extends AbstractPaymentGateway
{
    private string $terminalKey;
    private string $password;
    private string $apiUrl;

    public function getName(): string
    {
        return 'tbank';
    }

    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->validateConfig(['terminal_key', 'password']);

        $this->terminalKey = $this->getConfig('terminal_key');
        $this->password = $this->getConfig('password');
        $this->apiUrl = $this->getConfig('api_url', 'https://securepay.tinkoff.ru/v2');
    }

    public function createPayment(array $data): PaymentResult
    {
        try {
            $this->log('info', 'Creating T-Bank payment', $data);

            $orderId = $data['order_id'] ?? throw new \InvalidArgumentException('order_id is required');
            $amount = $data['amount'] ?? throw new \InvalidArgumentException('amount is required');

            // Convert amount from kopecks to rubles (T-Bank expects rubles)
            $amountInRubles = $amount / 100;

            // Prepare payment data
            $paymentData = [
                'TerminalKey' => $this->terminalKey,
                'Amount' => $amountInRubles * 100, // T-Bank expects kopecks actually
                'OrderId' => $orderId,
                'Description' => $data['description'] ?? "Payment for order {$orderId}",
                'PayType' => $this->getConfig('pay_type', 'O'),
                'Language' => $this->getConfig('language', 'ru'),
                'SuccessURL' => $this->buildUrl('success_url', $data),
                'FailURL' => $this->buildUrl('fail_url', $data),
                'NotificationURL' => $this->buildUrl('notification_url', $data),
            ];

            // Add token
            $paymentData['Token'] = $this->generateToken($paymentData);

            // Make API request
            $response = Http::timeout(30)->post($this->apiUrl . '/Init', $paymentData);

            if (!$response->successful()) {
                throw new \RuntimeException('T-Bank API request failed: ' . $response->status());
            }

            $result = $response->json();

            if ($result['Success'] ?? false) {
                $this->log('info', 'T-Bank payment created successfully', [
                    'payment_id' => $result['PaymentId'],
                    'order_id' => $orderId
                ]);

                return PaymentResult::success(
                    paymentId: (string) $result['PaymentId'],
                    paymentUrl: $result['PaymentURL'],
                    status: 'pending',
                    data: [
                        'order_id' => $orderId,
                        'amount' => $amount,
                        'amount_rubles' => $amountInRubles,
                        'terminal_key' => $this->terminalKey,
                    ]
                );
            } else {
                $this->log('error', 'T-Bank payment creation failed', [
                    'error' => $result['Message'] ?? 'Unknown error',
                    'error_code' => $result['ErrorCode'] ?? 'UNKNOWN',
                    'order_id' => $orderId
                ]);

                return PaymentResult::failure(
                    message: $result['Message'] ?? 'Payment creation failed',
                    errorCode: $result['ErrorCode'] ?? 'TBANK_ERROR'
                );
            }

        } catch (\Exception $e) {
            $this->log('error', 'T-Bank payment creation exception', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            return PaymentResult::failure(
                message: 'Payment creation failed: ' . $e->getMessage(),
                errorCode: 'TBANK_EXCEPTION'
            );
        }
    }

    public function cancelPayment(string $paymentId): PaymentResult
    {
        try {
            $this->log('info', 'Cancelling T-Bank payment', ['payment_id' => $paymentId]);

            $cancelData = [
                'TerminalKey' => $this->terminalKey,
                'PaymentId' => $paymentId,
            ];

            $cancelData['Token'] = $this->generateToken($cancelData);

            $response = Http::timeout(30)->post($this->apiUrl . '/Cancel', $cancelData);

            if (!$response->successful()) {
                throw new \RuntimeException('T-Bank API request failed: ' . $response->status());
            }

            $result = $response->json();

            if ($result['Success'] ?? false) {
                return PaymentResult::success(
                    paymentId: $paymentId,
                    status: $this->mapTBankStatus($result['Status'] ?? 'CANCELLED')
                );
            } else {
                return PaymentResult::failure(
                    message: $result['Message'] ?? 'Payment cancellation failed',
                    errorCode: $result['ErrorCode'] ?? 'TBANK_CANCEL_ERROR'
                );
            }

        } catch (\Exception $e) {
            $this->log('error', 'T-Bank payment cancellation exception', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId
            ]);

            return PaymentResult::failure(
                message: 'Payment cancellation failed: ' . $e->getMessage(),
                errorCode: 'TBANK_CANCEL_EXCEPTION'
            );
        }
    }

    public function getPaymentStatus(string $paymentId): PaymentResult
    {
        try {
            $this->log('info', 'Getting T-Bank payment status', ['payment_id' => $paymentId]);

            $statusData = [
                'TerminalKey' => $this->terminalKey,
                'PaymentId' => $paymentId,
            ];

            $statusData['Token'] = $this->generateToken($statusData);

            $response = Http::timeout(30)->post($this->apiUrl . '/GetState', $statusData);

            if (!$response->successful()) {
                throw new \RuntimeException('T-Bank API request failed: ' . $response->status());
            }

            $result = $response->json();

            if ($result['Success'] ?? false) {
                return PaymentResult::success(
                    paymentId: $paymentId,
                    status: $this->mapTBankStatus($result['Status'] ?? 'UNKNOWN'),
                    data: [
                        'tbank_status' => $result['Status'] ?? 'UNKNOWN',
                        'amount' => $result['Amount'] ?? 0,
                    ]
                );
            } else {
                return PaymentResult::failure(
                    message: $result['Message'] ?? 'Status check failed',
                    errorCode: $result['ErrorCode'] ?? 'TBANK_STATUS_ERROR'
                );
            }

        } catch (\Exception $e) {
            $this->log('error', 'T-Bank payment status exception', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId
            ]);

            return PaymentResult::failure(
                message: 'Status check failed: ' . $e->getMessage(),
                errorCode: 'TBANK_STATUS_EXCEPTION'
            );
        }
    }

    public function supportsRecurring(): bool
    {
        return true;
    }

    public function supportsWebhooks(): bool
    {
        return true;
    }

    public function supportsRefunds(): bool
    {
        return true;
    }

    public function handleWebhook(array $data): bool
    {
        $this->log('info', 'Processing T-Bank webhook', $data);

        // Basic webhook validation
        if (!isset($data['PaymentId']) || !isset($data['Status'])) {
            $this->log('warning', 'Invalid T-Bank webhook data', $data);
            return false;
        }

        $paymentId = $data['PaymentId'];
        $status = $data['Status'];
        $success = $data['Success'] ?? false;

        $this->log('info', 'T-Bank webhook processed', [
            'payment_id' => $paymentId,
            'status' => $status,
            'success' => $success
        ]);

        return true;
    }

    private function buildUrl(string $configKey, array $data): string
    {
        $url = $this->getConfig($configKey);

        if (!$url) {
            // Build default URLs
            $baseUrl = rtrim(config('app.url'), '/');
            switch ($configKey) {
                case 'success_url':
                    return $baseUrl . '/payment/success';
                case 'fail_url':
                    return $baseUrl . '/payment/failure';
                case 'notification_url':
                    return $baseUrl . '/webhook/tbank';
                default:
                    return $baseUrl;
            }
        }

        // Replace placeholders - исправляем замену переменных
        $replacements = [
            '{order_id}' => $data['order_id'] ?? '',
            '{app_url}' => rtrim(config('app.url'), '/'),
            '{uuid}' => $data['uuid'] ?? '', // добавляем поддержку UUID
            // Также поддерживаем URL-encoded версии
            '%7Bapp_url%7D' => rtrim(config('app.url'), '/'),
            '%7Buuid%7D' => $data['uuid'] ?? '',
            '%7Border_id%7D' => $data['order_id'] ?? '',
        ];

        $result = str_replace(array_keys($replacements), array_values($replacements), $url);

        $this->log('debug', 'URL building', [
            'config_key' => $configKey,
            'original_url' => $url,
            'final_url' => $result,
            'replacements' => $replacements,
        ]);

        return $result;
    }

    private function mapTBankStatus(string $tbankStatus): string
    {
        return match ($tbankStatus) {
            'NEW' => 'pending',
            'FORM_SHOWED' => 'pending',
            'AUTHORIZING' => 'pending',
            'AUTHORIZED' => 'pending',
            'CONFIRMING' => 'pending',
            'CONFIRMED' => 'paid',
            'REVERSED' => 'cancelled',
            'REFUNDING' => 'refunding',
            'REFUNDED' => 'refunded',
            'REJECTED' => 'failed',
            'DEADLINE_EXPIRED' => 'failed',
            default => 'unknown',
        };
    }

    /**
     * Generate T-Bank API token.
     */
    private function generateToken(array $data): string
    {
        // Remove Token if present
        unset($data['Token']);

        // Add password
        $data['Password'] = $this->password;

        // Sort by key
        ksort($data);

        // Create concatenated string
        $concatenated = '';
        foreach ($data as $value) {
            if (is_array($value)) {
                $value = json_encode($value);
            }
            $concatenated .= $value;
        }

        return hash('sha256', $concatenated);
    }
}
