<?php

namespace App\Services\Payment;

use App\Services\Payment\DTOs\PaymentRequest;
use App\Services\Payment\DTOs\PaymentResponse;
use App\Services\Payment\DTOs\RefundRequest;
use App\Services\Payment\DTOs\RefundResponse;
use App\Services\Payment\DTOs\WebhookData;
use App\Services\Payment\Exceptions\PaymentException;
use App\Models\Payment;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PaymentLogger
{
    private string $logLevel;
    private bool $enableDebug;
    private bool $maskSensitiveData;

    public function __construct()
    {
        $this->logLevel = config('payments.settings.log_level', 'info');
        $this->enableDebug = config('payments.settings.enable_debug', false);
        $this->maskSensitiveData = config('payments.settings.mask_sensitive_data', true);
    }

    /**
     * Log payment creation.
     */
    public function logPaymentCreation(PaymentRequest $request, string $gateway): void
    {
        $this->log('info', 'Payment creation initiated', [
            'gateway' => $gateway,
            'order_id' => $request->order->id,
            'user_id' => $request->user->id,
            'amount' => $request->amount,
            'currency' => $request->currency,
            'recurring' => $request->isRecurring(),
            'rebill' => $request->isRebill(),
        ]);
    }

    /**
     * Log payment response.
     */
    public function logPaymentResponse(PaymentResponse $response, string $gateway): void
    {
        $level = $response->isSuccessful() ? 'info' : 'warning';

        $this->log($level, 'Payment response received', [
            'gateway' => $gateway,
            'success' => $response->isSuccessful(),
            'status' => $response->status,
            'payment_id' => $response->paymentId,
            'external_payment_id' => $response->externalPaymentId,
            'error_code' => $response->errorCode,
            'message' => $response->message,
            'requires_3ds' => $response->requires3DS(),
            'requires_redirect' => $response->requiresRedirect(),
        ]);
    }

    /**
     * Log refund request.
     */
    public function logRefundRequest(RefundRequest $request, string $gateway): void
    {
        $this->log('info', 'Refund request initiated', [
            'gateway' => $gateway,
            'payment_id' => $request->paymentId,
            'amount' => $request->amount,
            'currency' => $request->currency,
            'reason' => $request->reason,
        ]);
    }

    /**
     * Log refund response.
     */
    public function logRefundResponse(RefundResponse $response, string $gateway): void
    {
        $level = $response->isSuccessful() ? 'info' : 'warning';

        $this->log($level, 'Refund response received', [
            'gateway' => $gateway,
            'success' => $response->isSuccessful(),
            'status' => $response->status,
            'refund_id' => $response->refundId,
            'payment_id' => $response->paymentId,
            'amount' => $response->amount,
            'currency' => $response->currency,
            'error_code' => $response->errorCode,
            'message' => $response->message,
        ]);
    }

    /**
     * Log webhook processing.
     */
    public function logWebhookReceived(WebhookData $webhookData, string $gateway): void
    {
        $this->log('info', 'Webhook received', [
            'gateway' => $gateway,
            'payment_id' => $webhookData->getPaymentId(),
            'order_id' => $webhookData->getOrderId(),
            'status' => $webhookData->getStatus(),
            'amount' => $webhookData->getAmount(),
            'currency' => $webhookData->getCurrency(),
            'ip_address' => $webhookData->ipAddress,
            'user_agent' => $webhookData->userAgent,
            'timestamp' => $webhookData->timestamp?->toISOString(),
        ]);
    }

    /**
     * Log webhook processing result.
     */
    public function logWebhookProcessed(WebhookData $webhookData, string $gateway, bool $success): void
    {
        $level = $success ? 'info' : 'warning';

        $this->log($level, 'Webhook processed', [
            'gateway' => $gateway,
            'payment_id' => $webhookData->getPaymentId(),
            'success' => $success,
            'status' => $webhookData->getStatus(),
        ]);
    }

    /**
     * Log payment exception.
     */
    public function logPaymentException(PaymentException $exception, ?string $gateway = null): void
    {
        $context = [
            'exception_class' => get_class($exception),
            'message' => $exception->getMessage(),
            'code' => $exception->getCode(),
            'payment_id' => $exception->getPaymentId(),
            'context' => $exception->getContext(),
        ];

        if ($gateway) {
            $context['gateway'] = $gateway;
        }

        if ($this->enableDebug) {
            $context['trace'] = $exception->getTraceAsString();
        }

        $this->log('error', 'Payment exception occurred', $context);
    }

    /**
     * Log API request (for external gateways).
     */
    public function logApiRequest(string $gateway, string $method, string $url, array $data = []): void
    {
        if (!$this->enableDebug) {
            return;
        }

        $this->log('debug', 'API request sent', [
            'gateway' => $gateway,
            'method' => $method,
            'url' => $this->maskUrl($url),
            'data' => $this->maskSensitiveData ? $this->maskSensitiveFields($data) : $data,
        ]);
    }

    /**
     * Log API response (for external gateways).
     */
    public function logApiResponse(string $gateway, int $statusCode, array $response = []): void
    {
        if (!$this->enableDebug) {
            return;
        }

        $level = $statusCode >= 200 && $statusCode < 300 ? 'debug' : 'warning';

        $this->log($level, 'API response received', [
            'gateway' => $gateway,
            'status_code' => $statusCode,
            'response' => $this->maskSensitiveData ? $this->maskSensitiveFields($response) : $response,
        ]);
    }

    /**
     * Log payment status change.
     */
    public function logPaymentStatusChange(Payment $payment, string $oldStatus, string $newStatus): void
    {
        $this->log('info', 'Payment status changed', [
            'payment_id' => $payment->id, // Internal ID for system tracking
            'public_id' => $payment->public_id, // Public ID for reference
            'order_id' => $payment->order_id, // Internal order ID for system tracking
            'order_public_id' => $payment->order->public_id, // Public order ID for reference
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
            'gateway' => $payment->method->code,
        ]);
    }

    /**
     * Log gateway availability check.
     */
    public function logGatewayAvailability(string $gateway, bool $available, ?string $reason = null): void
    {
        $level = $available ? 'debug' : 'warning';

        $context = [
            'gateway' => $gateway,
            'available' => $available,
        ];

        if ($reason) {
            $context['reason'] = $reason;
        }

        $this->log($level, 'Gateway availability checked', $context);
    }

    /**
     * Log recurring payment processing.
     */
    public function logRecurringPayment(string $gateway, string $customerId, string $rebillId, bool $success): void
    {
        $level = $success ? 'info' : 'warning';

        $this->log($level, 'Recurring payment processed', [
            'gateway' => $gateway,
            'customer_id' => $customerId,
            'rebill_id' => $rebillId,
            'success' => $success,
        ]);
    }

    /**
     * Log with payment context.
     */
    private function log(string $level, string $message, array $context = []): void
    {
        // Add payment system prefix
        $message = "[Payment] {$message}";

        // Add common context
        $context['timestamp'] = now()->toISOString();
        $context['request_id'] = request()->header('X-Request-ID') ?? Str::uuid();

        Log::log($level, $message, $context);
    }

    /**
     * Mask sensitive fields in data.
     */
    private function maskSensitiveFields(array $data): array
    {
        $sensitiveFields = [
            'password',
            'token',
            'secret',
            'key',
            'signature',
            'card_number',
            'cvv',
            'pan',
            'terminal_key',
            'customer_key',
        ];

        return $this->maskFields($data, $sensitiveFields);
    }

    /**
     * Recursively mask fields in array.
     */
    private function maskFields(array $data, array $fieldsToMask): array
    {
        foreach ($data as $key => $value) {
            $lowerKey = strtolower($key);

            if (in_array($lowerKey, $fieldsToMask)) {
                $data[$key] = '***MASKED***';
            } elseif (is_array($value)) {
                $data[$key] = $this->maskFields($value, $fieldsToMask);
            }
        }

        return $data;
    }

    /**
     * Mask sensitive parts of URL.
     */
    private function maskUrl(string $url): string
    {
        // Mask query parameters that might contain sensitive data
        return preg_replace('/([?&])(token|key|signature)=[^&]*/', '$1$2=***MASKED***', $url);
    }

    /**
     * Set log level dynamically.
     */
    public function setLogLevel(string $level): void
    {
        $this->logLevel = $level;
    }

    /**
     * Enable or disable debug logging.
     */
    public function setDebugEnabled(bool $enabled): void
    {
        $this->enableDebug = $enabled;
    }

    /**
     * Enable or disable sensitive data masking.
     */
    public function setSensitiveDataMasking(bool $enabled): void
    {
        $this->maskSensitiveData = $enabled;
    }
}
