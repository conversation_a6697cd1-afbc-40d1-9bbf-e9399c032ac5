<?php

namespace App\Services\Payment\Contracts;

use App\Services\Payment\DTOs\PaymentRequest;
use App\Services\Payment\DTOs\PaymentResponse;
use App\Services\Payment\DTOs\RefundRequest;
use App\Services\Payment\DTOs\RefundResponse;
use App\Services\Payment\DTOs\PaymentStatusResponse;
use App\Services\Payment\DTOs\RecurringPaymentRequest;
use App\Services\Payment\DTOs\WebhookData;
use App\Models\Payment;
use App\Models\PaymentMethod;

interface PaymentGatewayInterface
{
    /**
     * Initialize the gateway with configuration.
     */
    public function initialize(array $config): void;

    /**
     * Create a new payment.
     */
    public function createPayment(PaymentRequest $request): PaymentResponse;

    /**
     * Get payment status.
     */
    public function getPaymentStatus(string $paymentId): PaymentStatusResponse;

    /**
     * Cancel a payment.
     */
    public function cancelPayment(string $paymentId): PaymentResponse;

    /**
     * Refund a payment.
     */
    public function refundPayment(RefundRequest $request): RefundResponse;

    /**
     * Process a recurring payment.
     */
    public function processRecurringPayment(RecurringPaymentRequest $request): PaymentResponse;

    /**
     * Handle webhook data.
     */
    public function handleWebhook(WebhookData $webhookData): bool;

    /**
     * Verify webhook signature.
     */
    public function verifyWebhookSignature(array $headers, string $payload): bool;

    /**
     * Check if the gateway supports recurring payments.
     */
    public function supportsRecurring(): bool;

    /**
     * Check if the gateway supports webhooks.
     */
    public function supportsWebhooks(): bool;

    /**
     * Check if the gateway supports refunds.
     */
    public function supportsRefunds(): bool;

    /**
     * Get the gateway name.
     */
    public function getName(): string;

    /**
     * Get the gateway configuration.
     */
    public function getConfig(): array;

    /**
     * Validate payment request before processing.
     */
    public function validatePaymentRequest(PaymentRequest $request): array;

    /**
     * Get supported currencies.
     */
    public function getSupportedCurrencies(): array;

    /**
     * Get minimum payment amount for the gateway.
     */
    public function getMinimumAmount(string $currency = 'RUB'): int;

    /**
     * Get maximum payment amount for the gateway.
     */
    public function getMaximumAmount(string $currency = 'RUB'): int;

    /**
     * Check if the gateway is available.
     */
    public function isAvailable(): bool;

    /**
     * Get gateway-specific payment form data.
     */
    public function getPaymentFormData(Payment $payment): array;

    /**
     * Process payment confirmation (for two-stage payments).
     */
    public function confirmPayment(string $paymentId): PaymentResponse;

    /**
     * Get payment redirect URL.
     */
    public function getPaymentUrl(Payment $payment): ?string;

    /**
     * Handle payment callback/return from gateway.
     */
    public function handlePaymentCallback(array $data): PaymentResponse;
}
