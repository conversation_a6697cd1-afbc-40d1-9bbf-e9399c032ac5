<?php

namespace App\Services\Payment\DTOs;

use Illuminate\Support\Carbon;

class RefundResponse
{
    public function __construct(
        public readonly bool $success,
        public readonly string $status,
        public readonly ?string $refundId = null,
        public readonly ?string $paymentId = null,
        public readonly ?int $amount = null,
        public readonly ?string $currency = null,
        public readonly ?string $message = null,
        public readonly ?string $errorCode = null,
        public readonly array $data = [],
        public readonly ?Carbon $processedAt = null,
    ) {}

    /**
     * Create a successful refund response.
     */
    public static function success(
        string $status,
        string $refundId,
        string $paymentId,
        int $amount,
        string $currency,
        array $data = [],
        ?Carbon $processedAt = null,
    ): self {
        return new self(
            success: true,
            status: $status,
            refundId: $refundId,
            paymentId: $paymentId,
            amount: $amount,
            currency: $currency,
            data: $data,
            processedAt: $processedAt,
        );
    }

    /**
     * Create a failed refund response.
     */
    public static function failed(
        string $status,
        string $message,
        ?string $errorCode = null,
        ?string $refundId = null,
        ?string $paymentId = null,
        array $data = [],
    ): self {
        return new self(
            success: false,
            status: $status,
            refundId: $refundId,
            paymentId: $paymentId,
            message: $message,
            errorCode: $errorCode,
            data: $data,
        );
    }

    /**
     * Check if the refund is successful.
     */
    public function isSuccessful(): bool
    {
        return $this->success;
    }

    /**
     * Check if the refund is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the refund failed.
     */
    public function isFailed(): bool
    {
        return !$this->success;
    }

    /**
     * Get formatted amount.
     */
    public function getFormattedAmount(): ?string
    {
        if ($this->amount === null || $this->currency === null) {
            return null;
        }

        return number_format($this->amount / 100, 2) . ' ' . $this->currency;
    }

    /**
     * Convert to array.
     */
    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'status' => $this->status,
            'refund_id' => $this->refundId,
            'payment_id' => $this->paymentId,
            'amount' => $this->amount,
            'currency' => $this->currency,
            'message' => $this->message,
            'error_code' => $this->errorCode,
            'data' => $this->data,
            'processed_at' => $this->processedAt?->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Create from array.
     */
    public static function fromArray(array $data): self
    {
        return new self(
            success: $data['success'] ?? false,
            status: $data['status'] ?? 'unknown',
            refundId: $data['refund_id'] ?? null,
            paymentId: $data['payment_id'] ?? null,
            amount: $data['amount'] ?? null,
            currency: $data['currency'] ?? null,
            message: $data['message'] ?? null,
            errorCode: $data['error_code'] ?? null,
            data: $data['data'] ?? [],
            processedAt: isset($data['processed_at']) ? new Carbon($data['processed_at']) : null,
        );
    }
}
