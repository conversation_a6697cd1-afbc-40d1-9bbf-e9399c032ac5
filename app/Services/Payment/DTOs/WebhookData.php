<?php

namespace App\Services\Payment\DTOs;

use Illuminate\Support\Carbon;

class WebhookData
{
    public function __construct(
        public readonly array $headers,
        public readonly string $payload,
        public readonly array $data,
        public readonly string $signature,
        public readonly string $method = 'POST',
        public readonly ?string $userAgent = null,
        public readonly ?string $ipAddress = null,
        public readonly ?Carbon $timestamp = null,
    ) {}

    /**
     * Create from HTTP request data.
     */
    public static function fromRequest(
        array $headers,
        string $payload,
        string $method = 'POST',
        ?string $userAgent = null,
        ?string $ipAddress = null,
    ): self {
        $data = json_decode($payload, true) ?? [];

        // Try to get signature from headers first (standard approach)
        $signature = $headers['signature'] ?? $headers['x-signature'] ?? '';

        // If no signature in headers, check for T-Bank Token field in payload
        if (empty($signature) && isset($data['Token'])) {
            $signature = $data['Token'];
        }

        return new self(
            headers: $headers,
            payload: $payload,
            data: $data,
            signature: $signature,
            method: $method,
            userAgent: $userAgent,
            ipAddress: $ipAddress,
            timestamp: new Carbon(),
        );
    }

    /**
     * Get payment ID from webhook data.
     */
    public function getPaymentId(): ?string
    {
        return $this->data['PaymentId'] ??
               $this->data['payment_id'] ??
               $this->data['id'] ??
               null;
    }

    /**
     * Get order ID from webhook data.
     */
    public function getOrderId(): ?string
    {
        return $this->data['OrderId'] ??
               $this->data['order_id'] ??
               $this->data['orderId'] ??
               null;
    }

    /**
     * Get payment status from webhook data.
     */
    public function getStatus(): ?string
    {
        return $this->data['Status'] ??
               $this->data['status'] ??
               null;
    }

    /**
     * Get payment amount from webhook data.
     */
    public function getAmount(): ?int
    {
        return $this->data['Amount'] ??
               $this->data['amount'] ??
               null;
    }

    /**
     * Get currency from webhook data.
     */
    public function getCurrency(): ?string
    {
        return $this->data['Currency'] ??
               $this->data['currency'] ??
               null;
    }

    /**
     * Get success status from webhook data.
     */
    public function isSuccess(): bool
    {
        $success = $this->data['Success'] ?? $this->data['success'] ?? null;

        if ($success !== null) {
            return (bool) $success;
        }

        // Check status for success indicators
        $status = $this->getStatus();
        return in_array(strtolower($status ?? ''), [
            'confirmed', 'paid', 'completed', 'success', 'authorized'
        ]);
    }

    /**
     * Get error code from webhook data.
     */
    public function getErrorCode(): ?string
    {
        return $this->data['ErrorCode'] ??
               $this->data['error_code'] ??
               $this->data['code'] ??
               null;
    }

    /**
     * Get error message from webhook data.
     */
    public function getMessage(): ?string
    {
        return $this->data['Message'] ??
               $this->data['message'] ??
               $this->data['error_message'] ??
               null;
    }

    /**
     * Get rebill ID from webhook data.
     */
    public function getRebillId(): ?string
    {
        return $this->data['RebillId'] ??
               $this->data['rebill_id'] ??
               null;
    }

    /**
     * Get card mask from webhook data.
     */
    public function getCardMask(): ?string
    {
        return $this->data['Pan'] ??
               $this->data['card_mask'] ??
               $this->data['CardMask'] ??
               null;
    }

    /**
     * Get card type from webhook data.
     */
    public function getCardType(): ?string
    {
        return $this->data['CardType'] ??
               $this->data['card_type'] ??
               null;
    }

    /**
     * Get terminal key from webhook data.
     */
    public function getTerminalKey(): ?string
    {
        return $this->data['TerminalKey'] ??
               $this->data['terminal_key'] ??
               null;
    }

    /**
     * Get specific data field.
     */
    public function getData(string $key, $default = null)
    {
        return $this->data[$key] ?? $default;
    }

    /**
     * Check if webhook contains specific field.
     */
    public function hasData(string $key): bool
    {
        return array_key_exists($key, $this->data);
    }

    /**
     * Get header value.
     */
    public function getHeader(string $name, $default = null)
    {
        $name = strtolower($name);
        $headers = array_change_key_case($this->headers, CASE_LOWER);

        return $headers[$name] ?? $default;
    }

    /**
     * Convert to array.
     */
    public function toArray(): array
    {
        return [
            'headers' => $this->headers,
            'payload' => $this->payload,
            'data' => $this->data,
            'signature' => $this->signature,
            'method' => $this->method,
            'user_agent' => $this->userAgent,
            'ip_address' => $this->ipAddress,
            'timestamp' => $this->timestamp?->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Validate webhook data structure.
     */
    public function validate(): array
    {
        $errors = [];

        if (empty($this->payload)) {
            $errors[] = 'Webhook payload is empty';
        }

        if (empty($this->data)) {
            $errors[] = 'Webhook data is empty or invalid JSON';
        }

        if (empty($this->signature)) {
            $errors[] = 'Webhook signature is missing';
        }

        return $errors;
    }

    /**
     * Check if webhook is from allowed IP.
     */
    public function isFromAllowedIp(array $allowedIps): bool
    {
        if (empty($allowedIps) || empty($this->ipAddress)) {
            return true; // No restrictions or no IP to check
        }

        foreach ($allowedIps as $allowedIp) {
            if ($this->ipMatches($this->ipAddress, $allowedIp)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if IP matches pattern (supports CIDR notation).
     */
    private function ipMatches(string $ip, string $pattern): bool
    {
        if ($ip === $pattern) {
            return true;
        }

        if (strpos($pattern, '/') !== false) {
            // CIDR notation
            [$subnet, $mask] = explode('/', $pattern);
            return (ip2long($ip) & ~((1 << (32 - $mask)) - 1)) === ip2long($subnet);
        }

        return false;
    }
}
