<?php

namespace App\Services\Payment\DTOs;

use Illuminate\Support\Carbon;

class PaymentStatusResponse
{
    public function __construct(
        public readonly bool $success,
        public readonly string $status,
        public readonly ?string $paymentId = null,
        public readonly ?string $externalPaymentId = null,
        public readonly ?int $amount = null,
        public readonly ?string $currency = null,
        public readonly ?string $orderId = null,
        public readonly ?string $message = null,
        public readonly ?string $errorCode = null,
        public readonly array $data = [],
        public readonly ?Carbon $createdAt = null,
        public readonly ?Carbon $authorizedAt = null,
        public readonly ?Carbon $confirmedAt = null,
        public readonly ?string $cardMask = null,
        public readonly ?string $cardType = null,
        public readonly ?string $rebillId = null,
    ) {}

    /**
     * Create a successful status response.
     */
    public static function success(
        string $status,
        string $paymentId,
        ?string $externalPaymentId = null,
        ?int $amount = null,
        ?string $currency = null,
        ?string $orderId = null,
        array $data = [],
        ?Carbon $createdAt = null,
        ?Carbon $authorizedAt = null,
        ?Carbon $confirmedAt = null,
        ?string $cardMask = null,
        ?string $cardType = null,
        ?string $rebillId = null,
    ): self {
        return new self(
            success: true,
            status: $status,
            paymentId: $paymentId,
            externalPaymentId: $externalPaymentId,
            amount: $amount,
            currency: $currency,
            orderId: $orderId,
            data: $data,
            createdAt: $createdAt,
            authorizedAt: $authorizedAt,
            confirmedAt: $confirmedAt,
            cardMask: $cardMask,
            cardType: $cardType,
            rebillId: $rebillId,
        );
    }

    /**
     * Create a failed status response.
     */
    public static function failed(
        string $status,
        string $message,
        ?string $errorCode = null,
        ?string $paymentId = null,
        array $data = [],
    ): self {
        return new self(
            success: false,
            status: $status,
            paymentId: $paymentId,
            message: $message,
            errorCode: $errorCode,
            data: $data,
        );
    }

    /**
     * Check if the payment is completed.
     */
    public function isCompleted(): bool
    {
        return in_array($this->status, ['confirmed', 'paid', 'completed']);
    }

    /**
     * Check if the payment is pending.
     */
    public function isPending(): bool
    {
        return in_array($this->status, ['pending', 'new', 'authorizing', 'authorized']);
    }

    /**
     * Check if the payment failed.
     */
    public function isFailed(): bool
    {
        return in_array($this->status, ['failed', 'rejected', 'canceled', 'auth_fail']);
    }

    /**
     * Check if the payment was refunded.
     */
    public function isRefunded(): bool
    {
        return in_array($this->status, ['refunded', 'partial_refunded']);
    }

    /**
     * Check if the payment was reversed.
     */
    public function isReversed(): bool
    {
        return in_array($this->status, ['reversed', 'partial_reversed']);
    }

    /**
     * Get formatted amount.
     */
    public function getFormattedAmount(): ?string
    {
        if ($this->amount === null || $this->currency === null) {
            return null;
        }

        return number_format($this->amount / 100, 2) . ' ' . $this->currency;
    }

    /**
     * Get card information.
     */
    public function getCardInfo(): ?array
    {
        if ($this->cardMask === null) {
            return null;
        }

        return [
            'mask' => $this->cardMask,
            'type' => $this->cardType,
        ];
    }

    /**
     * Get payment duration.
     */
    public function getPaymentDuration(): ?int
    {
        if ($this->createdAt === null || $this->confirmedAt === null) {
            return null;
        }

        return $this->confirmedAt->getTimestamp() - $this->createdAt->getTimestamp();
    }

    /**
     * Convert to array.
     */
    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'status' => $this->status,
            'payment_id' => $this->paymentId,
            'external_payment_id' => $this->externalPaymentId,
            'amount' => $this->amount,
            'currency' => $this->currency,
            'order_id' => $this->orderId,
            'message' => $this->message,
            'error_code' => $this->errorCode,
            'data' => $this->data,
            'created_at' => $this->createdAt?->format('Y-m-d H:i:s'),
            'authorized_at' => $this->authorizedAt?->format('Y-m-d H:i:s'),
            'confirmed_at' => $this->confirmedAt?->format('Y-m-d H:i:s'),
            'card_mask' => $this->cardMask,
            'card_type' => $this->cardType,
            'rebill_id' => $this->rebillId,
        ];
    }

    /**
     * Create from array.
     */
    public static function fromArray(array $data): self
    {
        return new self(
            success: $data['success'] ?? false,
            status: $data['status'] ?? 'unknown',
            paymentId: $data['payment_id'] ?? null,
            externalPaymentId: $data['external_payment_id'] ?? null,
            amount: $data['amount'] ?? null,
            currency: $data['currency'] ?? null,
            orderId: $data['order_id'] ?? null,
            message: $data['message'] ?? null,
            errorCode: $data['error_code'] ?? null,
            data: $data['data'] ?? [],
            createdAt: isset($data['created_at']) ? new Carbon($data['created_at']) : null,
            authorizedAt: isset($data['authorized_at']) ? new Carbon($data['authorized_at']) : null,
            confirmedAt: isset($data['confirmed_at']) ? new Carbon($data['confirmed_at']) : null,
            cardMask: $data['card_mask'] ?? null,
            cardType: $data['card_type'] ?? null,
            rebillId: $data['rebill_id'] ?? null,
        );
    }
}
