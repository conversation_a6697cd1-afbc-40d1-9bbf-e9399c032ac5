<?php

namespace App\Services\Payment\DTOs;

class PaymentResponse
{
    public function __construct(
        public readonly bool $success,
        public readonly string $status,
        public readonly ?string $paymentId = null,
        public readonly ?string $externalPaymentId = null,
        public readonly ?string $paymentUrl = null,
        public readonly ?string $message = null,
        public readonly ?string $errorCode = null,
        public readonly array $data = [],
        public readonly ?string $rebillId = null,
        public readonly ?array $threeDSData = null,
        public readonly ?string $redirectUrl = null,
    ) {}

    /**
     * Create a successful response.
     */
    public static function success(
        string $status,
        ?string $paymentId = null,
        ?string $externalPaymentId = null,
        ?string $paymentUrl = null,
        array $data = [],
        ?string $rebillId = null,
        ?array $threeDSData = null,
        ?string $redirectUrl = null,
    ): self {
        return new self(
            success: true,
            status: $status,
            paymentId: $paymentId,
            externalPaymentId: $externalPaymentId,
            paymentUrl: $paymentUrl,
            data: $data,
            rebillId: $rebillId,
            threeDSData: $threeDSData,
            redirectUrl: $redirectUrl,
        );
    }

    /**
     * Create a failed response.
     */
    public static function failed(
        string $status,
        string $message,
        ?string $errorCode = null,
        ?string $paymentId = null,
        array $data = [],
    ): self {
        return new self(
            success: false,
            status: $status,
            paymentId: $paymentId,
            message: $message,
            errorCode: $errorCode,
            data: $data,
        );
    }

    /**
     * Create a pending response.
     */
    public static function pending(
        string $paymentId,
        ?string $externalPaymentId = null,
        ?string $paymentUrl = null,
        array $data = [],
        ?array $threeDSData = null,
        ?string $redirectUrl = null,
    ): self {
        return new self(
            success: true,
            status: 'pending',
            paymentId: $paymentId,
            externalPaymentId: $externalPaymentId,
            paymentUrl: $paymentUrl,
            data: $data,
            threeDSData: $threeDSData,
            redirectUrl: $redirectUrl,
        );
    }

    /**
     * Check if the payment is successful.
     */
    public function isSuccessful(): bool
    {
        return $this->success;
    }

    /**
     * Check if the payment is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the payment failed.
     */
    public function isFailed(): bool
    {
        return !$this->success;
    }

    /**
     * Check if 3DS authentication is required.
     */
    public function requires3DS(): bool
    {
        return !empty($this->threeDSData);
    }

    /**
     * Check if redirect is required.
     */
    public function requiresRedirect(): bool
    {
        return !empty($this->redirectUrl) || !empty($this->paymentUrl);
    }

    /**
     * Get the redirect URL.
     */
    public function getRedirectUrl(): ?string
    {
        return $this->redirectUrl ?? $this->paymentUrl;
    }

    /**
     * Get 3DS data.
     */
    public function get3DSData(): ?array
    {
        return $this->threeDSData;
    }

    /**
     * Get error message.
     */
    public function getErrorMessage(): ?string
    {
        return $this->message;
    }

    /**
     * Get error code.
     */
    public function getErrorCode(): ?string
    {
        return $this->errorCode;
    }

    /**
     * Get rebill ID for recurring payments.
     */
    public function getRebillId(): ?string
    {
        return $this->rebillId;
    }

    /**
     * Get additional data.
     */
    public function getData(string $key = null)
    {
        if ($key === null) {
            return $this->data;
        }

        return $this->data[$key] ?? null;
    }

    /**
     * Convert to array.
     */
    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'status' => $this->status,
            'payment_id' => $this->paymentId,
            'external_payment_id' => $this->externalPaymentId,
            'payment_url' => $this->paymentUrl,
            'message' => $this->message,
            'error_code' => $this->errorCode,
            'data' => $this->data,
            'rebill_id' => $this->rebillId,
            'three_ds_data' => $this->threeDSData,
            'redirect_url' => $this->redirectUrl,
        ];
    }

    /**
     * Convert to JSON.
     */
    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_UNESCAPED_UNICODE);
    }

    /**
     * Create from array.
     */
    public static function fromArray(array $data): self
    {
        return new self(
            success: $data['success'] ?? false,
            status: $data['status'] ?? 'unknown',
            paymentId: $data['payment_id'] ?? null,
            externalPaymentId: $data['external_payment_id'] ?? null,
            paymentUrl: $data['payment_url'] ?? null,
            message: $data['message'] ?? null,
            errorCode: $data['error_code'] ?? null,
            data: $data['data'] ?? [],
            rebillId: $data['rebill_id'] ?? null,
            threeDSData: $data['three_ds_data'] ?? null,
            redirectUrl: $data['redirect_url'] ?? null,
        );
    }
}
