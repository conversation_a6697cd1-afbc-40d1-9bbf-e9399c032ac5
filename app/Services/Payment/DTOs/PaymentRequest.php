<?php

namespace App\Services\Payment\DTOs;

use App\Models\Order;
use App\Models\User;

class PaymentRequest
{
    public function __construct(
        public readonly Order $order,
        public readonly User $user,
        public readonly int $amount,
        public readonly string $currency,
        public readonly string $description,
        public readonly ?string $customerKey = null,
        public readonly bool $recurring = false,
        public readonly ?string $successUrl = null,
        public readonly ?string $failUrl = null,
        public readonly ?string $notificationUrl = null,
        public readonly array $metadata = [],
        public readonly ?array $customerInfo = null,
        public readonly ?array $receipt = null,
        public readonly string $language = 'ru',
        public readonly string $payType = 'O', // O = one-stage, T = two-stage
        public readonly ?string $operationInitiatorType = null,
        public readonly ?string $rebillId = null,
    ) {}

    /**
     * Create from order.
     */
    public static function fromOrder(Order $order, array $options = []): self
    {
        return new self(
            order: $order,
            user: $order->user,
            amount: $order->total_amount,
            currency: $order->currency,
            description: $options['description'] ?? "Order #{$order->id}",
            customerKey: $options['customer_key'] ?? $order->user->id,
            recurring: $options['recurring'] ?? false,
            successUrl: $options['success_url'] ?? null,
            failUrl: $options['fail_url'] ?? null,
            notificationUrl: $options['notification_url'] ?? null,
            metadata: $options['metadata'] ?? [],
            customerInfo: $options['customer_info'] ?? [
                'email' => $order->user->email,
                'name' => $order->user->name,
            ],
            receipt: $options['receipt'] ?? null,
            language: $options['language'] ?? 'ru',
            payType: $options['pay_type'] ?? 'O',
            operationInitiatorType: $options['operation_initiator_type'] ?? null,
            rebillId: $options['rebill_id'] ?? null,
        );
    }

    /**
     * Convert to array.
     */
    public function toArray(): array
    {
        return [
            'order_id' => $this->order->id,
            'user_id' => $this->user->id,
            'amount' => $this->amount,
            'currency' => $this->currency,
            'description' => $this->description,
            'customer_key' => $this->customerKey,
            'recurring' => $this->recurring,
            'success_url' => $this->successUrl,
            'fail_url' => $this->failUrl,
            'notification_url' => $this->notificationUrl,
            'metadata' => $this->metadata,
            'customer_info' => $this->customerInfo,
            'receipt' => $this->receipt,
            'language' => $this->language,
            'pay_type' => $this->payType,
            'operation_initiator_type' => $this->operationInitiatorType,
            'rebill_id' => $this->rebillId,
        ];
    }

    /**
     * Get formatted amount in major units.
     */
    public function getFormattedAmount(): string
    {
        return number_format($this->amount / 100, 2) . ' ' . $this->currency;
    }

    /**
     * Check if this is a recurring payment.
     */
    public function isRecurring(): bool
    {
        return $this->recurring;
    }

    /**
     * Check if this is a rebill payment.
     */
    public function isRebill(): bool
    {
        return !empty($this->rebillId);
    }

    /**
     * Get customer email.
     */
    public function getCustomerEmail(): ?string
    {
        return $this->customerInfo['email'] ?? $this->user->email;
    }

    /**
     * Get customer name.
     */
    public function getCustomerName(): ?string
    {
        return $this->customerInfo['name'] ?? $this->user->name;
    }

    /**
     * Get customer phone.
     */
    public function getCustomerPhone(): ?string
    {
        return $this->customerInfo['phone'] ?? null;
    }

    /**
     * Validate the request.
     */
    public function validate(): array
    {
        $errors = [];

        if ($this->amount <= 0) {
            $errors[] = 'Amount must be greater than 0';
        }

        if (empty($this->currency)) {
            $errors[] = 'Currency is required';
        }

        if (empty($this->description)) {
            $errors[] = 'Description is required';
        }

        if ($this->recurring && empty($this->customerKey)) {
            $errors[] = 'Customer key is required for recurring payments';
        }

        if ($this->isRebill() && empty($this->customerKey)) {
            $errors[] = 'Customer key is required for rebill payments';
        }

        return $errors;
    }

    /**
     * Create a copy with modified properties.
     */
    public function with(array $changes): self
    {
        return new self(
            order: $changes['order'] ?? $this->order,
            user: $changes['user'] ?? $this->user,
            amount: $changes['amount'] ?? $this->amount,
            currency: $changes['currency'] ?? $this->currency,
            description: $changes['description'] ?? $this->description,
            customerKey: $changes['customer_key'] ?? $this->customerKey,
            recurring: $changes['recurring'] ?? $this->recurring,
            successUrl: $changes['success_url'] ?? $this->successUrl,
            failUrl: $changes['fail_url'] ?? $this->failUrl,
            notificationUrl: $changes['notification_url'] ?? $this->notificationUrl,
            metadata: $changes['metadata'] ?? $this->metadata,
            customerInfo: $changes['customer_info'] ?? $this->customerInfo,
            receipt: $changes['receipt'] ?? $this->receipt,
            language: $changes['language'] ?? $this->language,
            payType: $changes['pay_type'] ?? $this->payType,
            operationInitiatorType: $changes['operation_initiator_type'] ?? $this->operationInitiatorType,
            rebillId: $changes['rebill_id'] ?? $this->rebillId,
        );
    }
}
