<?php

namespace App\Services\Payment\DTOs;

use App\Models\Order;
use App\Models\User;

class RecurringPaymentRequest
{
    public function __construct(
        public readonly Order $order,
        public readonly User $user,
        public readonly int $amount,
        public readonly string $currency,
        public readonly string $description,
        public readonly string $customerKey,
        public readonly string $rebillId,
        public readonly array $metadata = [],
        public readonly ?array $customerInfo = null,
        public readonly ?array $receipt = null,
        public readonly string $operationInitiatorType = 'R', // MIT COF R - Merchant initiated, recurring
    ) {}

    /**
     * Create from order and rebill ID.
     */
    public static function fromOrder(Order $order, string $rebillId, array $options = []): self
    {
        return new self(
            order: $order,
            user: $order->user,
            amount: $order->total_amount,
            currency: $order->currency,
            description: $options['description'] ?? "Recurring payment for Order #{$order->id}",
            customerKey: $options['customer_key'] ?? $order->user->id,
            rebillId: $rebillId,
            metadata: $options['metadata'] ?? [],
            customerInfo: $options['customer_info'] ?? [
                'email' => $order->user->email,
                'name' => $order->user->name,
            ],
            receipt: $options['receipt'] ?? null,
            operationInitiatorType: $options['operation_initiator_type'] ?? 'R',
        );
    }

    /**
     * Convert to PaymentRequest.
     */
    public function toPaymentRequest(): PaymentRequest
    {
        return new PaymentRequest(
            order: $this->order,
            user: $this->user,
            amount: $this->amount,
            currency: $this->currency,
            description: $this->description,
            customerKey: $this->customerKey,
            recurring: true,
            metadata: $this->metadata,
            customerInfo: $this->customerInfo,
            receipt: $this->receipt,
            operationInitiatorType: $this->operationInitiatorType,
            rebillId: $this->rebillId,
        );
    }

    /**
     * Convert to array.
     */
    public function toArray(): array
    {
        return [
            'order_id' => $this->order->id,
            'user_id' => $this->user->id,
            'amount' => $this->amount,
            'currency' => $this->currency,
            'description' => $this->description,
            'customer_key' => $this->customerKey,
            'rebill_id' => $this->rebillId,
            'metadata' => $this->metadata,
            'customer_info' => $this->customerInfo,
            'receipt' => $this->receipt,
            'operation_initiator_type' => $this->operationInitiatorType,
        ];
    }

    /**
     * Get formatted amount.
     */
    public function getFormattedAmount(): string
    {
        return number_format($this->amount / 100, 2) . ' ' . $this->currency;
    }

    /**
     * Get customer email.
     */
    public function getCustomerEmail(): ?string
    {
        return $this->customerInfo['email'] ?? $this->user->email;
    }

    /**
     * Get customer name.
     */
    public function getCustomerName(): ?string
    {
        return $this->customerInfo['name'] ?? $this->user->name;
    }

    /**
     * Validate the request.
     */
    public function validate(): array
    {
        $errors = [];

        if ($this->amount <= 0) {
            $errors[] = 'Amount must be greater than 0';
        }

        if (empty($this->currency)) {
            $errors[] = 'Currency is required';
        }

        if (empty($this->description)) {
            $errors[] = 'Description is required';
        }

        if (empty($this->customerKey)) {
            $errors[] = 'Customer key is required for recurring payments';
        }

        if (empty($this->rebillId)) {
            $errors[] = 'Rebill ID is required for recurring payments';
        }

        return $errors;
    }
}
