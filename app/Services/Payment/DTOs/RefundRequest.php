<?php

namespace App\Services\Payment\DTOs;

class RefundRequest
{
    public function __construct(
        public readonly string $paymentId,
        public readonly int $amount,
        public readonly string $currency,
        public readonly string $reason,
        public readonly ?string $externalPaymentId = null,
        public readonly ?string $refundId = null,
        public readonly array $metadata = [],
        public readonly ?array $receipt = null,
    ) {}

    /**
     * Create a full refund request.
     */
    public static function fullRefund(
        string $paymentId,
        int $amount,
        string $currency,
        string $reason,
        ?string $externalPaymentId = null,
        array $metadata = [],
    ): self {
        return new self(
            paymentId: $paymentId,
            amount: $amount,
            currency: $currency,
            reason: $reason,
            externalPaymentId: $externalPaymentId,
            metadata: $metadata,
        );
    }

    /**
     * Create a partial refund request.
     */
    public static function partialRefund(
        string $paymentId,
        int $refundAmount,
        string $currency,
        string $reason,
        ?string $externalPaymentId = null,
        array $metadata = [],
    ): self {
        return new self(
            paymentId: $paymentId,
            amount: $refundAmount,
            currency: $currency,
            reason: $reason,
            externalPaymentId: $externalPaymentId,
            metadata: $metadata,
        );
    }

    /**
     * Get formatted amount.
     */
    public function getFormattedAmount(): string
    {
        return number_format($this->amount / 100, 2) . ' ' . $this->currency;
    }

    /**
     * Convert to array.
     */
    public function toArray(): array
    {
        return [
            'payment_id' => $this->paymentId,
            'amount' => $this->amount,
            'currency' => $this->currency,
            'reason' => $this->reason,
            'external_payment_id' => $this->externalPaymentId,
            'refund_id' => $this->refundId,
            'metadata' => $this->metadata,
            'receipt' => $this->receipt,
        ];
    }

    /**
     * Validate the request.
     */
    public function validate(): array
    {
        $errors = [];

        if (empty($this->paymentId)) {
            $errors[] = 'Payment ID is required';
        }

        if ($this->amount <= 0) {
            $errors[] = 'Refund amount must be greater than 0';
        }

        if (empty($this->currency)) {
            $errors[] = 'Currency is required';
        }

        if (empty($this->reason)) {
            $errors[] = 'Refund reason is required';
        }

        return $errors;
    }
}
