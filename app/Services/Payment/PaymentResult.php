<?php

namespace App\Services\Payment;

class PaymentResult
{
    public function __construct(
        public readonly bool $success,
        public readonly ?string $paymentId = null,
        public readonly ?string $paymentUrl = null,
        public readonly ?string $status = null,
        public readonly ?string $message = null,
        public readonly ?string $errorCode = null,
        public readonly array $data = []
    ) {}

    public static function success(
        ?string $paymentId = null,
        ?string $paymentUrl = null,
        ?string $status = null,
        array $data = []
    ): self {
        return new self(
            success: true,
            paymentId: $paymentId,
            paymentUrl: $paymentUrl,
            status: $status,
            data: $data
        );
    }

    public static function failure(
        string $message,
        ?string $errorCode = null,
        array $data = []
    ): self {
        return new self(
            success: false,
            message: $message,
            errorCode: $errorCode,
            data: $data
        );
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function isFailure(): bool
    {
        return !$this->success;
    }

    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'payment_id' => $this->paymentId,
            'payment_url' => $this->paymentUrl,
            'status' => $this->status,
            'message' => $this->message,
            'error_code' => $this->errorCode,
            'data' => $this->data,
        ];
    }
}
