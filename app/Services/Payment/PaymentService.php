<?php

namespace App\Services\Payment;

use App\Services\Payment\DTOs\PaymentRequest;
use App\Services\Payment\DTOs\PaymentResponse;
use App\Services\Payment\DTOs\RefundRequest;
use App\Services\Payment\DTOs\RefundResponse;
use App\Services\Payment\DTOs\PaymentStatusResponse;
use App\Services\Payment\DTOs\RecurringPaymentRequest;
use App\Services\Payment\PaymentGatewayManager;
use App\Services\Payment\Exceptions\PaymentException;
use App\Services\Payment\Exceptions\GatewayNotFoundException;
use App\Services\Payment\Exceptions\PaymentValidationException;
use App\Models\Payment;
use App\Models\PaymentMethod;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class PaymentService
{
    private PaymentGatewayManager $gatewayManager;

    public function __construct(PaymentGatewayManager $gatewayManager)
    {
        $this->gatewayManager = $gatewayManager;
    }

    /**
     * Create a new payment.
     */
    public function createPayment(PaymentRequest $request, ?string $gatewayName = null): PaymentResponse
    {
        try {
            DB::beginTransaction();

            // Get the payment method
            $paymentMethod = $this->getPaymentMethod($gatewayName);

            // Get the gateway
            $gateway = $this->gatewayManager->getGateway($paymentMethod->code);

            // Validate the request
            $validationErrors = $gateway->validatePaymentRequest($request);
            if (!empty($validationErrors)) {
                throw new PaymentValidationException('Payment validation failed: ' . implode(', ', $validationErrors));
            }

            // Create payment record in database
            $payment = $this->createPaymentRecord($request, $paymentMethod);

            Log::info('Creating payment', [
                'payment_id' => $payment->id,
                'order_id' => $request->order->id,
                'gateway' => $gatewayName,
                'amount' => $request->amount,
            ]);

            // Process payment through gateway
            $response = $gateway->createPayment($request);

            // Update payment record with gateway response
            $this->updatePaymentFromResponse($payment, $response);

            DB::commit();

            Log::info('Payment created successfully', [
                'payment_id' => $payment->id,
                'gateway_payment_id' => $response->paymentId,
                'status' => $response->status,
            ]);

            return $response;

        } catch (PaymentException $e) {
            DB::rollBack();
            Log::error('Payment creation failed', [
                'error' => $e->getMessage(),
                'order_id' => $request->order->id,
                'gateway' => $gatewayName,
            ]);
            throw $e;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Unexpected error during payment creation', [
                'error' => $e->getMessage(),
                'order_id' => $request->order->id,
                'gateway' => $gatewayName,
            ]);
            throw new PaymentException('Payment creation failed: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Get payment status.
     */
    public function getPaymentStatus(string $paymentId, ?string $gatewayName = null): PaymentStatusResponse
    {
        try {
            $payment = $this->findPayment($paymentId);
            $gateway = $this->gatewayManager->getGateway($gatewayName ?? $payment->method->code);

            $response = $gateway->getPaymentStatus($payment->public_id);

            // Update payment status if changed
            if ($response->isSuccessful() && $response->status !== $payment->status) {
                $this->updatePaymentStatus($payment, $response->status);
            }

            return $response;

        } catch (\Exception $e) {
            Log::error('Failed to get payment status', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
            ]);
            throw new PaymentException('Failed to get payment status: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Cancel a payment.
     */
    public function cancelPayment(string $paymentId, ?string $gatewayName = null): PaymentResponse
    {
        try {
            DB::beginTransaction();

            $payment = $this->findPayment($paymentId);

            if ($payment->isSuccessful()) {
                throw new PaymentException('Cannot cancel a successful payment');
            }

            $gateway = $this->gatewayManager->getGateway($gatewayName ?? $payment->method->code);
            $response = $gateway->cancelPayment($payment->public_id);

            if ($response->isSuccessful()) {
                $payment->markAsCancelled();
            }

            DB::commit();

            Log::info('Payment cancelled', [
                'payment_id' => $payment->id,
                'status' => $response->status,
            ]);

            return $response;

        } catch (PaymentException $e) {
            DB::rollBack();
            throw $e;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to cancel payment', [
                'error' => $e->getMessage(),
                'payment_id' => $paymentId,
            ]);
            throw new PaymentException('Failed to cancel payment: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Refund a payment.
     */
    public function refundPayment(RefundRequest $request, ?string $gatewayName = null): RefundResponse
    {
        try {
            DB::beginTransaction();

            $payment = $this->findPayment($request->paymentId);

            if (!$payment->isSuccessful()) {
                throw new PaymentException('Cannot refund a non-successful payment');
            }

            $gateway = $this->gatewayManager->getGateway($gatewayName ?? $payment->method->code);

            if (!$gateway->supportsRefunds()) {
                throw new PaymentException('Gateway does not support refunds');
            }

            $response = $gateway->refundPayment($request);

            // Create refund record if successful
            if ($response->isSuccessful()) {
                $this->createRefundRecord($payment, $request, $response);
            }

            DB::commit();

            Log::info('Payment refund processed', [
                'payment_id' => $payment->id,
                'refund_id' => $response->refundId,
                'amount' => $request->amount,
            ]);

            return $response;

        } catch (PaymentException $e) {
            DB::rollBack();
            throw $e;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to process refund', [
                'error' => $e->getMessage(),
                'payment_id' => $request->paymentId,
            ]);
            throw new PaymentException('Failed to process refund: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Process recurring payment.
     */
    public function processRecurringPayment(RecurringPaymentRequest $request, ?string $gatewayName = null): PaymentResponse
    {
        try {
            $paymentMethod = $this->getPaymentMethod($gatewayName);
            $gateway = $this->gatewayManager->getGateway($paymentMethod->code);

            if (!$gateway->supportsRecurring()) {
                throw new PaymentException('Gateway does not support recurring payments');
            }

            return $this->createPayment($request->toPaymentRequest(), $paymentMethod->code);

        } catch (\Exception $e) {
            Log::error('Failed to process recurring payment', [
                'error' => $e->getMessage(),
                'order_id' => $request->order->id,
                'rebill_id' => $request->rebillId,
            ]);
            throw new PaymentException('Failed to process recurring payment: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Get available payment methods.
     */
    public function getAvailablePaymentMethods(): array
    {
        return PaymentMethod::active()
            ->get()
            ->filter(function ($method) {
                try {
                    $gateway = $this->gatewayManager->getGateway($method->code);
                    return $gateway->isAvailable();
                } catch (\Exception $e) {
                    Log::warning('Payment method unavailable', [
                        'method' => $method->code,
                        'error' => $e->getMessage(),
                    ]);
                    return false;
                }
            })
            ->map(function ($method) {
                $gateway = $this->gatewayManager->getGateway($method->code);
                return [
                    'code' => $method->code,
                    'name' => $method->name,
                    'type' => $method->type,
                    'supports_recurring' => $gateway->supportsRecurring(),
                    'supports_refunds' => $gateway->supportsRefunds(),
                    'supported_currencies' => $gateway->getSupportedCurrencies(),
                    'minimum_amount' => $gateway->getMinimumAmount(),
                    'maximum_amount' => $gateway->getMaximumAmount(),
                ];
            })
            ->values()
            ->toArray();
    }

    /**
     * Get payment form data for a specific gateway.
     */
    public function getPaymentFormData(Payment $payment): array
    {
        try {
            $gateway = $this->gatewayManager->getGateway($payment->method->code);
            return $gateway->getPaymentFormData($payment);
        } catch (\Exception $e) {
            Log::error('Failed to get payment form data', [
                'error' => $e->getMessage(),
                'payment_id' => $payment->id,
            ]);
            return [];
        }
    }

    /**
     * Get payment URL for redirect-based payments.
     */
    public function getPaymentUrl(Payment $payment): ?string
    {
        try {
            $gateway = $this->gatewayManager->getGateway($payment->method->code);
            return $gateway->getPaymentUrl($payment);
        } catch (\Exception $e) {
            Log::error('Failed to get payment URL', [
                'error' => $e->getMessage(),
                'payment_id' => $payment->id,
            ]);
            return null;
        }
    }

    /**
     * Create payment record in database.
     */
    private function createPaymentRecord(PaymentRequest $request, PaymentMethod $paymentMethod): Payment
    {
        return Payment::create([
            'public_id' => Payment::generatePublicId($paymentMethod),
            'order_id' => $request->order->id,
            'method_id' => $paymentMethod->id,
            'status' => 'pending',
            'amount' => $request->amount,
            'currency' => $request->currency,
            'raw_payload' => $request->toArray(),
        ]);
    }

    /**
     * Update payment record from gateway response.
     */
    private function updatePaymentFromResponse(Payment $payment, PaymentResponse $response): void
    {
        $updateData = [
            'status' => $response->status,
        ];

        if ($response->externalPaymentId) {
            $updateData['external_payment_id'] = $response->externalPaymentId;
        }

        if ($response->isSuccessful() && $response->status === 'paid') {
            $updateData['paid_at'] = now();
        }

        $payment->update($updateData);
    }

    /**
     * Update payment status.
     */
    private function updatePaymentStatus(Payment $payment, string $status): void
    {
        $updateData = ['status' => $status];

        if ($status === 'paid' && !$payment->paid_at) {
            $updateData['paid_at'] = now();
        }

        $payment->update($updateData);
    }

    /**
     * Find payment by ID.
     */
    private function findPayment(string $paymentId): Payment
    {
        $payment = Payment::where('public_id', $paymentId)
            ->orWhere('id', $paymentId)
            ->with('method')
            ->first();

        if (!$payment) {
            throw new PaymentException('Payment not found');
        }

        return $payment;
    }

    /**
     * Get payment method.
     */
    private function getPaymentMethod(?string $gatewayName = null): PaymentMethod
    {
        if (!$gatewayName) {
            $gatewayName = config('payments.default');
        }

        $paymentMethod = PaymentMethod::where('code', $gatewayName)->first();

        if (!$paymentMethod) {
            throw new GatewayNotFoundException("Payment method '{$gatewayName}' not found");
        }

        return $paymentMethod;
    }

    /**
     * Create refund record.
     */
    private function createRefundRecord(Payment $payment, RefundRequest $request, RefundResponse $response): void
    {
        // Here you would create a refund record in your database
        // This depends on your refund tracking requirements
        Log::info('Refund record created', [
            'payment_id' => $payment->id,
            'refund_id' => $response->refundId,
            'amount' => $request->amount,
            'reason' => $request->reason,
        ]);
    }
}
