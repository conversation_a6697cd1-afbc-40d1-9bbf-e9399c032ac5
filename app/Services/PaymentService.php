<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Payment;
use App\Models\PaymentMethod;
use App\Models\PaymentWebhook;
use App\Services\Payment\PaymentGatewayInterface;
use App\Services\Payment\PaymentResult;
use App\Services\Payment\WebhookResult;
use App\Services\Payment\Gateways\TBankGateway;
use App\Services\Payment\Gateways\FreeGateway;
use App\Services\Payment\Gateways\ManualGateway;
use App\Services\Payment\Gateways\CashGateway;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PaymentService
{
    private array $gateways = [];

    public function __construct()
    {
        $this->initializeGateways();
    }

    /**
     * Create a payment for an order.
     */
    public function createPayment(Order $order, string $methodCode, array $additionalData = []): PaymentResult
    {
        return DB::transaction(function () use ($order, $methodCode, $additionalData) {
            try {
                // Get payment method
                $paymentMethod = PaymentMethod::where('code', $methodCode)
                    ->where('is_active', true)
                    ->firstOrFail();

                // Get gateway
                $gateway = $this->getGateway($methodCode);

                // Create payment record
                $payment = Payment::create([
                    'public_id' => $this->generatePaymentId($methodCode),
                    'order_id' => $order->id,
                    'method_id' => $paymentMethod->id,
                    'status' => 'pending',
                    'amount' => $order->total_amount,
                    'currency' => $order->currency,
                ]);

                // Create payment through gateway
                $result = $gateway->createPayment(array_merge([
                    'order_id' => $order->public_id,
                    'payment_id' => $payment->public_id,
                    'amount' => $order->total_amount,
                    'currency' => $order->currency,
                    'description' => "Order {$order->public_id}",
                ], $additionalData));

                if ($result->isSuccess()) {
                    // Update payment with gateway data
                    $updateData = [
                        'external_payment_id' => $result->paymentId,
                        'raw_payload' => $result->data,
                    ];

                    // Сохраняем дополнительные данные от платежной системы
                    if (!empty($result->data)) {
                        $externalDetails = [
                            'payment_url' => $result->paymentUrl,
                            'gateway_response' => $result->data,
                            'created_at' => now()->toISOString(),
                        ];
                        $updateData['external_details'] = $externalDetails;
                    }

                    $payment->update($updateData);

                    // Update payment status if auto-confirmed
                    if ($result->status === 'paid') {
                        $this->updatePaymentStatus($payment, 'paid');
                    }

                    Log::info('Payment created successfully', [
                        'payment_id' => $payment->public_id,
                        'order_id' => $order->public_id,
                        'method' => $methodCode,
                        'amount' => $order->total_amount,
                        'gateway_payment_id' => $result->paymentId,
                    ]);

                    return $result;
                } else {
                    // Mark payment as failed
                    $payment->update(['status' => 'failed']);

                    Log::error('Payment creation failed', [
                        'payment_id' => $payment->public_id,
                        'order_id' => $order->public_id,
                        'method' => $methodCode,
                        'error' => $result->message,
                    ]);

                    return $result;
                }

            } catch (\Exception $e) {
                Log::error('Payment creation exception', [
                    'order_id' => $order->public_id,
                    'method' => $methodCode,
                    'error' => $e->getMessage(),
                ]);

                return PaymentResult::failure(
                    message: 'Payment creation failed: ' . $e->getMessage(),
                    errorCode: 'PAYMENT_CREATION_ERROR'
                );
            }
        });
    }

    /**
     * Update payment status.
     */
    public function updatePaymentStatus(Payment $payment, string $status, ?string $notes = null): bool
    {
        $oldStatus = $payment->status;

        $updateData = ['status' => $status];

        if ($status === 'paid' && !$payment->paid_at) {
            $updateData['paid_at'] = now();
        }

        $updated = $payment->update($updateData);

        if ($updated) {
            Log::info('Payment status updated', [
                'payment_id' => $payment->public_id,
                'old_status' => $oldStatus,
                'new_status' => $status,
                'notes' => $notes,
            ]);

            // Update order status if payment is paid
            if ($status === 'paid' && $payment->order) {
                app(OrderService::class)->updateOrderStatus($payment->order, 'paid');
            }
        }

        return $updated;
    }

    /**
     * Cancel payment.
     */
    public function cancelPayment(Payment $payment): PaymentResult
    {
        try {
            $gateway = $this->getGateway($payment->method->code);

            $result = $gateway->cancelPayment($payment->external_payment_id ?? $payment->public_id);

            if ($result->isSuccess()) {
                $this->updatePaymentStatus($payment, 'cancelled');
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('Payment cancellation exception', [
                'payment_id' => $payment->public_id,
                'error' => $e->getMessage(),
            ]);

            return PaymentResult::failure(
                message: 'Payment cancellation failed: ' . $e->getMessage(),
                errorCode: 'PAYMENT_CANCEL_ERROR'
            );
        }
    }

    /**
     * Refund payment.
     */
    public function refundPayment(Payment $payment, ?int $amount = null): PaymentResult
    {
        try {
            $gateway = $this->getGateway($payment->method->code);

            if (!$gateway->supportsRefunds()) {
                return PaymentResult::failure(
                    message: 'Refunds not supported by this payment method',
                    errorCode: 'REFUNDS_NOT_SUPPORTED'
                );
            }

            $result = $gateway->refundPayment(
                $payment->external_payment_id ?? $payment->public_id,
                $amount
            );

            if ($result->isSuccess()) {
                $this->updatePaymentStatus($payment, 'refunded');
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('Payment refund exception', [
                'payment_id' => $payment->public_id,
                'error' => $e->getMessage(),
            ]);

            return PaymentResult::failure(
                message: 'Payment refund failed: ' . $e->getMessage(),
                errorCode: 'PAYMENT_REFUND_ERROR'
            );
        }
    }

    /**
     * Handle webhook notification.
     */
    public function handleWebhook(string $gatewayCode, array $data, array $headers = []): bool
    {
        try {
            // Log webhook
            $this->logWebhook($gatewayCode, $data, $headers);

            // Get gateway
            $gateway = $this->getGateway($gatewayCode);

            if (!$gateway->supportsWebhooks()) {
                Log::warning('Webhook received for gateway that does not support webhooks', [
                    'gateway' => $gatewayCode,
                ]);
                return false;
            }

            // Process webhook through gateway
            $webhookResult = $gateway->handleWebhook($data);

            if ($webhookResult->isSuccess()) {
                // Update payment if webhook contains payment update
                if ($webhookResult->hasPaymentUpdate()) {
                    $this->processWebhookPaymentUpdate($webhookResult);
                }

                Log::info('Webhook processed successfully', [
                    'gateway' => $gatewayCode,
                    'payment_id' => $webhookResult->paymentId,
                    'status' => $webhookResult->status,
                    'message' => $webhookResult->message,
                ]);

                return true;
            } else {
                Log::warning('Webhook processing failed', [
                    'gateway' => $gatewayCode,
                    'error' => $webhookResult->message,
                    'error_code' => $webhookResult->errorCode,
                ]);

                return false;
            }

        } catch (\Exception $e) {
            Log::error('Webhook processing exception', [
                'gateway' => $gatewayCode,
                'error' => $e->getMessage(),
                'data' => $data,
            ]);

            return false;
        }
    }

    /**
     * Get payment gateway.
     */
    public function getGateway(string $code): PaymentGatewayInterface
    {
        if (!isset($this->gateways[$code])) {
            throw new \InvalidArgumentException("Payment gateway '{$code}' not found");
        }

        return $this->gateways[$code];
    }

    /**
     * Get payment by public ID.
     */
    public function getPaymentByPublicId(string $publicId): ?Payment
    {
        return Payment::where('public_id', $publicId)->first();
    }

    /**
     * Initialize payment gateways.
     */
    private function initializeGateways(): void
    {
        $gatewayConfigs = config('payments.gateways', []);

        foreach ($gatewayConfigs as $code => $config) {
            if (!($config['enabled'] ?? false)) {
                continue;
            }

            $gateway = $this->createGateway($code, $config);
            if ($gateway) {
                $this->gateways[$code] = $gateway;
            }
        }
    }

    /**
     * Create gateway instance.
     */
    private function createGateway(string $code, array $config): ?PaymentGatewayInterface
    {
        $gateway = match ($code) {
            'tbank' => new TBankGateway(),
            'free' => new FreeGateway(),
            'manual' => new ManualGateway(),
            'cash' => new CashGateway(),
            default => null,
        };

        if ($gateway) {
            $gateway->initialize($config['config'] ?? []);
        }

        return $gateway;
    }

    /**
     * Log webhook data.
     */
    private function logWebhook(string $gatewayCode, array $data, array $headers): void
    {
        try {
            $paymentMethod = PaymentMethod::where('code', $gatewayCode)->first();

            if ($paymentMethod) {
                PaymentWebhook::create([
                    'method_id' => $paymentMethod->id,
                    'external_payment_id' => $data['PaymentId'] ?? $data['payment_id'] ?? null,
                    'raw_payload' => is_array($data) ? $data : json_decode($data, true),
                    'headers' => is_array($headers) ? $headers : json_decode($headers, true),
                    'ip_address' => request()->ip(),
                    'user_agent' => request()->userAgent(),
                    'signature' => $data['Token'] ?? $headers['signature'] ?? null,
                    'event_type' => $data['event_type'] ?? $data['Status'] ?? null,
                    'webhook_status' => $data['Status'] ?? $data['status'] ?? null,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Webhook logging failed', [
                'gateway' => $gatewayCode,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Process webhook payment update.
     */
    private function processWebhookPaymentUpdate(WebhookResult $webhookResult): void
    {
        $paymentId = $webhookResult->paymentId;

        if (!$paymentId) {
            Log::warning('Webhook result missing payment ID', [
                'webhook_result' => $webhookResult->toArray()
            ]);
            return;
        }

        $payment = Payment::where('external_payment_id', $paymentId)->first();

        if (!$payment) {
            Log::warning('Payment not found for webhook', [
                'payment_id' => $paymentId,
                'webhook_result' => $webhookResult->toArray()
            ]);
            return;
        }

        Log::info('Processing webhook payment update', [
            'payment_id' => $paymentId,
            'payment_public_id' => $payment->public_id,
            'current_status' => $payment->status,
            'new_status' => $webhookResult->status,
        ]);

        // Update external_details with webhook data
        $externalDetails = $payment->external_details ?? [];

        // Merge additional data from webhook result
        if (!empty($webhookResult->additionalData)) {
            $externalDetails = array_merge($externalDetails, $webhookResult->additionalData);
        }

        $payment->update(['external_details' => $externalDetails]);

        // Update payment status based on webhook result
        if ($webhookResult->status && $webhookResult->status !== $payment->status) {
            $this->updatePaymentStatus($payment, $webhookResult->status);
        }
    }

    /**
     * Generate unique payment ID.
     */
    private function generatePaymentId(string $methodCode): string
    {
        $prefix = strtoupper(substr($methodCode, 0, 4));

        do {
            $id = "PAY-{$prefix}-" . strtoupper(Str::random(7));
        } while (Payment::where('public_id', $id)->exists());

        return $id;
    }
}
