<?php

namespace App\Services;

use App\Models\OrderItem;
use App\Models\SubscriptionPlan;
use App\Models\User;

class SubscriptionAnalyzerService
{
    /**
     * Detect the action to be taken when a user subscribes to a new plan.
     */
    public function detectSubscriptionAction(User $user, SubscriptionPlan $newPlan): string
    {
        $current = $user->currentSubscription;

        if (! $current) {
            return OrderItem::ACTION_NEW;
        }

        if ($current->plan_id === $newPlan->id) {
            return OrderItem::ACTION_RENEW;
        }

        if ($current->plan && $current->plan->price < $newPlan->price) {
            return OrderItem::ACTION_UPGRADE;
        }

        return OrderItem::ACTION_DOWNGRADE; // если цена ниже
    }
}
