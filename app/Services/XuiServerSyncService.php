<?php

namespace App\Services;

use App\Models\XuiServer;
use App\Models\User;
use App\Models\UserOnlineLog;
use App\Jobs\SyncXuiServerJob;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class XuiServerSyncService
{
    private int $defaultBatchSize;
    private int $onlineThresholdSeconds;
    private int $offlineThresholdSeconds;

    public function __construct()
    {
        $this->defaultBatchSize = config('xui_sync.default_batch_size', 10);
        $this->onlineThresholdSeconds = config('xui_sync.online_threshold_seconds', 20);
        $this->offlineThresholdSeconds = config('xui_sync.offline_threshold_seconds', 15);
    }

    /**
     * Synchronize servers with options for async processing and filtering.
     */
    public function syncServers(array $options = []): array
    {
        $async = $options['async'] ?? false;
        $batchSize = $options['batch_size'] ?? $this->defaultBatchSize;
        $serverIds = $options['server_ids'] ?? null;
        $force = $options['force'] ?? false;

        Log::info('Starting XUI server synchronization', [
            'async' => $async,
            'batch_size' => $batchSize,
            'server_ids' => $serverIds,
            'force' => $force,
        ]);

        // Get servers to sync
        $servers = $this->getServersToSync($serverIds, $force);

        if ($servers->isEmpty()) {
            Log::info('No servers found for synchronization');
            return ['synced' => 0, 'failed' => 0, 'skipped' => 0];
        }

        Log::info("Found {$servers->count()} servers for synchronization");

        if ($async) {
            return $this->syncServersAsync($servers, $batchSize);
        } else {
            return $this->syncServersSync($servers, $batchSize);
        }
    }

    /**
     * Get servers that should be synchronized.
     */
    private function getServersToSync(?array $serverIds, bool $force): Collection
    {
        $query = XuiServer::where('is_active', true);

        if ($serverIds) {
            $query->whereIn('id', $serverIds);
        }

        if (!$force) {
            $query->where('auto_sync', true);
        }

        return $query->get();
    }

    /**
     * Synchronize servers asynchronously using jobs.
     */
    private function syncServersAsync(Collection $servers, int $batchSize): array
    {
        $batches = $servers->chunk($batchSize);
        $totalJobs = 0;

        foreach ($batches as $batch) {
            foreach ($batch as $server) {
                SyncXuiServerJob::dispatch($server->id);
                $totalJobs++;
            }
        }

        Log::info("Dispatched {$totalJobs} sync jobs for servers");

        return [
            'synced' => 0,
            'failed' => 0,
            'skipped' => 0,
            'jobs_dispatched' => $totalJobs,
        ];
    }

    /**
     * Synchronize servers synchronously.
     */
    private function syncServersSync(Collection $servers, int $batchSize): array
    {
        $results = ['synced' => 0, 'failed' => 0, 'skipped' => 0];
        $batches = $servers->chunk($batchSize);

        foreach ($batches as $batchIndex => $batch) {
            Log::info("Processing batch " . ($batchIndex + 1) . " with {$batch->count()} servers");

            // Process batch concurrently (simulate with sequential for now)
            foreach ($batch as $server) {
                try {
                    $syncResult = $this->syncSingleServer($server);
                    if ($syncResult) {
                        $results['synced']++;
                    } else {
                        $results['skipped']++;
                    }
                } catch (\Exception $e) {
                    $results['failed']++;
                    Log::error("Failed to sync server {$server->id}: {$e->getMessage()}");
                }
            }
        }

        return $results;
    }

    /**
     * Synchronize a single server.
     */
    public function syncSingleServer(XuiServer $server): bool
    {
        Log::info("Syncing server: {$server->name} (ID: {$server->id})");

        try {
            DB::transaction(function () use ($server) {
                $now = Carbon::now();

                // 1. Sync raw data fields
                $this->syncRawServerData($server, $now);

                // 2. Update computed fields
                $this->updateComputedFields($server);

                // 3. Update user online status
                $this->updateUserOnlineStatus($server, $now);

                // 4. Update client traffic statistics
                $this->updateClientTrafficStats($server);

                // 5. Update last sync timestamp
                $server->update(['last_sync_at' => $now]);
            });

            Log::info("Successfully synced server {$server->id}");
            return true;

        } catch (\Exception $e) {
            Log::error("Error syncing server {$server->id}: {$e->getMessage()}", [
                'server_id' => $server->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return false;
        }
    }

    /**
     * Sync raw data fields from server.
     */
    private function syncRawServerData(XuiServer $server, Carbon $now): void
    {
        $serverInfoService = new ServerInfoService($server);

        // Server status
        try {
            $serverStatus = $serverInfoService->getStatus();
            $server->update([
                'raw_server_status' => $serverStatus,
                'raw_server_status_updated_at' => $now,
            ]);
        } catch (\Exception $e) {
            Log::warning("Failed to get server status for server {$server->id}: {$e->getMessage()}");
        }

        // Settings
        try {
            $settings = $serverInfoService->getAllSettings();
            $server->update([
                'raw_settings_all' => $settings,
                'raw_settings_all_updated_at' => $now,
            ]);
        } catch (\Exception $e) {
            Log::warning("Failed to get settings for server {$server->id}: {$e->getMessage()}");
        }

        // Inbounds list
        try {
            $inbounds = $serverInfoService->getInbounds();
            $server->update([
                'raw_inbounds_list' => $inbounds,
                'raw_inbounds_list_updated_at' => $now,
            ]);
        } catch (\Exception $e) {
            Log::warning("Failed to get inbounds for server {$server->id}: {$e->getMessage()}");
        }

        // Online clients
        try {
            $onlineClients = $serverInfoService->getOnlineClients();
            $server->update([
                'raw_clients_online' => $onlineClients,
                'raw_clients_online_updated_at' => $now,
            ]);
        } catch (\Exception $e) {
            Log::warning("Failed to get online clients for server {$server->id}: {$e->getMessage()}");
        }
    }

    /**
     * Update computed fields based on raw data.
     */
    private function updateComputedFields(XuiServer $server): void
    {
        $updates = [];

        // Calculate server load from raw_server_status
        if ($server->raw_server_status) {
            $serverStatus = is_string($server->raw_server_status)
                ? json_decode($server->raw_server_status, true)
                : $server->raw_server_status;

            if (isset($serverStatus['cpu']) && isset($serverStatus['mem'])) {
                // calculate server load by ServerInfoService::calculateServerLoadPercentage
                $updates['server_load'] = ServerInfoService::calculateServerLoadPercentage($serverStatus);
            }
        }

        // Calculate clients count from raw_inbounds_list
        if ($server->raw_inbounds_list) {
            $inbounds = is_string($server->raw_inbounds_list)
                ? json_decode($server->raw_inbounds_list, true)
                : $server->raw_inbounds_list;

            $totalClients = 0;
            if (is_array($inbounds)) {
                foreach ($inbounds as $inbound) {
                    $totalClients += count($inbound['clientStats'] ?? []);
                }
            }
            $updates['clients_count'] = $totalClients;
        }

        // Calculate online clients from raw_clients_online
        if ($server->raw_clients_online) {
            $onlineClients = is_string($server->raw_clients_online)
                ? json_decode($server->raw_clients_online, true)
                : $server->raw_clients_online;

            if (is_array($onlineClients)) {
                // Extract unique user email from client email (remove inbound suffix)
                $updates['clients_online_list'] = collect($onlineClients)
                                                        ->filter()
                                                        ->map(function ($clientEmail) {
                                                            // Extract user email from client email (remove inbound suffix)
                                                            return User::extractEmailFromClientEmail($clientEmail);
                                                        })
                                                        ->unique()
                                                        ->toArray();

                $updates['clients_online_count'] = count($updates['clients_online_list']);
            }
        }

        if (!empty($updates)) {
            $server->update($updates);
        }
    }

    /**
     * Update user online status based on server data.
     */
    private function updateUserOnlineStatus(XuiServer $server, Carbon $now): void
    {
        if (!$server->clients_online_list) {
            return;
        }

        // if raw_clients_online_updated_at is more than 10 seconds ago, skip
        if ($server->raw_clients_online_updated_at->diffInSeconds($now) > 60) {
            return;
        }

        $onlineClients = is_string($server->clients_online_list)
            ? json_decode($server->clients_online_list, true)
            : $server->clients_online_list;

        if (!is_array($onlineClients)) {
            return;
        }

        // Extract emails from online clients
        $onlineEmails = collect($onlineClients)
            ->filter()
            ->map(function ($clientEmail) {
                // Extract user email from client email (remove inbound suffix)
                return User::extractEmailFromClientEmail($clientEmail);
            })
            ->unique()
            ->toArray();

        if (empty($onlineEmails)) {
            return;
        }

        // Get users from this server's pools only
        $poolUserIds = $server->serverPools()
            ->where('server_pools.is_active', true)
            ->whereNull('server_pool_assignments.released_at')
            ->with(['users' => function ($query) {
                $query->whereNull('user_server_assignments.released_at');
            }])
            ->get()
            ->pluck('users')
            ->flatten()
            ->pluck('id')
            ->unique()
            ->toArray();

        if (empty($poolUserIds)) {
            return;
        }

        // Get online users from server pools
        $onlineUsers = User::whereIn('email', $onlineEmails)
            ->whereIn('id', $poolUserIds)
            ->get()
            ->keyBy('id');

        // === 1. Process ONLINE users ===
        $onlineUsers->each(function ($user) use ($now, $server) {
            $wasOfflineLongAgo = $user->last_online_at === null ||
                $user->last_online_at->lt($now->subSeconds($this->onlineThresholdSeconds));

            // Only update if user was offline for a while
            if ($wasOfflineLongAgo) {
                $user->updateQuietly(['last_online_at' => $now]);

                UserOnlineLog::create([
                    'user_id' => $user->id,
                    'xui_server_id' => $server->id,
                    'status' => UserOnlineLog::STATUS_ONLINE,
                    'status_timestamp' => $now,
                    'created_at' => $now,
                ]);
            }
        });

        $onlineUserIds = $onlineUsers->keys()->toArray();

        // === 2. Process OFFLINE users ===
        User::whereIn('id', $poolUserIds)
            ->whereNotIn('id', $onlineUserIds)
            ->whereNotNull('last_online_at')
            ->where('last_online_at', '<', $now->subSeconds($this->offlineThresholdSeconds))
            ->get()
            ->each(function ($user) use ($now, $server) {
                // Get last log for this user on this server
                $lastLog = UserOnlineLog::where('user_id', $user->id)
                    ->where('xui_server_id', $server->id)
                    ->latest('status_timestamp')
                    ->first();

                if (!$lastLog || $lastLog->status !== UserOnlineLog::STATUS_OFFLINE) {
                    UserOnlineLog::create([
                        'user_id' => $user->id,
                        'xui_server_id' => $server->id,
                        'status' => UserOnlineLog::STATUS_OFFLINE,
                        'status_timestamp' => $user->last_online_at->copy()->addSeconds(10),
                        'created_at' => $now,
                    ]);
                }
            });
    }

    /**
     * Update client traffic statistics.
     */
    private function updateClientTrafficStats(XuiServer $server): void
    {
        if (!$server->raw_inbounds_list) {
            return;
        }

        $inbounds = is_string($server->raw_inbounds_list)
            ? json_decode($server->raw_inbounds_list, true)
            : $server->raw_inbounds_list;

        if (!is_array($inbounds)) {
            return;
        }

        foreach ($inbounds as $inbound) {
            if (!isset($inbound['clientStats']) || !is_array($inbound['clientStats'])) {
                continue;
            }

            foreach ($inbound['clientStats'] as $clientStat) {
                $clientEmail = $clientStat['email'] ?? null;
                if (!$clientEmail) {
                    continue;
                }

                // Extract user email from client email
                $userEmail = $clientEmail;
                if (preg_match('/^(.+)_\d+$/', $clientEmail, $matches)) {
                    $userEmail = $matches[1];
                }

                // Find user and their current subscription
                $user = User::where('email', $userEmail)->first();
                if (!$user) {
                    continue;
                }

                $subscription = $user->currentSubscription;
                if (!$subscription) {
                    continue;
                }

                // Update traffic statistics
                $downBytes = $clientStat['down'] ?? 0;
                $upBytes = $clientStat['up'] ?? 0;
                $totalBytes = $downBytes + $upBytes;

                $subscription->update([
                    'traffic_used_bytes' => $totalBytes,
                    'traffic_up_bytes' => $upBytes,
                    'traffic_down_bytes' => $downBytes,
                ]);
            }
        }
    }

    /**
     * Detect and log offline users (for scheduled job).
     */
    public function detectOfflineUsers(): void
    {
        $now = Carbon::now();
        $offlineThreshold = $now->subSeconds($this->offlineThresholdSeconds);

        Log::info('Starting offline user detection');

        $offlineUsers = User::whereNotNull('last_online_at')
            ->where('last_online_at', '<', $offlineThreshold)
            ->whereHas('serverPools', function ($query) {
                $query->whereNull('user_server_assignments.released_at');
            })
            ->get();

        $processedCount = 0;

        foreach ($offlineUsers as $user) {
            // Get user's server pools
            $serverIds = $user->serverPools()
                ->whereNull('user_server_assignments.released_at')
                ->with('servers')
                ->get()
                ->pluck('servers')
                ->flatten()
                ->pluck('id')
                ->unique()
                ->toArray();

            foreach ($serverIds as $serverId) {
                // Check if we already have an offline log for this user on this server
                $lastLog = UserOnlineLog::where('user_id', $user->id)
                    ->where('xui_server_id', $serverId)
                    ->latest('status_timestamp')
                    ->first();

                if (!$lastLog || $lastLog->status !== UserOnlineLog::STATUS_OFFLINE) {
                    UserOnlineLog::create([
                        'user_id' => $user->id,
                        'xui_server_id' => $serverId,
                        'status' => UserOnlineLog::STATUS_OFFLINE,
                        'status_timestamp' => $user->last_online_at->copy()->addSeconds(10),
                        'created_at' => $now,
                    ]);
                    $processedCount++;
                }
            }
        }

        Log::info("Offline detection completed. Processed {$processedCount} offline logs for {$offlineUsers->count()} users");
    }
}
