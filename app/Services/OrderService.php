<?php

namespace App\Services;

use App\Events\Order\OrderPaid;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Payment;
use App\Models\User;
use App\Models\SubscriptionPlan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class OrderService
{
    // TODO: createOrderForPlanPurchase, createOrderForPlanUpgrade

    /**
     * Create a new order for VPN subscription.
     */
    public function createVpnSubscriptionOrder(User $user, SubscriptionPlan $plan, ?string $actionType = 'new', ?int $quantity = 1): Order
    {
        try {
            DB::beginTransaction();

            // Calculate total amount
            $itemPrice = $plan->price;
            $totalAmount = $itemPrice * $quantity;

            // Create order
            $order = Order::create([
                'user_id' => $user->id,
                'status' => 'pending',
                'total_amount' => $totalAmount,
                'currency' => 'RUB',
                'notes' => "VPN subscription: {$plan->name}",
            ]);

            // Create order item
            OrderItem::create([
                'order_id' => $order->id,
                'item_type' => 'subscription_plan',
                'item_id' => $plan->id,
                'action' => $actionType,
                'name' => $plan->name,
                'description' => $plan->description,
                'price' => $itemPrice,
                'quantity' => $quantity,
                'total_price' => $totalAmount,
            ]);

            Log::info('VPN order created', [
                'order_id' => $order->public_id,
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'amount' => $totalAmount,
            ]);

            DB::commit();

            return $order;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating VPN order', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Create a custom order.
     */
    public function createCustomOrder(User $user, array $items, array $options = []): Order
    {
        return DB::transaction(function () use ($user, $items, $options) {
            $totalAmount = 0;

            // Create order
            $order = Order::create([
                'user_id' => $user->id,
                'status' => 'pending',
                'total_amount' => 0, // Will be updated after items
                'currency' => $options['currency'] ?? 'RUB',
                'notes' => $options['notes'] ?? null,
            ]);

            // Create order items
            foreach ($items as $item) {
                $itemTotal = $item['price'] * $item['quantity'];
                $totalAmount += $itemTotal;

                OrderItem::create([
                    'order_id' => $order->id,
                    'item_type' => $item['item_type'] ?? 'custom',
                    'item_id' => $item['item_id'] ?? null,
                    'name' => $item['name'],
                    'description' => $item['description'] ?? null,
                    'price' => $item['price'],
                    'quantity' => $item['quantity'],
                    'total_price' => $itemTotal,
                ]);
            }

            // Update order total
            $order->update(['total_amount' => $totalAmount]);

            Log::info('Custom order created', [
                'order_id' => $order->public_id,
                'user_id' => $user->id,
                'items_count' => count($items),
                'amount' => $totalAmount,
            ]);

            return $order;
        });
    }

    /**
     * Update order status.
     */
    public function updateOrderStatus(Order $order, string $status, ?string $notes = null): bool
    {
        $oldStatus = $order->status;

        $updated = $order->update([
            'status' => $status,
            'notes' => $notes ? ($order->notes . "\n" . $notes) : $order->notes,
        ]);

        if ($updated) {
            Log::info('Order status updated', [
                'order_id' => $order->public_id,
                'old_status' => $oldStatus,
                'new_status' => $status,
                'notes' => $notes,
            ]);
        }

        return $updated;
    }

    /**
     * Mark order as paid.
     */
    public function markAsPaid(Order $order, ?Payment $payment = null): bool
    {
        if ($order->status === 'completed') {
            throw new \InvalidArgumentException('Order is already completed');
        }

        $oldStatus = $order->status;

        $updated = $order->update([
            'status' => 'paid',
            'paid_at' => now(),
            'notes' => $payment ? ($order->notes . "\n" . "Paid by {$payment->method->name} ({$payment->public_id})") : $order->notes,
        ]);

        if ($updated) {
            Log::info('Order status updated', [
                'order_id' => $order->public_id,
                'old_status' => $oldStatus,
                'new_status' => 'paid',
                'notes' => $payment ? "Paid by {$payment->method->name} ({$payment->public_id})" : null,
            ]);

            // Dispatch event to complete order
            OrderPaid::dispatch($order);
        }

        return $updated;
    }

    /**
     * Cancel order.
     */
    public function cancelOrder(Order $order, ?string $reason = null): bool
    {
        if ($order->status === 'completed') {
            throw new \InvalidArgumentException('Cannot cancel completed order');
        }

        $notes = $reason ? "Cancelled: {$reason}" : 'Cancelled';

        return $this->updateOrderStatus($order, 'cancelled', $notes);
    }

    /**
     * Complete order.
     */
    public function completeOrder(Order $order): bool
    {
        if ($order->status !== 'paid') {
            throw new \InvalidArgumentException('Order must be paid before completion');
        }

        return $this->updateOrderStatus($order, 'completed', 'Order completed successfully');
    }

    /**
     * Get order by public ID.
     */
    public function getOrderByPublicId(string $publicId): ?Order
    {
        return Order::where('public_id', $publicId)->first();
    }

    /**
     * Get user orders.
     */
    public function getUserOrders(User $user, int $limit = 20): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        return Order::where('user_id', $user->id)
            ->with(['items', 'payments'])
            ->orderBy('created_at', 'desc')
            ->paginate($limit);
    }

    /**
     * Get order statistics.
     */
    public function getOrderStatistics(?\DateTime $from = null, ?\DateTime $to = null): array
    {
        $query = Order::query();

        if ($from) {
            $query->where('created_at', '>=', $from);
        }

        if ($to) {
            $query->where('created_at', '<=', $to);
        }

        $stats = $query->selectRaw('
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = ? THEN 1 END) as pending_orders,
            COUNT(CASE WHEN status = ? THEN 1 END) as paid_orders,
            COUNT(CASE WHEN status = ? THEN 1 END) as completed_orders,
            COUNT(CASE WHEN status = ? THEN 1 END) as cancelled_orders,
            SUM(total_amount) as total_amount,
            SUM(CASE WHEN status IN (?, ?) THEN total_amount ELSE 0 END) as paid_amount
        ', ['pending', 'paid', 'completed', 'cancelled', 'paid', 'completed'])->first();

        return [
            'total_orders' => $stats->total_orders ?? 0,
            'pending_orders' => $stats->pending_orders ?? 0,
            'paid_orders' => $stats->paid_orders ?? 0,
            'completed_orders' => $stats->completed_orders ?? 0,
            'cancelled_orders' => $stats->cancelled_orders ?? 0,
            'total_amount' => $stats->total_amount ?? 0,
            'paid_amount' => $stats->paid_amount ?? 0,
        ];
    }

}
