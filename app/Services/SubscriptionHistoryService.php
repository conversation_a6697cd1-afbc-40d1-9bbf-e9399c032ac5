<?php

namespace App\Services;

use App\Models\Subscription;
use App\Models\SubscriptionHistory;

class SubscriptionHistoryService
{
    /**
     * Record a subscription creation in history.
     */
    public function recordCreation(Subscription $subscription, ?string $notes = null): SubscriptionHistory
    {
        return SubscriptionHistory::recordCreation($subscription, $notes);
    }

    /**
     * Record a subscription extension in history.
     */
    public function recordExtension(Subscription $subscription, int $deltaDuration, string $deltaDurationUnits, ?string $notes = null): SubscriptionHistory
    {
        return SubscriptionHistory::recordExtension($subscription, $deltaDuration, $deltaDurationUnits, $notes);
    }

    /**
     * Record a subscription upgrade in history.
     */
    public function recordUpgrade(Subscription $subscription, ?string $notes = null): SubscriptionHistory
    {
        return SubscriptionHistory::recordUpgrade($subscription, $notes);
    }

    /**
     * Record a subscription cancellation in history.
     */
    public function recordCancellation(Subscription $subscription, ?string $notes = null): SubscriptionHistory
    {
        return SubscriptionHistory::recordCancellation($subscription, $notes);
    }

    /**
     * Record a subscription expiration in history.
     */
    public function recordExpiration(Subscription $subscription, ?string $notes = null): SubscriptionHistory
    {
        return SubscriptionHistory::recordExpiration($subscription, $notes);
    }

    /**
     * Record a manual change in history.
     */
    public function recordManualChange(Subscription $subscription, ?string $notes = null, ?string $adminNotes = null): SubscriptionHistory
    {
        return SubscriptionHistory::recordManualChange($subscription, $notes, $adminNotes);
    }
}
