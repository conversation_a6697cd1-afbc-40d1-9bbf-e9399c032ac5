<?php

namespace App\Services;

use alirezax5\XuiApi\Panel\MHSanaei;
use App\Models\XuiServer;
use Exception;

class XuiManagementService extends MHSanaei
{
    protected $server;

    /**
     * Create a new XuiManagementService instance.
     */
    public function __construct(?XuiServer $server = null)
    {
        if (! $server) {
            throw new Exception('XuiServer is not provided');
        }

        $this->server = $server;

        parent::__construct(
            $server->getFullUrlAttribute(),
            $server->username,
            $server->password
        );

        $cookieDirectory = storage_path("app/xui/{$server->id}");
        if (! is_dir($cookieDirectory)) {
            mkdir($cookieDirectory, 0777, true);
        }

        $this->setCookie("{$cookieDirectory}/cookie.txt");

        $this->login(); // или отложенный логин
    }

    /**
     * Call a method with automatic login retry on 401.
     */
    protected function safeCall(string $method, ...$args)
    {
        try {
            return call_user_func_array([$this, $method], $args);
        } catch (Exception $e) {
            if (str_contains($e->getMessage(), '401')) {
                $this->login(true); // форс логин
                return call_user_func_array([$this, $method], $args);
            }

            throw $e;
        }
    }

    // Можно также использовать __call() для перехвата всех методов ↓↓↓
    public function __call($method, $args)
    {
        return $this->safeCall($method, ...$args);
    }

    public function login($force = false)
    {
        // Если логин реально нужен
        $needsLogin = $force || ! $this->checkCookieFile();

        if (! $needsLogin) {
            return;
        }

        $this->resetCookieFile();

        try {
            $response = $this->curl('login', [
                'username' => $this->username,
                'password' => $this->password,
            ]);
        } catch (\Throwable $e) {
            throw new \Exception('Ошибка при выполнении curl-запроса на логин: ' . $e->getMessage());
        }

        // Проверка формата
        if (!is_array($response) || !array_key_exists('success', $response)) {
            throw new \Exception('Некорректный ответ от сервера логина.');
        }

        // Если логин неудачен
        if (!$response['success']) {
            $msg = $response['msg'] ?? 'Логин не удался по неизвестной причине.';
            throw new \Exception('Ошибка логина: ' . $msg);
        }

        // Успешный логин
        if ($this->server) {
            $cookiePath = $this->getCookie();
            $this->server->update([
                'last_login_at' => now(),
                'session_cookie' => file_exists($cookiePath) ? file_get_contents($cookiePath) : null,
            ]);
        }
    }

}
