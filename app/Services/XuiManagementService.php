<?php

namespace App\Services;

use alirezax5\XuiApi\Panel\MHSanaei;
use App\Models\XuiServer;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class XuiManagementService extends MHSanaei
{
    protected $server;

    /**
     * Create a new XuiManagementService instance.
     */
    public function __construct(?XuiServer $server = null)
    {
        if (! $server) {
            throw new Exception('XuiServer is not provided');
        }

        $this->server = $server;

        // Переопределить конструктор родительского класса
        // Чтобы можно было переопределить его в дочернем классе
        parent::__construct(
            $server->getFullUrlAttribute(),
            $server->username,
            $server->password
        );

        // Создать папку для куков, если нет
        $cookieDirectory = storage_path("app/xui/{$server->id}");
        if (! is_dir($cookieDirectory)) {
            mkdir($cookieDirectory, 0777, true);
        }

        $this->setCookie("{$cookieDirectory}/cookie.txt");

        $this->login(); // или отложенный логин
    }

    public function getServer(): XuiServer
    {
        return $this->server;
    }

    /**
     * Вызов метода родительского класса с автоматической попыткой логина при 401, 404
     */
    protected function safeCall(string $method, ...$args)
    {
        try {
            return $this->callParentMethod($method, $args);
        } catch (Exception $e) {
            Log::alert($e->getMessage());

            if (Str::contains($e->getMessage(), '401') || Str::contains($e->getMessage(), '404')) {
                if (Str::contains($e->getMessage(), '404')) {
                    // Если 404, то куки не актуальны, нужно сбросить
                    $this->resetCookieFile();
                }

                $this->login(true); // форс логин
                return $this->callParentMethod($method, $args);
            }

            throw $e;
        }
    }

    /**
     * Вызов метода родительского класса с помощью Reflection
     * @link https://php.net/manual/en/class.reflectionmethod.php
     */
    protected function callParentMethod(string $method, array $args)
    {
        $parentClass = get_parent_class($this);

        if (!method_exists($parentClass, $method)) {
            throw new \BadMethodCallException("Метод {$method} не найден в родителе {$parentClass}");
        }

        $reflection = new \ReflectionMethod($parentClass, $method);
        return $reflection->invokeArgs($this, $args);
    }

    // Можно также использовать __call() для перехвата всех методов ↓↓↓
    public function __call($method, $args)
    {
        return $this->safeCall($method, ...$args);
    }

    /**
     * Логин на сервер через родительские методы с перехватом исключений
     *
     * @param bool $force - форсировать логин, даже если куки есть
     *
     * @throws \Exception
     */
    public function login($force = false)
    {
        // Если логин реально нужен
        $needsLogin = $force || ! $this->checkCookieFile();

        if (! $needsLogin) {
            return;
        }

        $this->resetCookieFile();

        try {
            $response = $this->curl('login', [
                'username' => $this->username,
                'password' => $this->password,
            ]);
        } catch (\Throwable $e) {
            throw new \Exception('Ошибка при выполнении curl-запроса на логин: ' . $e->getMessage());
        }

        // Проверка формата
        if (!is_array($response) || !array_key_exists('success', $response)) {
            throw new \Exception('Некорректный ответ от сервера логина.');
        }

        // Если логин неудачен
        if (!$response['success']) {
            // Если есть сообщение об ошибке, то использовать его
            $msg = $response['msg'] ?? 'Логин не удался по неизвестной причине.';
            throw new \Exception('Ошибка логина: ' . $msg);
        }

        // Успешный логин
        if ($this->server) {
            $cookiePath = $this->getCookie();
            $this->server->update([
                'last_login_at' => now(),
                'session_cookie' => file_exists($cookiePath) ? file_get_contents($cookiePath) : null,
            ]);
        }
    }

    /* --------------- Переопределить методы MHSanaei class --------------- */

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::updateSetting()
     */
    public function updateSetting(...$args)
    {
        return $this->safeCall('updateSetting', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::addInbound()
     */
    public function addInbound(...$args)
    {
        return $this->safeCall('addInbound', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::addNewClient()
     */
    public function addNewClient(...$args)
    {
        return $this->safeCall('addNewClient', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::addClient()
     */
    public function addClient(...$args)
    {
        return $this->safeCall('addClient', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::editClient()
     */
    public function editClient(...$args)
    {
        return $this->safeCall('editClient', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::updateClient()
     */
    public function updateClient(...$args)
    {
        return $this->safeCall('updateClient', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::editClientByEmail()
     */
    public function editClientByEmail(...$args)
    {
        return $this->safeCall('editClientByEmail', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::editInbound()
     */
    public function editInbound(...$args)
    {
        return $this->safeCall('editInbound', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::enableClient()
     */
    public function enableClient(...$args)
    {
        return $this->safeCall('enableClient', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::enableClientByEmail()
     */
    public function enableClientByEmail(...$args)
    {
        return $this->safeCall('enableClientByEmail', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::getClientIP()
     */
    public function getClientIP(...$args)
    {
        return $this->safeCall('getClientIP', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::clearClientIP()
     */
    public function clearClientIP(...$args)
    {
        return $this->safeCall('clearClientIP', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::getClientData()
     */
    public function getClientData(...$args)
    {
        return $this->safeCall('getClientData', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::getClientDataByEmail()
     */
    public function getClientDataByEmail(...$args)
    {
        return $this->safeCall('getClientDataByEmail', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::disableClientByEmail()
     */
    public function disableClientByEmail(...$args)
    {
        return $this->safeCall('disableClientByEmail', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::disableClient()
     */
    public function disableClient(...$args)
    {
        return $this->safeCall('disableClient', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::editClientTraffic()
     */
    public function editClientTraffic(...$args)
    {
        return $this->safeCall('editClientTraffic', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::editClientTrafficByEmail()
     */
    public function editClientTrafficByEmail(...$args)
    {
        return $this->safeCall('editClientTrafficByEmail', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::resetClientTraffic()
     */
    public function resetClientTraffic(...$args)
    {
        return $this->safeCall('resetClientTraffic', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::resetClientTrafficByUuid()
     */
    public function resetClientTrafficByUuid(...$args)
    {
        return $this->safeCall('resetClientTrafficByUuid', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::removeClientByEmail()
     */
    public function removeClientByEmail(...$args)
    {
        return $this->safeCall('removeClientByEmail', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::delClient()
     */
    public function delClient(...$args)
    {
        return $this->safeCall('delClient', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::logs()
     */
    public function logs(...$args)
    {
        return $this->safeCall('logs', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::getListsApi()
     */
    public function getListsApi(...$args)
    {
        return $this->safeCall('getListsApi', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::getApi()
     */
    public function getApi(...$args)
    {
        return $this->safeCall('getApi', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::resetAllClientTrafficsApi()
     */
    public function resetAllClientTrafficsApi(...$args)
    {
        return $this->safeCall('resetAllClientTrafficsApi', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::delDepletedClientsApi()
     */
    public function delDepletedClientsApi(...$args)
    {
        return $this->safeCall('delDepletedClientsApi', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::delAllDepletedClientsApi()
     */
    public function delAllDepletedClientsApi(...$args)
    {
        return $this->safeCall('delAllDepletedClientsApi', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::getDb()
     */
    public function getDb(...$args)
    {
        return $this->safeCall('getDb', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::getConfigJson()
     */
    public function getConfigJson(...$args)
    {
        return $this->safeCall('getConfigJson', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::getClientTraffics()
     */
    public function getClientTraffics(...$args)
    {
        return $this->safeCall('getClientTraffics', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::getClientTrafficsById()
     */
    public function getClientTrafficsById(...$args)
    {
        return $this->safeCall('getClientTrafficsById', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::getNewX25519Cert()
     */
    public function getNewX25519Cert(...$args)
    {
        return $this->safeCall('getNewX25519Cert', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::removeClient()
     */
    public function removeClient(...$args)
    {
        return $this->safeCall('removeClient', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::showOnlines()
     */
    public function showOnlines(...$args)
    {
        return $this->safeCall('showOnlines', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\MHSanaei::createbackup()
     */
    public function createbackup(...$args)
    {
        return $this->safeCall('createbackup', ...$args);
    }


    /* ----------------------- Переопределить методы Base class ----------------------- */

    /**
     * @see \alirezax5\XuiApi\Panel\Base::status()
     */
    public function status(...$args)
    {
        return $this->safeCall('status', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\Base::getXrayVersion()
     */
    public function getXrayVersion(...$args)
    {
        return $this->safeCall('getXrayVersion', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\Base::restartPanel()
     */
    public function restartPanel(...$args)
    {
        return $this->safeCall('restartPanel', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\Base::installXray()
     */
    public function installXray(...$args)
    {
        return $this->safeCall('installXray', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\Base::restartXrayService()
     */
    public function restartXrayService(...$args)
    {
        return $this->safeCall('restartXrayService', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\Base::stopXrayService()
     */
    public function stopXrayService(...$args)
    {
        return $this->safeCall('stopXrayService', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\Base::startXrayService()
     */
    public function startXrayService(...$args)
    {
        return $this->safeCall('startXrayService', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\Base::listInbound()
     */
    public function listInbound(...$args)
    {
        return $this->safeCall('listInbound', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\Base::allSetting()
     */
    public function allSetting(...$args)
    {
        return $this->safeCall('allSetting', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\Base::updateSetting()
     */
    public function updateUser(...$args)
    {
        return $this->safeCall('updateUser', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\Base::editInboundDataWithKey()
     */
    public function editInboundDataWithKey(...$args)
    {
        return $this->safeCall('editInboundDataWithKey', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\Base::removeInbound()
     */
    public function removeInbound(...$args)
    {
        return $this->safeCall('removeInbound', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\Base::removeClient()
     */
    public function editClientWithKey(...$args)
    {
        return $this->safeCall('editClientWithKey', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Panel\Base::editClientByEmailWithKey()
     */
    public function editClientByEmailWithKey(...$args)
    {
        return $this->safeCall('editClientByEmailWithKey', ...$args);
    }


    /* -------------------- Дополнительные методы из alirezax5\XuiApi\Traits\Additions -------------------- */

    /**
     * @see \alirezax5\XuiApi\Traits\Additions::list()
     */
    public function list(...$args)
    {
        return $this->safeCall('list', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Traits\Additions::checkExistsUuidOnClients()
     */
    public function checkExistsUuidOnClients(...$args)
    {
        return $this->safeCall('checkExistsUuidOnClients', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Traits\Additions::getClientIndex()
     */
    public function getClientIndex(...$args)
    {
        return $this->safeCall('getClientIndex', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Traits\Additions::getClientIndexByEmail()
     */
    public function getClientIndexByEmail(...$args)
    {
        return $this->safeCall('getClientIndexByEmail', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Traits\Additions::editClientExpiryTime()
     */
    public function editClientExpiryTime(...$args)
    {
        return $this->safeCall('editClientExpiryTime', ...$args);
    }

    /**
     * @see \alirezax5\XuiApi\Traits\Additions::editClientExpiryTimeByEmail()
     */
    public function editClientExpiryTimeByEmail(...$args)
    {
        return $this->safeCall('editClientExpiryTimeByEmail', ...$args);
    }
}
