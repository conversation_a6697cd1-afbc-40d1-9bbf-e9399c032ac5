<?php

namespace App\Services;

use App\Models\XuiServer;
use Illuminate\Support\Facades\Log;

class ServerInfoService
{
    protected XuiManagementService $xuiManagementService;

    /**
     * Create a new ServerInfoService instance.
     */
    public function __construct(protected XuiServer $xuiServer)
    {
        $this->xuiManagementService = new XuiManagementService($xuiServer);
    }

    /**
     * Возвращает статус сервера
     */
    public function getStatus(): ?array
    {
        try {
            $result = $this->xuiManagementService->status();
            // dd($result);
            if ($result['success']) {
                return $result['obj'];
            }
        } catch (\Throwable $e) {
            Log::error('Error getting server status: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Возвращает текущую нагрузку на сервер в процентах (1.00–100.00)
     */
    public function getLoadPercentage(): float
    {
        $serverStatus = $this->getStatus();
        return self::calculateServerLoadPercentage($serverStatus);
    }


    /**
     * Получить все подключения (inbounds): содержит информацию о подключении, статистику клиентов и настройки клиентов
     */
    public function getInbounds(): ?array
    {
        try {
            $result = $this->xuiManagementService->listInbound();
            if ($result['success']) {
                return $result['obj'];
            }
        } catch (\Throwable $e) {
            Log::error('Error getting inbounds: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Возвращает список email'ов пользователей, которые в данный момент онлайн на сервере.
     */
    public function getOnlineClients(): ?array
    {
        try {
            $result = $this->xuiManagementService->showOnlines();
            if ($result['success']) {
                return $result['obj'] ?? [];
            }
        } catch (\Throwable $e) {
            dd($e);
            Log::error('Error getting online clients: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Возвращает все настройки сервера
     */
    public function getAllSettings(): ?array
    {
        try {
            $result = $this->xuiManagementService->allSetting();
            if ($result['success']) {
                return $result['obj'];
            }
        } catch (\Throwable $e) {
            Log::error('Error getting server settings: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Возвращает краткие настройки сервера
     */
    public function getShortSettings(): ?array
    {
        try {
            $result = $this->xuiManagementService->defaultSettings();
            if ($result['success']) {
                return $result['obj'];
            }
        } catch (\Throwable $e) {
            Log::error('Error getting server short settings: ' . $e->getMessage());
        }

        return null;
    }

    /* ------ Helper functions ------ */

    /**
     * Подсчитывает текущую нагрузку на сервер в процентах (1.00–100.00)
     *
     * Алгоритм учитывает:
     * - CPU нагрузку (в виде доли, умноженной на 100);
     * - Использование RAM;
     * - Load average (на интервале 1 минуты), нормализованный по количеству логических ядер.
     *
     * Итоговая формула: CPU * 0.5 + RAM * 0.3 + LoadAvg * 0.2
     *
     * @param array $serverStatus — декодированный JSON статус от x-ui
     * @return float
     */
    public static function calculateServerLoadPercentage(array $serverStatus): float
    {
        if (!isset($serverStatus)) {
            return 1.00; // Если данных нет — минимальная нагрузка
        }

        // CPU как дробь от 1.0 (например, 0.0856 = 8.56%)
        $cpuRaw = $serverStatus['cpu'] ?? 0;
        $cpuPercent = min(100, max(0, $cpuRaw));

        // RAM
        $memCurrent = $serverStatus['mem']['current'] ?? 0;
        $memTotal = $serverStatus['mem']['total'] ?? 1; // избегаем деления на 0
        $memPercent = min(100, max(0, ($memCurrent / $memTotal) * 100));

        // Load average
        $loadAvg = $serverStatus['loads'][0] ?? 0;
        $logicalPro = $serverStatus['logicalPro'] ?? 1;
        $loadPercent = min(100, max(0, ($loadAvg / $logicalPro) * 100));

        // Взвешенное среднее: CPU 50%, RAM 30%, LoadAvg 20%
        $finalLoad = ($cpuPercent * 0.5) + ($memPercent * 0.3) + ($loadPercent * 0.2);

        // Ограничение и округление
        return max(1.00, min(100.00, round($finalLoad, 2)));
    }

    /**
     * Преобразует целое число в строку с его представлением в виде верхнего индекса (superscript),
     * обёрнутого в скобки верхнего индекса.
     *
     * Например: 47 → ⁽⁴⁷⁾
     *
     * @param int $number Целое число, которое нужно преобразовать.
     * @return string Строка с символами верхнего индекса, обёрнутая в ⁽ и ⁾.
     */
    public function toSuperscriptNumber(int $number): string
    {
        // Таблица верхних индексов
        $superscriptDigits = [
            '0' => '⁰',
            '1' => '¹',
            '2' => '²',
            '3' => '³',
            '4' => '⁴',
            '5' => '⁵',
            '6' => '⁶',
            '7' => '⁷',
            '8' => '⁸',
            '9' => '⁹',
        ];

        // Преобразуем цифры в верхний индекс
        $digits = str_split((string)$number);
        $superscript = '';
        foreach ($digits as $digit) {
            $superscript .= $superscriptDigits[$digit] ?? '';
        }

        // Оборачиваем в скобки верхнего индекса
        return '⁽' . $superscript . '⁾';
    }

    /**
     * Преобразует процент загрузки в символ Брайля, отображающий уровень нагрузки от 0% до 100%.
     *
     * Используется 9 символов Юникода в диапазоне U+2800–U+28FF, отражающих степень "заполненности" точки:
     * от пустого символа (⠀) до полностью заполненного (⣿).
     *
     * Шкала построена в 8 градаций по ~12.5%:
     * 0% = ⠀, 12.5% = ⠁, 25% = ⠃, ..., 100% = ⣿
     *
     * @param float $percent Уровень загрузки в процентах (0.0–100.0)
     * @return string Символ Брайля, соответствующий уровню загрузки
     */
    public function toBrailleLoadChar(float $percent): string {
        // Символы Брайля от 0 до 8 точек заполнения
        $levels = [
            0x2801, // 0%   - ⠁
            0x2803, // 12.5% - ⠃
            0x2807, // 25% - ⠇
            0x280F, // 37.5% - ⠏
            0x281F, // 50% - ⠟
            0x283F, // 62.5% - ⠿
            0x287F, // 75% - ⡿
            0x28FF  // 100% - ⣿
        ];

        // Определяем индекс символа в зависимости от процентов
        $index = (int) floor($percent / 100 * 8);
        $index = max(0, min(7, $index)); // Безопасный диапазон [0,8]

        // Конвертируем Unicode в символ
        return mb_convert_encoding('&#' . $levels[$index] . ';', 'UTF-8', 'HTML-ENTITIES');
    }
}
