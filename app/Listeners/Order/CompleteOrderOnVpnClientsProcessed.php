<?php

namespace App\Listeners\Order;

use App\Events\Xui\VpnClientsForSubscriptionProcessed;
use App\Services\OrderService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CompleteOrderOnVpnClientsProcessed
{
    /**
     * Create the event listener.
     */
    public function __construct(
        protected OrderService $orderService,
    )
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(VpnClientsForSubscriptionProcessed $event): void
    {
        try {
            $this->orderService->completeOrder($event->subscription->order);
        } catch (\Exception $e) {
            // Log the error
            report($e);
        }
    }
}
