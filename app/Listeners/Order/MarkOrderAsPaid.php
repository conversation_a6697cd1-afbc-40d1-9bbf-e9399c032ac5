<?php

namespace App\Listeners\Order;

use App\Events\Payment\PaymentReceived;
use App\Services\OrderService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class MarkOrderAsPaid
{
    /**
     * Create the event listener.
     */
    public function __construct(
        protected OrderService $orderService,
    )
    {
        //
    }

    /**
     * Handle the event.
     * Этот слушатель для события PaymentReceived, чтобы после оплаты поменять статус заказа на paid.
     */
    public function handle(PaymentReceived $event): void
    {
        $payment = $event->payment;
        $this->orderService->markAsPaid($payment->order, $payment);
    }
}
