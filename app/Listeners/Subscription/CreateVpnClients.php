<?php

namespace App\Listeners\Subscription;

use App\Events\Subscription\SubscriptionCreated;
use App\Services\VpnClientManager;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class CreateVpnClients
{
    /**
     * Create the event listener.
     */
    public function __construct(
        protected VpnClientManager $vpnClientManager,
    )
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(SubscriptionCreated $event): void
    {
        Log::info('Listener CreateVpnClients called for', [
            'subscription' => $event->subscription->id,
            'user' => $event->subscription->user->id,
        ]);

        // create vpn clients for the user
        $this->vpnClientManager->createVpnClientsOnPools($event->subscription->user);
    }
}
