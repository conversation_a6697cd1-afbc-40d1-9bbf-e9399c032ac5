<?php

namespace App\Listeners\Subscription;

use App\Exceptions\SubscriptionPlan\NoSubscriptionPlanItemsFoundInOrder;
use App\Events\Order\OrderPaid;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use App\Services\OrderService;
use App\Services\SubscriptionService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class ProcessSubscriptionOnOrderPaid
{
    /**
     * Create the event listener.
     */
    public function __construct(
        protected SubscriptionService $subscriptionService,
        protected OrderService $orderService,
    )
    {
        // Log execution
        Log::info('Listener ProcessSubscriptionOnOrderPaid created');
    }

    /**
     * Handle the event.
     */
    public function handle(OrderPaid $event): void
    {
        // get order items
        $orderItems = $event->order->items;
        $user = $event->order->user;

        // get subscription plan items
        $subscriptionPlanItems = $orderItems->filter(function ($item) {
            return $item->item_type === 'subscription_plan';
        });

        try {
            $this->processSubscriptionPlanItem($subscriptionPlanItems, $user, $event->order);
        } catch (\Exception $e) {
            // Log the error
            report($e);
        }
    }

    protected function processSubscriptionPlanItem($subscriptionPlanItems, User $user, Order $order): void
    {
        if (empty($subscriptionPlanItems)) {
            // use special exception for No subscription plan items found in order
            throw new NoSubscriptionPlanItemsFoundInOrder('No subscription plan items found in order #' . $order->id);
        }

        // create subscriptions
        foreach ($subscriptionPlanItems as $item) {
            // check action type and do suitable action
            switch ($item->action) {
                case OrderItem::ACTION_NEW:
                    $subscription = $this->subscriptionService->createSubscription($user, $item->subscriptionPlan, now());
                    break;
                case OrderItem::ACTION_RENEW:
                    $subscription = $this->subscriptionService->renewSubscription($user->currentSubscription);
                    break;
                case OrderItem::ACTION_EXTEND:
                    $subscription = $this->subscriptionService->extendSubscription(
                        $user->currentSubscription,
                        $item->subscriptionPlan->duration,
                        $item->subscriptionPlan->duration_unit
                    );
                    break;
                case OrderItem::ACTION_UPGRADE:
                    $subscription = $this->subscriptionService->upgradeSubscription($user->currentSubscription, $item->subscriptionPlan);
                    break;
                case OrderItem::ACTION_DOWNGRADE:
                    $subscription = $this->subscriptionService->upgradeSubscription($user->currentSubscription, $item->subscriptionPlan);
                default:
                    Log::warning('Unknown subscription action type: ' . $item->action);
                    continue 2;
            }
        }
    }
}

