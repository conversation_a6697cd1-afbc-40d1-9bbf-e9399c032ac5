<?php

namespace App\Listeners\Subscription;

use App\Events\Order\OrderPaid;
use App\Services\OrderService;
use App\Services\SubscriptionService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class CreateSubscription
{
    /**
     * Create the event listener.
     */
    public function __construct(
        protected SubscriptionService $subscriptionService,
        protected OrderService $orderService,
    )
    {
        // Log execution
        Log::info('Listener CreateSubscription created');
    }

    /**
     * Handle the event.
     */
    public function handle(OrderPaid $event): void
    {
        // get order items
        $orderItems = $event->order->items;

        // get subscription plan items
        $subscriptionPlanItems = $orderItems->filter(function ($item) {
            return $item->item_type === 'subscription_plan';
        });

        // create subscriptions
        foreach ($subscriptionPlanItems as $item) {
            $newSubscription = $this->subscriptionService->createSubscription($event->order->user, $item->subscriptionPlan, now());

            if ($newSubscription) {
                // complete order
                $this->orderService->completeOrder($event->order);
            }
        }
    }
}
