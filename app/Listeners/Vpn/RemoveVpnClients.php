<?php

namespace App\Listeners\Vpn;

use App\Events\Subscription\SubscriptionCancelled;
use App\Events\Subscription\SubscriptionExpired;
use App\Services\VpnClientManager;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class RemoveVpnClients
{
    /**
     * Create the event listener.
     */
    public function __construct(
        protected VpnClientManager $vpnClientManager,
    )
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(SubscriptionExpired | SubscriptionCancelled $event): void
    {
        Log::info('Listener RemoveVpnClients called for', [
            'subscription' => $event->subscription->id,
            'user' => $event->subscription->user->id,
        ]);

        // remove vpn clients for the user
        $this->vpnClientManager->removeVpnClientsOnPools($event->subscription->user);
    }
}
