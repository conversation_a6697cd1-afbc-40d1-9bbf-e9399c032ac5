<?php

namespace App\Listeners\Vpn;

use App\Events\Subscription\SubscriptionExtended;
use App\Events\Subscription\SubscriptionManuallyChanged;
use App\Events\Subscription\SubscriptionUpgraded;
use App\Services\VpnClientManager;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class UpdateVpnClients
{
    /**
     * Create the event listener.
     */
    public function __construct(
        protected VpnClientManager $vpnClientManager,
    )
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(SubscriptionExtended | SubscriptionUpgraded | SubscriptionManuallyChanged $event): void
    {
        Log::info('Listener UpdateVpnClients called for', [
            'subscription' => $event->subscription->id,
            'user' => $event->subscription->user->id,
        ]);

        // update vpn clients for the user
        $this->vpnClientManager->recreateVpnClientsOnPools($event->subscription->user);
    }
}
