<?php

namespace App\Http\Controllers;

use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class WebhookController extends Controller
{
    public function __construct(
        private PaymentService $paymentService
    ) {}

    /**
     * Handle T-Bank webhook.
     */
    public function tbank(Request $request): JsonResponse
    {
        return $this->handleWebhook($request, 'tbank');
    }

    /**
     * Handle generic webhook.
     */
    public function handle(Request $request, string $gateway): JsonResponse
    {
        return $this->handleWebhook($request, $gateway);
    }

    /**
     * Process webhook for any gateway.
     */
    private function handleWebhook(Request $request, string $gateway): JsonResponse | Response
    {
        try {
            Log::info("Webhook received for {$gateway}", [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'content_type' => $request->header('Content-Type'),
                'payload_size' => strlen($request->getContent()),
            ]);

            $data = $request->all();
            $headers = $request->headers->all();

            // Process webhook
            $processed = $this->paymentService->handleWebhook($gateway, $data, $headers);

            if ($processed) {
                Log::info("Webhook processed successfully for {$gateway}", [
                    'payment_id' => $data['PaymentId'] ?? $data['payment_id'] ?? null,
                ]);

                if ($gateway === 'tbank') {
                    /*
                        В случае успешной обработки уведомления мерчанту нужно вернуть ответ HTTP CODE = 200 с телом сообщения OK — без тегов, заглавными английскими буквами.
                        Если ответ OK не получен, уведомление считается неуспешным, и сервис будет повторно отправлять это уведомление раз в час в течение 24 часов. Если за это время уведомление не доставлено, оно будет перемещено в архив.
                        См. https://www.tbank.ru/kassa/dev/payments/#tag/Uvedomleniya-ob-operaciyah/Otvet-na-HTTP(s)-uvedomlenie
                    */
                    return response('OK', 200);
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Webhook processed successfully',
                ]);
            } else {
                Log::warning("Webhook processing failed for {$gateway}", [
                    'data' => $data,
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Webhook processing failed',
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error("Webhook processing error for {$gateway}", [
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
            ], 500);
        }
    }
}
