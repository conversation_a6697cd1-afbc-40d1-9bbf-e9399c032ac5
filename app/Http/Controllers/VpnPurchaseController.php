<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Order;
use App\Models\SubscriptionPlan;
use App\Models\PaymentMethod;
use App\Services\Payment\PaymentService;
use App\Services\Payment\DTOs\PaymentRequest;
use App\Services\Payment\Exceptions\PaymentException;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class VpnPurchaseController extends Controller
{
    public function __construct(
        private PaymentService $paymentService
    ) {}

    /**
     * Process plan selection and payment method choice, create an order, and generate payment.
     */
    public function purchase(Request $request, string $uuid): RedirectResponse|JsonResponse
    {
        try {
            // Validate the UUID and find the user
            $user = $this->validateAndGetUser($uuid);

            // Validate the request input
            $validatedData = $this->validatePurchaseRequest($request);

            // Get the selected plan and payment method
            $plan = $this->getValidatedPlan($validatedData['plan_id'], $user);
            $paymentMethod = $this->getValidatedPaymentMethod($validatedData['payment_method']);

            // Analyze current subscription and calculate pricing
            $pricingData = $this->analyzePricingForUser($user, $plan);

            // Create the order
            $order = $this->createOrder($user, $plan, $pricingData);

            // Create payment request
            $paymentRequest = $this->createPaymentRequest($order, $user, $pricingData, $uuid);

            // Process payment through PaymentService
            $paymentResponse = $this->paymentService->createPayment($paymentRequest, $paymentMethod->code);

            // Handle payment response
            return $this->handlePaymentResponse($paymentResponse, $order, $request);

        } catch (ValidationException $e) {
            return $this->handleValidationError($e, $request);
        } catch (PaymentException $e) {
            return $this->handlePaymentError($e, $request);
        } catch (\Exception $e) {
            return $this->handleGeneralError($e, $request);
        }
    }

    /**
     * Validate UUID and get the user.
     */
    private function validateAndGetUser(string $uuid): User
    {
        if (!preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $uuid)) {
            throw ValidationException::withMessages(['uuid' => 'Invalid user UUID format.']);
        }

        $user = User::where('id', $uuid)->first();

        if (!$user) {
            throw ValidationException::withMessages(['uuid' => 'User not found.']);
        }

        if (!$user->is_active) {
            throw ValidationException::withMessages(['user' => 'User account is not active.']);
        }

        return $user;
    }

    /**
     * Validate the purchase request input.
     */
    private function validatePurchaseRequest(Request $request): array
    {
        $validator = Validator::make($request->all(), [
            'plan_id' => 'required|integer|exists:subscription_plans,id',
            'payment_method' => 'required|string|exists:payment_methods,code',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    /**
     * Get and validate the selected subscription plan.
     */
    private function getValidatedPlan(int $planId, User $user): SubscriptionPlan
    {
        $plan = SubscriptionPlan::find($planId);

        if (!$plan) {
            throw ValidationException::withMessages(['plan_id' => 'Selected plan not found.']);
        }

        // Check if plan is accessible to the user
        $currentSubscription = $user->currentSubscription;
        $isCurrentPlan = $currentSubscription && $currentSubscription->plan_id === $plan->id;

        // Allow access to current plan even if it's not public/active (for renewals)
        if (!$isCurrentPlan && (!$plan->is_public || !$plan->is_active || $plan->is_archived)) {
            throw ValidationException::withMessages(['plan_id' => 'Selected plan is not available for purchase.']);
        }

        return $plan;
    }

    /**
     * Get and validate the selected payment method.
     */
    private function getValidatedPaymentMethod(string $paymentMethodCode): PaymentMethod
    {
        $paymentMethod = PaymentMethod::where('code', $paymentMethodCode)
            ->where('is_active', true)
            ->first();

        if (!$paymentMethod) {
            throw ValidationException::withMessages(['payment_method' => 'Selected payment method is not available.']);
        }

        return $paymentMethod;
    }

    /**
     * Analyze pricing based on user's current subscription.
     */
    private function analyzePricingForUser(User $user, SubscriptionPlan $newPlan): array
    {
        $currentSubscription = $user->currentSubscription;

        if (!$currentSubscription) {
            // New subscription
            return [
                'type' => 'new',
                'amount' => $newPlan->price,
                'description' => "New subscription: {$newPlan->name}",
                'current_plan' => null,
                'new_plan' => $newPlan,
            ];
        }

        $currentPlan = $currentSubscription->plan;

        if ($currentPlan->id === $newPlan->id) {
            // Renewal of same plan
            return [
                'type' => 'renewal',
                'amount' => $newPlan->price,
                'description' => "Renewal: {$newPlan->name}",
                'current_plan' => $currentPlan,
                'new_plan' => $newPlan,
            ];
        }

        // Plan change (upgrade/downgrade)
        // For simplicity, we'll charge the full price of the new plan
        // In a more complex system, you might implement prorating
        return [
            'type' => 'change',
            'amount' => $newPlan->price,
            'description' => "Plan change from {$currentPlan->name} to {$newPlan->name}",
            'current_plan' => $currentPlan,
            'new_plan' => $newPlan,
        ];
    }

    /**
     * Create an order for the purchase.
     */
    private function createOrder(User $user, SubscriptionPlan $plan, array $pricingData): Order
    {
        return DB::transaction(function () use ($user, $plan, $pricingData) {
            $order = Order::create([
                'user_id' => $user->id,
                'status' => 'new',
                'total_amount' => $pricingData['amount'],
                'currency' => $plan->currency,
                'notes' => $pricingData['description'],
            ]);

            Log::info('Order created for VPN purchase', [
                'order_id' => $order->id,
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'amount' => $pricingData['amount'],
                'type' => $pricingData['type'],
            ]);

            return $order;
        });
    }

    /**
     * Create a payment request DTO.
     */
    private function createPaymentRequest(Order $order, User $user, array $pricingData, string $uuid): PaymentRequest
    {
        return new PaymentRequest(
            order: $order,
            user: $user,
            amount: $pricingData['amount'],
            currency: $order->currency,
            description: $pricingData['description'],
            successUrl: route('vpn.plan.success', ['uuid' => $uuid, 'order' => $order->public_id]),
            failUrl: route('vpn.plan.failure', ['uuid' => $uuid, 'order' => $order->public_id]),
            metadata: [
                'subscription_type' => $pricingData['type'],
                'plan_id' => $pricingData['new_plan']->id,
                'user_uuid' => $uuid,
            ]
        );
    }

    /**
     * Handle successful payment response.
     */
    private function handlePaymentResponse($paymentResponse, Order $order, Request $request): RedirectResponse|JsonResponse
    {
        if ($paymentResponse->isSuccessful()) {
            Log::info('Payment initiated successfully', [
                'order_id' => $order->id,
                'payment_id' => $paymentResponse->paymentId,
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'payment_url' => $paymentResponse->paymentUrl,
                    'payment_id' => $paymentResponse->paymentId,
                ]);
            }

            if ($paymentResponse->paymentUrl) {
                return redirect($paymentResponse->paymentUrl);
            }

            return redirect()->route('vpn.plan.success', [
                'uuid' => $request->route('uuid'),
                'order' => $order->public_id
            ])->with('success', 'Payment completed successfully.');
        }

        throw new PaymentException('Payment failed: ' . $paymentResponse->message);
    }

    /**
     * Handle validation errors.
     */
    private function handleValidationError(ValidationException $e, Request $request): RedirectResponse|JsonResponse
    {
        Log::warning('VPN purchase validation failed', [
            'errors' => $e->errors(),
            'input' => $request->except(['_token']),
        ]);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors(),
            ], 422);
        }

        return back()->withErrors($e->errors())->withInput();
    }

    /**
     * Handle payment errors.
     */
    private function handlePaymentError(PaymentException $e, Request $request): RedirectResponse|JsonResponse
    {
        Log::error('VPN purchase payment error', [
            'error' => $e->getMessage(),
            'input' => $request->except(['_token']),
        ]);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed: ' . $e->getMessage(),
            ], 500);
        }

        return back()->with('error', 'Payment processing failed: ' . $e->getMessage())->withInput();
    }

    /**
     * Handle general errors.
     */
    private function handleGeneralError(\Exception $e, Request $request): RedirectResponse|JsonResponse
    {
        Log::error('VPN purchase general error', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'input' => $request->except(['_token']),
        ]);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred. Please try again.',
            ], 500);
        }

        return back()->with('error', 'An unexpected error occurred. Please try again.')->withInput();
    }
}
