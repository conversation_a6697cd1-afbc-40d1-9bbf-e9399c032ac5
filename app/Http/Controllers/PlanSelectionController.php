<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\PaymentMethod;
use App\Services\Payment\PaymentGatewayManager;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class PlanSelectionController extends Controller
{
    public function __construct(
        private PaymentGatewayManager $gatewayManager
    ) {}

    /**
     * Display available subscription plans and payment methods for a user.
     */
    public function show(string $uuid): View
    {
        try {
            // Validate and get the user
            $user = $this->validateAndGetUser($uuid);

            // Get current subscription information
            $currentSubscription = $user->currentSubscription;
            $currentPlan = $currentSubscription?->plan;

            // Load available subscription plans
            $availablePlans = $this->getAvailablePlans($currentPlan);

            // Load available payment methods
            $paymentMethods = $this->getAvailablePaymentMethods();

            // Prepare subscription status data
            $subscriptionStatus = $this->getSubscriptionStatus($currentSubscription);

            Log::info('Plan selection page accessed', [
                'user_id' => $user->id,
                'current_plan_id' => $currentPlan?->id,
                'available_plans_count' => $availablePlans->count(),
                'payment_methods_count' => $paymentMethods->count(),
            ]);

            return view('vpn.plan-selection', [
                'user' => $user,
                'currentPlan' => $currentPlan,
                'currentSubscription' => $currentSubscription,
                'availablePlans' => $availablePlans,
                'paymentMethods' => $paymentMethods,
                'subscriptionStatus' => $subscriptionStatus,
                'uuid' => $uuid,
            ]);

        } catch (\Exception $e) {
            Log::error('Error loading plan selection page', [
                'uuid' => $uuid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            abort(404, 'User not found or access denied.');
        }
    }

    /**
     * Validate UUID and get the user.
     */
    private function validateAndGetUser(string $uuid): User
    {
        if (!preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $uuid)) {
            throw new \InvalidArgumentException('Invalid user UUID format.');
        }

        $user = User::with(['currentSubscription.plan'])->where('id', $uuid)->first();

        if (!$user) {
            throw new \InvalidArgumentException('User not found.');
        }

        return $user;
    }

    /**
     * Get available subscription plans for the user.
     */
    private function getAvailablePlans(?SubscriptionPlan $currentPlan): \Illuminate\Database\Eloquent\Collection
    {
        // Get all public and active plans
        $publicPlans = SubscriptionPlan::where('is_public', true)
            ->where('is_active', true)
            ->where('is_archived', false)
            ->orderBy('price')
            ->get();

        // If user has a current plan that's not in the public list, we need to include it
        if ($currentPlan && !$publicPlans->contains('id', $currentPlan->id)) {
            // Add current plan at the beginning of the collection
            $publicPlans = $publicPlans->prepend($currentPlan);
        }

        return $publicPlans;
    }

    /**
     * Get available payment methods.
     */
    private function getAvailablePaymentMethods(): \Illuminate\Database\Eloquent\Collection
    {
        // Get active payment methods
        $paymentMethods = PaymentMethod::where('is_active', true)
            ->orderBy('name')
            ->get();

        // Filter payment methods based on gateway availability
        return $paymentMethods->filter(function ($paymentMethod) {
            try {
                $gateway = $this->gatewayManager->getGateway($paymentMethod->code);
                return $gateway->isAvailable();
            } catch (\Exception $e) {
                Log::warning('Payment gateway not available', [
                    'payment_method' => $paymentMethod->code,
                    'error' => $e->getMessage(),
                ]);
                return false;
            }
        });
    }

    /**
     * Get subscription status information.
     */
    private function getSubscriptionStatus($currentSubscription): array
    {
        if (!$currentSubscription) {
            return [
                'has_subscription' => false,
                'status' => 'none',
                'message' => 'No active subscription',
            ];
        }

        $now = now();
        $endDate = $currentSubscription->end_date;
        $daysRemaining = $now->diffInDays($endDate, false);

        if ($daysRemaining < 0) {
            return [
                'has_subscription' => true,
                'status' => 'expired',
                'message' => 'Subscription expired',
                'end_date' => $endDate,
                'days_remaining' => 0,
            ];
        }

        if ($daysRemaining <= 7) {
            $status = 'expiring_soon';
            $message = $daysRemaining === 0
                ? 'Subscription expires today'
                : "Subscription expires in {$daysRemaining} day(s)";
        } else {
            $status = 'active';
            $message = "Subscription active until {$endDate->format('M j, Y')}";
        }

        return [
            'has_subscription' => true,
            'status' => $status,
            'message' => $message,
            'end_date' => $endDate,
            'days_remaining' => max(0, $daysRemaining),
            'hours_remaining' => max(0, $now->diffInHours($endDate, false)),
        ];
    }
}
