<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\PaymentMethod;
use Illuminate\View\View;
use Illuminate\Support\Facades\Log;

class PlanSelectionController extends Controller
{
    public function __construct() {}

    /**
     * Display available subscription plans and payment methods for a user.
     */
    public function show(string $uuid): View
    {
        try {
            // Find user by UUID (id field is UUID in this model)
            $user = User::where('id', $uuid)->firstOrFail();

            // Get available subscription plans (active, public, not archived)
            $availablePlans = SubscriptionPlan::active()
                ->public()
                ->notArchived()
                ->orderBy('price')
                ->get();

            // Get available payment methods (active and public)
            $paymentMethods = PaymentMethod::active()
                ->where('is_public', true)
                ->orderBy('name')
                ->get();

            // Get current subscription and plan
            $currentSubscription = $user->currentSubscription;
            $currentPlan = $currentSubscription?->plan;

            // Determine subscription status
            $subscriptionStatus = $this->getSubscriptionStatus($currentSubscription);

            Log::info('Plan selection page accessed', [
                'user_uuid' => $uuid,
                'user_id' => $user->id,
                'current_subscription_id' => $currentSubscription?->id,
                'current_plan_id' => $currentPlan?->id,
                'available_plans_count' => $availablePlans->count(),
                'payment_methods_count' => $paymentMethods->count(),
            ]);

            return view('vpn.plan-selection', [
                'uuid' => $uuid,
                'user' => $user,
                'availablePlans' => $availablePlans,
                'paymentMethods' => $paymentMethods,
                'currentPlan' => $currentPlan,
                'currentSubscription' => $currentSubscription,
                'subscriptionStatus' => $subscriptionStatus,
            ]);

        } catch (\Exception $e) {
            Log::error('Plan selection page error', [
                'user_uuid' => $uuid,
                'error' => $e->getMessage(),
            ]);

            abort(404, 'User not found or access denied');
        }
    }

    /**
     * Get subscription status information for display.
     */
    private function getSubscriptionStatus($currentSubscription): array
    {
        if (!$currentSubscription) {
            return [
                'has_subscription' => false,
                'status' => 'none',
                'message' => 'You don\'t have an active subscription. Choose a plan to get started.',
            ];
        }

        if ($currentSubscription->isExpired()) {
            return [
                'has_subscription' => true,
                'status' => 'expired',
                'message' => 'Your subscription has expired. Please renew or choose a new plan.',
            ];
        }

        if ($currentSubscription->isAboutToExpire(7)) {
            $remainingDays = $currentSubscription->getRemainingDays();
            return [
                'has_subscription' => true,
                'status' => 'expiring-soon',
                'message' => "Your subscription expires in {$remainingDays} day(s). Consider renewing or upgrading.",
            ];
        }

        if ($currentSubscription->isActive()) {
            $remainingDays = $currentSubscription->getRemainingDays();
            return [
                'has_subscription' => true,
                'status' => 'active',
                'message' => "Your subscription is active and expires in {$remainingDays} day(s). You can upgrade or extend your plan.",
            ];
        }

        return [
            'has_subscription' => true,
            'status' => 'unknown',
            'message' => 'Your subscription status is unclear. Please contact support.',
        ];
    }
}
