<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\Payment\WebhookService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class WebhookController extends Controller
{
    private WebhookService $webhookService;

    public function __construct(WebhookService $webhookService)
    {
        $this->webhookService = $webhookService;
    }

    /**
     * Handle T-Bank webhook.
     */
    public function tbank(Request $request): JsonResponse
    {
        return $this->handleWebhook($request, 'tbank');
    }

    /**
     * Handle generic webhook for any gateway.
     */
    public function handle(Request $request, string $gateway): JsonResponse
    {
        return $this->handleWebhook($request, $gateway);
    }

    /**
     * Process webhook for a specific gateway.
     */
    private function handleWebhook(Request $request, string $gateway): JsonResponse
    {
        try {
            Log::info("Webhook received for {$gateway}", [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'content_type' => $request->header('Content-Type'),
                'payload_size' => strlen($request->getContent()),
            ]);

            // Verify webhook signature if required
            if (config("payments.gateways.{$gateway}.webhook.verify_signature", true)) {
                if (!$this->webhookService->verifyWebhookSignature($request, $gateway)) {
                    Log::warning("Webhook signature verification failed for {$gateway}", [
                        'ip' => $request->ip(),
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid signature',
                    ], 401);
                }
            }

            // Process the webhook
            $result = $this->webhookService->processWebhook($request, $gateway);

            $statusCode = $result['success'] ? 200 : 400;

            return response()->json($result, $statusCode);

        } catch (\Exception $e) {
            Log::error("Webhook processing error for {$gateway}", [
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
            ], 500);
        }
    }

    /**
     * Get webhook statistics (admin endpoint).
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $gateway = $request->query('gateway');
            $days = (int) $request->query('days', 30);

            $stats = $this->webhookService->getWebhookStatistics($gateway, $days);

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get webhook statistics', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get statistics',
            ], 500);
        }
    }

    /**
     * Retry failed webhooks (admin endpoint).
     */
    public function retryFailed(Request $request): JsonResponse
    {
        try {
            $gateway = $request->input('gateway');
            $maxAge = (int) $request->input('max_age', 24);

            $results = $this->webhookService->retryFailedWebhooks($gateway, $maxAge);

            return response()->json([
                'success' => true,
                'data' => $results,
                'message' => 'Failed webhooks retry completed',
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to retry webhooks', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retry webhooks',
            ], 500);
        }
    }

    /**
     * Get recent webhook activity (admin endpoint).
     */
    public function activity(Request $request): JsonResponse
    {
        try {
            $limit = (int) $request->query('limit', 50);

            $activity = $this->webhookService->getRecentWebhookActivity($limit);

            return response()->json([
                'success' => true,
                'data' => $activity,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get webhook activity', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get webhook activity',
            ], 500);
        }
    }

    /**
     * Clean up old webhook records (admin endpoint).
     */
    public function cleanup(Request $request): JsonResponse
    {
        try {
            $daysToKeep = (int) $request->input('days_to_keep', 90);

            $deleted = $this->webhookService->cleanupOldWebhooks($daysToKeep);

            return response()->json([
                'success' => true,
                'data' => [
                    'deleted_count' => $deleted,
                    'days_to_keep' => $daysToKeep,
                ],
                'message' => 'Webhook cleanup completed',
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to cleanup webhooks', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to cleanup webhooks',
            ], 500);
        }
    }
}
