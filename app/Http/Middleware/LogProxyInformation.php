<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class LogProxyInformation
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Log proxy information if debugging is enabled
        if (config('proxy.debug.log_headers', false)) {
            $this->logProxyHeaders($request);
        }

        // Log suspicious requests if enabled
        if (config('proxy.security.log_suspicious_requests', true)) {
            $this->logSuspiciousRequests($request);
        }

        // Add IP information to response headers in debug mode
        $response = $next($request);

        if (config('proxy.debug.show_ip_info', false) && app()->environment('local')) {
            $this->addDebugHeaders($request, $response);
        }

        return $response;
    }

    /**
     * Log proxy headers for debugging.
     */
    private function logProxyHeaders(Request $request): void
    {
        $proxyHeaders = [
            'X-Forwarded-For' => $request->header('X-Forwarded-For'),
            'X-Forwarded-Host' => $request->header('X-Forwarded-Host'),
            'X-Forwarded-Port' => $request->header('X-Forwarded-Port'),
            'X-Forwarded-Proto' => $request->header('X-Forwarded-Proto'),
            'X-Forwarded-Prefix' => $request->header('X-Forwarded-Prefix'),
            'X-Real-IP' => $request->header('X-Real-IP'),
            'CF-Connecting-IP' => $request->header('CF-Connecting-IP'), // Cloudflare
            'True-Client-IP' => $request->header('True-Client-IP'), // Cloudflare
            'X-Client-IP' => $request->header('X-Client-IP'),
            'X-Cluster-Client-IP' => $request->header('X-Cluster-Client-IP'),
            'Forwarded' => $request->header('Forwarded'),
        ];

        $filteredHeaders = array_filter($proxyHeaders);

        if (!empty($filteredHeaders)) {
            Log::debug('Proxy headers detected', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'client_ip' => $request->ip(),
                'proxy_headers' => $filteredHeaders,
                'user_agent' => $request->userAgent(),
            ]);
        }
    }

    /**
     * Log suspicious requests.
     */
    private function logSuspiciousRequests(Request $request): void
    {
        $suspiciousIndicators = [];

        // Check for multiple X-Forwarded-For entries (potential spoofing)
        $xForwardedFor = $request->header('X-Forwarded-For');
        if ($xForwardedFor && substr_count($xForwardedFor, ',') > 2) {
            $suspiciousIndicators[] = 'Multiple X-Forwarded-For entries';
        }

        // Check for mismatched host headers
        $host = $request->header('Host');
        $xForwardedHost = $request->header('X-Forwarded-Host');
        if ($xForwardedHost && $host !== $xForwardedHost) {
            $suspiciousIndicators[] = 'Mismatched Host and X-Forwarded-Host headers';
        }

        // Check for suspicious user agents
        $userAgent = $request->userAgent();
        if ($userAgent && $this->isSuspiciousUserAgent($userAgent)) {
            $suspiciousIndicators[] = 'Suspicious user agent';
        }

        // Check for requests to sensitive endpoints from unexpected IPs
        if ($this->isSensitiveEndpoint($request) && !$this->isFromTrustedSource($request)) {
            $suspiciousIndicators[] = 'Sensitive endpoint accessed from untrusted source';
        }

        if (!empty($suspiciousIndicators)) {
            Log::warning('Suspicious request detected', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'client_ip' => $request->ip(),
                'indicators' => $suspiciousIndicators,
                'headers' => [
                    'Host' => $request->header('Host'),
                    'X-Forwarded-For' => $request->header('X-Forwarded-For'),
                    'X-Forwarded-Host' => $request->header('X-Forwarded-Host'),
                    'User-Agent' => $request->userAgent(),
                ],
            ]);
        }
    }

    /**
     * Add debug headers to response.
     */
    private function addDebugHeaders(Request $request, Response $response): void
    {
        $response->headers->set('X-Debug-Client-IP', $request->ip());
        $response->headers->set('X-Debug-Server-IP', $_SERVER['SERVER_ADDR'] ?? 'unknown');
        $response->headers->set('X-Debug-X-Forwarded-For', $request->header('X-Forwarded-For', 'none'));
        $response->headers->set('X-Debug-Host', $request->header('Host'));
        $response->headers->set('X-Debug-Is-Secure', $request->isSecure() ? 'true' : 'false');
    }

    /**
     * Check if user agent is suspicious.
     */
    private function isSuspiciousUserAgent(string $userAgent): bool
    {
        $suspiciousPatterns = [
            '/bot/i',
            '/crawler/i',
            '/spider/i',
            '/scanner/i',
            '/curl/i',
            '/wget/i',
            '/python/i',
            '/perl/i',
            '/php/i',
            '/java/i',
            '/go-http-client/i',
            '/masscan/i',
            '/nmap/i',
            '/sqlmap/i',
            '/nikto/i',
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if the request is to a sensitive endpoint.
     */
    private function isSensitiveEndpoint(Request $request): bool
    {
        $sensitivePatterns = [
            '/webhook/',
            '/api/webhook/',
            '/payment/',
            '/admin/',
            '/.env',
            '/config/',
            '/database/',
            '/storage/',
        ];

        $path = $request->path();

        foreach ($sensitivePatterns as $pattern) {
            if (str_contains($path, trim($pattern, '/'))) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if request is from a trusted source.
     */
    private function isFromTrustedSource(Request $request): bool
    {
        $clientIp = $request->ip();
        
        // Check against trusted proxy IPs
        $trustedProxies = config('proxy.trusted_proxies.ips', []);
        foreach ($trustedProxies as $proxy) {
            if ($this->ipInRange($clientIp, $proxy)) {
                return true;
            }
        }

        // Check against webhook-specific trusted IPs
        $webhookIps = config('proxy.webhook.trusted_ips', []);
        foreach ($webhookIps as $ip) {
            if ($this->ipInRange($clientIp, $ip)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if IP is in range (supports CIDR notation).
     */
    private function ipInRange(string $ip, string $range): bool
    {
        if ($range === '*') {
            return true;
        }

        if (!str_contains($range, '/')) {
            return $ip === $range;
        }

        [$subnet, $mask] = explode('/', $range);
        
        if (!filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) ||
            !filter_var($subnet, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return false;
        }

        $ipLong = ip2long($ip);
        $subnetLong = ip2long($subnet);
        $maskLong = -1 << (32 - (int) $mask);

        return ($ipLong & $maskLong) === ($subnetLong & $maskLong);
    }
}
