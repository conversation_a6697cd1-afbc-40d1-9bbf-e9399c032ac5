<?php

namespace App\Jobs;

use App\Models\XuiServer;
use App\Services\UserOnlineStatusSyncService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncUserOnlineStatusJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;
    public int $timeout = 120; // 2 minutes
    public int $backoff = 30; // 30 seconds between retries

    /**
     * Create a new job instance.
     */
    public function __construct(
        public int $serverId
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("Starting async user status sync for server {$this->serverId}");

        try {
            $server = XuiServer::findOrFail($this->serverId);
            
            if (!$server->is_active) {
                Log::info("Server {$this->serverId} is not active, skipping user status sync");
                return;
            }

            $syncService = new UserOnlineStatusSyncService();
            $result = $syncService->syncSingleServerUserStatus($server);

            if ($result) {
                Log::info("Successfully completed async user status sync for server {$this->serverId}");
            } else {
                Log::warning("Async user status sync completed with warnings for server {$this->serverId}");
            }

        } catch (\Exception $e) {
            Log::error("Failed to sync user status for server {$this->serverId}: {$e->getMessage()}", [
                'server_id' => $this->serverId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e; // Re-throw to trigger retry mechanism
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("User status sync job failed permanently for server {$this->serverId}: {$exception->getMessage()}", [
            'server_id' => $this->serverId,
            'error' => $exception->getMessage(),
        ]);
    }
}
