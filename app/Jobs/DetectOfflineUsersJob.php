<?php

namespace App\Jobs;

use App\Services\UserOnlineStatusSyncService;
use App\Services\XuiServerSyncService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class DetectOfflineUsersJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 2;
    public int $timeout = 120; // 2 minutes
    public int $backoff = 30; // 30 seconds between retries

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Starting offline user detection job');

        try {
            $userOnlineSyncService = new UserOnlineStatusSyncService();
            $userOnlineSyncService->detectOfflineUsers();

            Log::info('Offline user detection job completed successfully');

        } catch (\Exception $e) {
            Log::error("Offline user detection job failed: {$e->getMessage()}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e; // Re-throw to trigger retry mechanism
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("Offline user detection job failed permanently: {$exception->getMessage()}", [
            'error' => $exception->getMessage(),
        ]);
    }
}
