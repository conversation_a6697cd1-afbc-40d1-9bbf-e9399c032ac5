---
type: "manual"
---

# 📝 Техническое задание (ТЗ) для проекта SmartVPN

## 🛡️ SmartVPN — краткое техническое задание (ТЗ)

### 📌 Описание проекта

SmartVPN — это система управления подпиской на VPN-услуги, интегрированная с XUI-серверами. Проект включает регистрацию пользователей, оплату тарифов, создание VPN-аккаунтов, контроль трафика, серверные пулы, рефералы и административный мониторинг.

---

## 🧩 Основные модули

### 1. Пользователи (`users`)

* Аутентификация
* Реферальный код + `referred_by_user_id`
* Одна активная подписка
* Отслеживание онлайн-сессий и трафика

### 2. Подписки (`subscriptions`)

* Связь с тарифом (`subscription_plans`)
* Лимит по времени и трафику
* Возможность продления, апгрейда
* Статус: активна, завершена, отменена
* История изменений: `subscription_history`
* Логи подключений: `subscription_access_logs`

### 3. Тарифы (`subscription_plans`)

* Продолжительность (в днях или бессрочно)
* Лимит трафика (и период сброса — day/week/month)
* Цена, валюта, тип
* Поддержка "конструктора тарифов" (кастомные планы)

---

### 4. Заказы и оплаты

#### Заказы (`orders`, `order_items`)

* Содержат одну или несколько позиций (тарифы, услуги)
* Хранят общую сумму и валюту

#### Оплаты (`payments`)

* Привязка к заказу
* Уникальный ID: `PAY-STRP-XXXXXX` (тип + код)
* Статус: `pending`, `paid`, `failed`, `cancelled`, `refunded`
* Метод оплаты (`payment_methods`)
* История входящих webhook'ов: `payment_webhooks`

---

### 5. VPN-сервера и управление

#### Серверы (`xui_servers`)

* IP, порт, токен доступа
* API-интеграция с XUI
* Группировка по пулам (`server_pools`)
* Статистика (`user_online_logs`, `user_traffic_logs`)

#### Пулы (`server_pools`, `xui_server_pool_assignments`)

* Один пул = до 250 пользователей
* Пользователи автоматически распределяются
* Логика назначения: освобождённый слот → используется снова

#### Назначения (`user_server_assignments`)

* Логика, кто и где был назначен (история)

---

### 6. Мониторинг и метрики

* Хранение статистики по серверам и пользователям
* Проверка доступности серверов
* Расчёт нагрузки
* В админке можно мониторить текущую загрузку и онлайн

---

### 7. Рефералы (`referrals`)

* Прямая связь "кто кого пригласил"
* Бонусы в будущем можно активировать
* Возможно расширение до полной системы с начислениями

---

### 8. Администрирование (`settings`, `announces`)

* Управление тарифами, пользователями, серверами
* Публикация анонсов (таблица `announces`)
* Расширяемые настройки (`settings`)
* Логи фоновых задач (`jobs`, `failed_jobs`, `job_batches`)

---

## 🛠️ Архитектура кода (на Laravel 12)

### Основные сервисы:

* `SubscriptionService` — управление подписками
* `PaymentService` — обработка заказов и платежей
* `VpnAccountManager` — создание/удаление VPN-пользователей
* `XuiApiService` — запросы к XUI API
* `XuiServerManager` — управление серверами, мониторинг
* `XuiMonitoringService` — сбор статистики (cron, queue)
* `ReferralService` — обработка реферальных связей

### DTO (Data Transfer Objects):

* Для передачи данных в сервисы (`CreateSubscriptionDTO`, `CreateVpnUserDTO`, `ServerStatsDTO` и т.п.)

---

## 📅 Автоматизация

* События (например, `SubscriptionCreated`) вызывают автоматическое создание аккаунта на сервере
* Cron-задачи:

  * Проверка подписок (истекших/будущих)
  * Мониторинг серверов
  * Очистка и переиспользование пулов

---

## 📄 Вывод

Проект покрывает все базовые и расширенные аспекты VPN-сервиса:

* подписки,
* сервера и пулы,
* учёт трафика,
* оплаты и заказы,
* аналитика,
* инфраструктурный мониторинг.

Структура БД соответствует лучшим практикам и может масштабироваться без переделки архитектуры.
