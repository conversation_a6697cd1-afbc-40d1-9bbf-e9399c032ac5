---
type: "manual"
---

# Xui Server API wrapper

```php
use alirezax5\XuiApi\Panel\MHSanaei as XuiApiService;
```

## About the project

It is a web service project to manage the x-ui panel

## Features

* Support for 90% of panels
* Xray management by API
* Show status
* Change xray version
* Inbound management
* Settings management
* User(admin) management.

## Install

```bash
composer require alirezax5/xuiapi
```

## Panels

In this project we support 6 panels, but actually use the `MHSanaei/3x-ui`:

* [vaxilu](https://github.com/vaxilu/x-ui) =>  [Vaxilu.php](https://github.com/alirezax5/xuiapi/blob/main/src/Panel/Vaxilu.php)
* [FranzKafkaYu](https://github.com/FranzKafkaYu/x-ui) =>  [FranzKafkaYu.php](https://github.com/alirezax5/xuiapi/blob/main/src/Panel/FranzKafkaYu.php)
* [HexaSoftwareTech](https://github.com/HexaSoftwareTech/x-ui) =>  [HexaSoftwareTech.php](https://github.com/alirezax5/xuiapi/blob/main/src/Panel/HexaSoftwareTech.php)
* [MHSanaei](https://github.com/MHSanaei/3x-ui)  =>  [MHSanaei.php](https://github.com/alirezax5/xuiapi/blob/main/src/Panel/MHSanaei.php)
* [alireza0](https://github.com/alireza0/x-ui) =>  [Alireza0.php](https://github.com/alirezax5/xuiapi/blob/main/src/Panel/Alireza0.php)
* [NidukaAkalanka](https://github.com/NidukaAkalanka/x-ui-english) =>  [NidukaAkalanka.php](https://github.com/alirezax5/xuiapi/blob/main/src/Panel/NidukaAkalanka.php)

## How to use

### Examples

#### List Inbounds

```php
<?php
require __DIR__ . '/vendor/autoload.php';
$xui = new \alirezax5\XuiApi\Panel\MHSanaei('YOU_PANEL_URL', 'YOU_PANEL_USERNAME', 'YOU_PANEL_PASSWORD');
#Set Up Cookie
$xui->setCookie(__DIR__.'/Cookie.txt');
$xui->login();
print_r($xui->listInbound());
```

#### Add new client

```php
<?php
require __DIR__ . '/vendor/autoload.php';
$xui = new \alirezax5\XuiApi\Panel\MHSanaei('YOU_PANEL_URL', 'YOU_PANEL_USERNAME', 'YOU_PANEL_PASSWORD');
$xui->setCookie(__DIR__.'/Cookie.txt');
$xui->login();

var_dump($xui->addnewClient('inbound id[int]', 'uuid or password[string]', 'email[string]', 'subId[string]', 'tgid[string]', 'flow[string]', 'traffic[int Gb]', 'time_ms[big int]', 'limitIp[int]', 'fingerprint[string]', 'isTrojan[bool]'));

// php 8
$xui->addnewClient(id:123, uuid: 'uuid', email: 'email', totalgb: 0, eT: 0);
```

#### Edit Client

```php
<?php
require __DIR__ . '/vendor/autoload.php';
$xui = new \alirezax5\XuiApi\Panel\MHSanaei('YOU_PANEL_URL', 'YOU_PANEL_USERNAME', 'YOU_PANEL_PASSWORD');
$xui->setCookie(__DIR__.'/Cookie.txt');
$xui->login();

$xui->editClient('inbound id[int]','uuid or password[string]','email[string]','uuid or password[string]','traffic[int Gb]','time_ms[big int]','tgId !just MHSanaei panel[string]','subId !just MHSanaei panel [string]','limitIp[int]','fingerprint[string]','flow[string]');
$xui->editClientByEmail('inbound id[int]','email[string]','email[string]','uuid or password[string]','traffic[int Gb]','time_ms[big int]','tgId !just MHSanaei panel [string]','subId !just MHSanaei panel [string]','limitIp[int]','fingerprint[string]','flow[string]');
$xui->editClientWithKey('inbound id[int]','uuid or password[string]','key[string]','value');
$xui->editClientByEmailWithKey('inbound id[int]','email[string]','key[string]','value');

//example for editClientByEmailWithKey:
$xui->editClientByEmailWithKey(1,'alirezax5','email','newEmail');
```

#### Remove Client

```php
<?php
require __DIR__ . '/vendor/autoload.php';
$xui = new \alirezax5\XuiApi\Panel\MHSanaei('YOU_PANEL_URL', 'YOU_PANEL_USERNAME', 'YOU_PANEL_PASSWORD');
$xui->setCookie(__DIR__.'/Cookie.txt');
$xui->login();

$xui->removeClient('inbound id[int]','uuid or password[string]');
$xui->removeClientByEmail('inbound id[int]','email[string]');
```

#### Edit Client Traffic

```php
<?php
require __DIR__ . '/vendor/autoload.php';
$xui = new \alirezax5\XuiApi\Panel\MHSanaei('YOU_PANEL_URL', 'YOU_PANEL_USERNAME', 'YOU_PANEL_PASSWORD');
$xui->setCookie(__DIR__.'/Cookie.txt');
$xui->login();

$xui->editClientTraffic('inbound id[int]','uuid or password[string]','Gb');
$xui->editClientTrafficByEmail('inbound id[int]','email[string]','Gb');
```

#### Edit Inbound Data

```php
<?php
require __DIR__ . '/vendor/autoload.php';
$xui = new \alirezax5\XuiApi\Panel\MHSanaei('YOU_PANEL_URL', 'YOU_PANEL_USERNAME', 'YOU_PANEL_PASSWORD');
$xui->setCookie(__DIR__.'/Cookie.txt');
$xui->login();

$xui->editInboundDataWithKey('inbound id[int]','key[string]','value');
$xui->editInboundDataWithKey(1,'email','newEmail');
```

## All available methods

Full list of methods is in [Panel/MHSanaei.php](https://github.com/alirezax5/xuiapi/blob/main/src/Panel/MHSanaei.php)
Locally in [MHSanaei.php](../src/Panel/MHSanaei.php) and in [Base.php](../src/Panel/Base.php)
