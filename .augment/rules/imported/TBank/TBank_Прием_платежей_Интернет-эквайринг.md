---
type: "manual"
---

# Т‑Банк. Прием платежей. API

> Version 1.75

# Начало работы

## Подключение интернет-эквайринга от Т‑Бизнес

Интернет-эквайринг помогает принимать онлайн-платежи так, как удобно вам и покупателю: на сайте, в мобильном приложении, 
соцсетях, мессенджерах, по e-mail или СМС. Вы можете принимать оплату разными способами, возвращать и замораживать выплаты и 
настраивать рекуррентные платежи.

Чтобы подключить интернет-эквайринг, оставьте [заявку на сайте Т‑Банка](https://www.tbank.ru/kassa/) и заполните анкету компании или ИП. 
Подробнее о подключении можно прочитать [в Т-Помощи](https://www.tbank.ru/business/help/business-payments/internet-acquiring/how-involve/connect/?card=q1) или узнать у персонального менеджера.

## Способы интеграции интернет-эквайринга от Т‑Бизнес

Интернет-эквайринг нужно интегрировать — настроить оплату на сайте или в приложении. Есть четыре способа интеграции:

* Платежный модуль — для сайтов на CMS.
* Платежный виджет — для самописного сайта.
* Мобильная интеграция — для мобильного приложения.
* API — для разработки своей интеграции.

Интегрироваться можно самостоятельно или с помощью разработчика.

### Платежный модуль

Способ интеграции интернет-эквайринга с сайтом, который создан на основе CMS.

Модуль подходит, если ваш сайт собран на CMS — например, 1С-Битрикс, WordPress или Taplink. Т‑Бизнес поддерживает многие популярные CMS,
в некоторые уже встроены модули — их устанавливать не нужно.

Принцип работы:

1. Вы устанавливаете модуль и настраиваете способы оплаты — банковская карта, T‑Pay, SberPay, Mir Pay, СБП, Долями, в рассрочку.  
Можно оставить все способы или выбрать определённые.
2. На странице сайта появляется кнопка оплаты. 
3. Покупатель нажимает на кнопку и переходит на платежную форму с разными способами оплаты.

На этой [странице](https://www.tbank.ru/kassa/dev/cms/) представлен список систем управления контентом (CMS), для которых разработаны платежные модули. Если вашего решения нет в этом списке, мы рекомендуем настроить передачу объекта `DATA` с параметром `connection_type`. В этом параметре укажите название модуля, через который вы интегрируетесь. Более подробную информацию вы можете в описании метода [Init](https://www.tbank.ru/kassa/dev/payments/index.html#tag/Standartnyj-platezh/operation/Init). Если у вас возникнут вопросы или потребуется дополнительная настройка, пожалуйста, обратитесь в техническую поддержку вашего модуля.

[Инструкции по интеграции с помощью платежного модуля](https://www.tbank.ru/kassa/dev/cms/)

### Платежный виджет

Способ интеграции интернет-эквайринга с самописным сайтом.

Способ подходит, если:
* ваш сайт самописный или на CMS, для которой в Т‑Банке нет платежного модуля;
* вы не планируете принимать автоплатежи.

Для интеграции потребуется помощь программиста.

[Инструкция по интеграции](https://www.tbank.ru/kassa/dev/integrationjs/)

 ### Мобильная интеграция

Способ интеграции интернет-эквайринга с мобильным приложением. <!--Покупатель оплачивает товар без перехода в мобильный браузер, оставаясь внутри вашего приложения.-->

Подключение осуществляется через [WebView ПФ](https://www.tbank.ru/kassa/dev/webview/)

### API

Самый гибкий и сложный способ интеграции интернет-эквайринга. Например, API подходит, если у вас самописный сайт и вы хотите 
настроить оплату под запросы бизнеса — совмещать в платежной форме разные способы оплаты, принимать рекуррентные платежи или 
подключать другие сервисы Т‑Бизнес.

Для интеграции понадобится помощь программиста.

## Платежная форма

Платежная форма — это готовый интерфейс с встроенными способами оплаты, который позволяет принимать платежи онлайн.

Для использования платежной формы нужно подключить интернет-эквайринг, настроить терминал и интегрировать платежную форму на
ваш сайт одним из способов выше.

Для открытия платежной формы на вашем сайте, требуется выполнить следущие шаги:

1. Ваш бекенд-сервис должен вызвать запрос [Init](https://www.tbank.ru/kassa/dev/payments/#tag/Standartnyj-platezh/operation/Init).
2. Передать `PaymentUrl` из ответа на запрос [Init](https://www.tbank.ru/kassa/dev/payments/#tag/Standartnyj-platezh/operation/Init) в фронтенд-приложение.
3. Произвести редирект на полученный URL.

Для открытия платежной формы в iframe см. раздел "Платежная форма в iframe"

### Платежная форма в WebView <a name="WebView"></a>  
WebView — это встроенный в приложение браузер. Таким образом, он представляет собой крупномасштабный программный компонент, который позволяет использовать веб-контент внутри приложений.
Инструкция по интеграции мобильного приложения через [WebView](https://www.tbank.ru/kassa/dev/webview/)

### Платежная форма в iframe

Для корректной работы платежной формы, открытой в iframe, нужно использовать скрипт интеграции по [инструкции](https://www.tbank.ru/kassa/dev/integrationjs/index.html#section/Moduli/Otkrytie-platezhnoj-formy-v-iframe).

### Кастомизация платежной формы

Платежную форму можно кастомизировать — настроить под себя и своих клиентов. Для 
установки кастомизации обратитесь к вашему персональному менеджеру и передайте пожелания по настройкам.

#### Список доступных настроек кастомизации

|**Возможности кастомизации** | **Дополнительное описание**                                                                                                                                                                                          |
|--- |----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|Брендирование UI платежной формы | <li>Добавлять логотим своей компании в платежную форму — логотип отобразится рядом с суммой заказа </li><li>Управлять цветами кнопок — кнопка **Оплатить** и другие кнопки со страниц статусов и модальных окон</li> |
|Управление блоком детализации (информация о заказе и магазине) | <li>Делать блок свернутым и развернутым по умолчанию</li><li> Скрывать  блок с детализацией на платежной форме</li><li>Менять порядок строк в детализации</li>                                                       |
|Управление светлой и темной темой | <li>Показывать темную или светлую тему по умолчанию</li><li>Отключать темную или светлую тему</li>                                                                                                                   |


<!--#### Установить последнюю версию Acquiring SDK
 <style>
.block {
text-decoration: none;
padding: 2rem;
margin: 2rem;
border: 0.2rem solid #dddbd9;
border-radius: 1rem;
box-shadow: 0 1.5rem 0.5rem -0.5rem rgba(70,70,94,.1); 
position: auto;  overflow: auto; 
}

.ios_upd {
text-decoration: none;
padding: 10px;
margin: 10px;
border: 1px solid #949996;
border-radius: 1rem;
box-shadow: 0 1.5rem 0.5rem -0.5rem rgba(70,70,94,.1); 
position: auto;  overflow: auto;
display: none;
}
.android_upd {
text-decoration: none;
padding: 10px;
margin: 10px;
border: 1px solid #949996;
border-radius: 1rem;
box-shadow: 0 1.5rem 0.5rem -0.5rem rgba(70,70,94,.1); 
position: auto;  overflow: auto;
display: none;
}
p:hover + .ios_upd {
display: block;
}
p:hover + .android_upd {
display: block;
}
.updtext {
	text-decoration: underline;
	text-decoration-style: dotted;
}
</style>
<div class="block">
	IOS
	<br>
	Релиз 3.1.1 от 12.09.2023
	<br>
	<a href="https://github.com/tinkoff-mobile-tech/AcquiringSdk_IOS">Ссылка на скачивание</a>
	<p class="updtext">Что изменилось (?)</p>
		<div class="ios_upd">
			<ul>
				<li>Новая фича </li>
				<li>ещё одна новая фича</li>
				<li>ещё одна</li>
			</ul>
		</div>
</div>
<div class="block">
	Android
	<br>
	Релиз 3.1.2 от 12.09.2023
	<br>
	<a href="https://github.com/tinkoff-mobile-tech/AcquiringSdkAndroid">Ссылка на скачивание</a>
	<p class="updtext">Что изменилось (?)</p>
		<div class="android_upd">
			<ul>
				<li>Новая фича </li>
				<li>ещё одна новая фича</li>
				<li>ещё одна</li>
			</ul>
		</div>
</div> -->

## Безопасность при интеграции

Убедитесь, что вы используете последнюю версию интеграции и [генерируете и передаете корректный токен](https://www.tbank.ru/kassa/dev/payments/#section/Token) при любом
способе интеграции. 

Если ваш сайт собран на CMS, нужно использовать новейшую версию платежного модуля, доступную на [сайте Т‑Бизнес](https://www.tbank.ru/kassa/dev/cms/) — это
источник актуальных версий. Современные модули для популярных CMS генерируют корректный токен автоматически.

Дополнительные обязательные меры, которые нужно соблюдать при интеграции с MAPI: 

1. Сверяйте параметры созданных заказов при любых способах интеграции с MAPI, особенно при использовании платежного виджета. Если вы обнаружите несоответствие между ожидаемой суммой заказа и фактической суммой операции, не отправляйте товар и немедленно свяжитесь с Т‑Банком. 

Для сверки параметров доступно несколько способов:

- Получение уведомлений:
	
  - По электронной почте: когда платёж переходит в статус `CONFIRMED`, на указанную почту будет отправлено письмо
	
  - По HTTP: MAPI отправляет POST-запрос на указанный URL при каждом изменении статуса платежа

- Вызов метода [GetState](#tag/Standartnyj-platezh/operation/GetState):
	
  - Метод GetState возвращает основные параметры и текущий статус платежа. Рекомендуется дополнительно проверять или подтверждать дополнительные данные заказа, такие как `PaymentId` и `Amount`. Особенно важно сравнивать сумму Amount, полученную в уведомлении или через метод GetState, с ожидаемой стоимостью заказа. Это поможет избежать ошибок при обработке платежей.

2. Обновляйте модули для CMS. Современные модули для популярных CMS сверяют суммы заказов автоматически.

Если вы не применяете эти меры безопасности на вашем сайте или используете программное обеспечение для интеграции
не с [сайта Т‑Бизнес](https://www.tbank.ru/kassa/develop/), вы сами отвечаете за возможные риски и неблагоприятные последствия, связанные
с использованием такого программного обеспечения.

## Обработка карточных данных

Платежные системы разработали требования к безопасности карточных данных клиентов — **Payment Card Industry Data Security Standard** (PCI DSS).
Стандарт PCI DSS — это международный стандарт безопасности, созданный специально для защиты данных платежных карт. Он позволяет защитить организацию от инцидентов безопасности и обеспечить необходимый уровень защищенности во всей платежной системе. Соответствовать правилам стандарта PCI DSS должны все организации.

Если:

- У вас нет сертификации PCI DSS, вы можете использовать платежную форму Т‑Бизнес. В этом случае все операции, которые связаны с обработкой критичных данных, проводятся на стороне Т‑Бизнес. Мерчанту достаточно настроить интеграцию с MerchantAPI и инициализировать платеж. Клиент будет перенаправлен на платежную форму, в которую он сможет ввести данные карты. Когда
платеж завершится, клиент снова увидит сайт мерчанта.

- Вы имеете сертификацию PCI DSS, то можете собирать и хранить карточные данные клиентов. В этом случае
MerchantAPI получает зашифрованные карточные данные от мерчанта.

[Подробнее о режимах передачи карточных данных](#tag/Scenarii-oplaty-po-karte/Scenarii-platezha)

# Передача признака инициатора операции

Платежные системы хотят понимать, кем была инициирована карточная операция. Это особенно важно при проведении операций без 3DS 
и по сохраненным данным.

Для выполнения требования регулятора мы добавили новый атрибут `OperationInitiatorType` в метод [Init](#tag/Standartnyj-platezh/operation/Init). В значении этого
атрибута мы ожидаем получать признак того, кем была инициирована операция и какой способ предоставления реквизитов был использован.

Подробное описание сценариев проведения операций, значений `OperationInitiatorType`, взаимосвязь с другими атрибутами и типами терминалов:

| Тип операции и инициатор                                     | Описание                                                                                                                                                                                                                                                                            | Сценарий карточной операции                                        | OperationInitiatorType | RebillId в [Charge](#tag/Rekurrentnyj-platezh/operation/ChargePCI) | Recurrent в [Init](#tag/Standartnyj-platezh/operation/Init) | Обязательность CVP2/CVV             | Обязательность аутентификации (3DS) |
|--------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------|------------------------|--------------------------------------------------------------------|-------------------------------------------------------------|-------------------------------------|-------------------------------------|
|Сustomer Initiated Credential-Not-Captured (CIT CNC)          | Оплата, инициированная покупателем, без сохранения реквизитов карты для последующих платежей.                                                                                                                                                                                       | Стандартный платеж                                                 |0                       |null                                                                | N                                                           |Обязателен, если не используется 3DS |Обязательна, если нет CVP2/CVV       |
|Сustomer Initiated Credential-Captured (CIT CC)               | Оплата, инициированная покупателем, с сохранением реквизитов карты для последующих платежей.                                                                                                                                                                                        | Стандартный платеж с созданием родительского рекуррентного платежа |1                       |null                                                                | Y                                                           |Обязателен, если не используется 3DS |Обязательна, если нет CVP2/CVV       |
|Сustomer Initiated Credential-on-File (CIT COF)               | Оплата, инициированная покупателем, по сохраненным реквизитам карты (ранее была проведена операция с сохранением реквизитов CIT CC).                                                                                                                                                | Рекуррентный платеж, инициированный покупателем                    |2                       |not null                                                            | N                                                           |Нет                                  |Нет                                  |
|Merchant Initiated Credential-on-File, Recurring (MIT COF R)  | Повторяющиеся платежи, инициированные магазином, **без графика** (ранее была проведена операция с сохранением реквизитов CIT CC). Применяется для оплаты коммунальных услуг, услуг связи и т.д. Сумма может быть определена заранее или непосредственно перед оплатой.              | Рекуррентный платеж, инициированный магазином                      |R                       |not null                                                            | N                                                           |Нет                                  |Нет                                  |
|Merchant Credential-on-File, Installment (MIT COF I)          | Повторяющиеся платежи, инициированные магазином, **по графику** (ранее была проведена операция с сохранением реквизитов CIT CC). Применяется для платежей в рассрочку, оплаты страховки в рассрочку и т.д. График платежей должен быть известен плательщику до проведения операции. | Рекуррентный платеж, инициированный магазином                      |I                       |not null                                                            | N                                                           |Нет                                  |Нет                                  |

# Термины
| **Термин**                  | Определение                                                                                                                                                                                                                                                                               |
|-----------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **Клиент**                  | Физлицо, производящее перевод с использованием банковской карты на сайте мерчанта.                                                                                                                                                                                                        |
| **Мерчант**                 | Бизнес, принимающий и осуществляющий переводы по банковским картам на своем сайте.                                                                                                                                                                                                        |
| **Т‑Бизнес**                 | Сервис, помогающий проводить выплату клиенту-физлицу.                                                                                                                                                                                                                                     |
| **Эмитент**                 | Банк, выпустивший карту клиента-физлица.                                                                                                                                                                                                                                                  |
| **PCI DSS**                 | Международный стандарт безопасности, созданный для защиты данных банковских карт.                                                                                                                                                                                                         |
| **3-D Secure**              | Протокол, который используется как дополнительный уровень безопасности для онлайн-кредитных и дебетовых карт. 3-D Secure добавляет ещё один шаг аутентификации для онлайн-платежей.                                                                                                       |
| **Терминал**                | Точка приема платежей мерчанта. В общем случае привязывается к сайту, на котором осуществляется прием платежей. Далее в этой документации описан протокол для терминала мерчанта. <br> Для проведения тестов используются данные тестового терминала TinkoffBankTest — пароль аналогичен. |
| **ККМ**                     | Контрольно-кассовая машина.                                                                                                                                                                                                                                                               |
| **Личный кабинет мерчанта** | [Веб-приложение](https://business.tbank.ru/oplata/main), в котором мерчант управляет интернет-эквайрингом — настраивает параметры терминалов, подтверждает или отменяет платежи, анализирует статистику.                                                                                  |


# Параметры терминала

Каждый терминал обладает свойствами, которые влияют на те или иные аспекты приёма платежей. Эти свойства настраиваются при
подключении интернет-эквайринга и могут быть изменены в личном кабинете мерчанта.

Основные параметры приёма платежей для терминала:

| Название параметра                        | Формат                                | Описание                                                                                                                                                                                                                                                                    |
|-------------------------------------------|---------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| TerminalKey                               | 20 символов, чувствительно к регистру | Уникальный символьный ключ терминала. Устанавливается в Т‑Бизнес.                                                                                                                                                                                                             |
| Success URL                               | 250 символов, чувствительно к регистру | URL на веб-сайте мерчанта, куда будет переведен клиент в случае успешной оплаты <br> • true — платеж завершился успешно; <br> • false — платеж не завершился. *                                                                                                             |
| Fail URL                                  | 250 символов, чувствительно к регистру | URL на веб-сайте мерчанта, куда будет переведен клиент в случае неуспешной оплаты. *                                                                                                                                                                                        |
| Success Add Card URL                      | 250 символов, чувствительно к регистру | URL на веб-сайте мерчанта, куда будет переведен клиент после успешной привязки карты. *                                                                                                                                                                                     |
| Fail Add Card URL                         | 250 символов, чувствительно к регистру | URL на веб-сайте мерчанта, куда будет переведен клиент после неуспешной привязки карты. *                                                                                                                                                                                   |
| Notification URL                          | 250 символов, чувствительно к регистру | URL на веб-сайте мерчанта, куда будет отправлен POST запрос о статусе выполнения вызываемых методов. Только для методов **Authorize**, [FinishAuthorize](#tag/Standartnyj-platezh/operation/FinishAuthorize), [Confirm](#tag/Dvuhstadijnyj-platezh/operation/Confirm), [Cancel](#tag/Otmena-platezha/operation/Cancel). |
| Валюта терминала                          | 3 символа                             | Валюта, в которой будут происходить списания по данному терминалу, если иное не передано в запросе.                                                                                                                                                                         |
| Активность терминала                      | Рабочий/Неактивный/Тестовый           | Определяет режим работы данного терминала.                                                                                                                                                                                                                                  |
| Password                                  | 20 символов, чувствительно к регистру | Используется для подписи запросов/ответов. Является секретной информацией, известной только мерчанту и Т‑Бизнес. Пароль находится в [личном кабинете](https://business.tbank.ru/oplata/main) мерчанта.                                                                       |
| Отправлять нотификацию на FinishAuthorize | Да/Нет                                | Определяет, будет ли отправлена нотификация на выполнение метода [FinishAuthorize](#tag/Standartnyj-platezh/operation/FinishAuthorize). По умолчанию — да.                                                                                                                  |
| Отправлять нотификацию на Completed       | Да/Нет                                | Определяет, будет ли отправлена нотификация на выполнение метода [AttachCard](#tag/Metody-raboty-s-kartami/operation/AttachCard). По умолчанию — да.                                                                                                                        |
| Отправлять нотификацию на Reversed        | Да/Нет                                | Определяет, будет ли отправлена нотификация на выполнение метода [Cancel](#tag/Otmena-platezha/operation/Cancel). По умолчанию — да.                                                                                                                                        |

\* В URL можно указать нужные параметры в виде ${<параметр>}, которые будут переданы на URL через метод **GET**.

# Токен

Перед выполнением запроса MAPI проверяет, можно ли доверять его инициатору. Для этого сервер проверяет подпись запроса. 
В MAPI используется механизм подписи с помощью токена. Мерчант должен добавлять токен к каждому запросу, где это требуется. 

> В описании входных параметров для каждого метода мы указали, нужно подписывать запрос или нет. Токен формируется на 
основании тех полей, которые есть в запросе, поэтому токены для каждого запроса уникальные и никогда не совпадают.

**Токен** в MAPI — это строка, в которой мерчант зашифровал данные своего запроса с помощью пароля. Для создания токена 
мерчант использует пароль из личного кабинета мерчанта.

Пример процесса шифрования тела запроса для метода [Init](#tag/Standartnyj-platezh/operation/Init):

```json
{
  "TerminalKey": "MerchantTerminalKey",
  "Amount": 19200,
  "OrderId": "21090",
  "Description": "Подарочная карта на 1000 рублей",
  "Token": "68711168852240a2f34b6a8b19d2cfbd296c7d2a6dff8b23eda6278985959346",
  "DATA": {
    "Phone": "+7*********0",
    "Email": "<EMAIL>"
  },
  "Receipt": {
    "Email": "<EMAIL>",
    "Phone": "+***********",
    "Taxation": "osn",
    "Items": [
      {
        "Name": "Наименование товара 1",
        "Price": 10000,
        "Quantity": 1,
        "Amount": 10000,
        "Tax": "vat10",
        "Ean13": "303130323930303030630333435"
      },
      {
        "Name": "Наименование товара 2",
        "Price": 3500,
        "Quantity": 2,
        "Amount": 7000,
        "Tax": "vat20"
      },
      {
        "Name": "Наименование товара 3",
        "Price": 550,
        "Quantity": 4,
        "Amount": 4200,
        "Tax": "vat10"
      }
    ]
  }
}
```

Чтобы зашифровать данные запроса, мерчанту нужно:

1. Собрать массив передаваемых данных в виде пар ключ-значения. В массив нужно добавить только параметры корневого объекта. 
Вложенные объекты и массивы не участвуют в расчете токена. В примере в массив включены параметры `TerminalKey`, `Amount`, `OrderId`, `Description`  и исключен объект `Receipt` и `DATA`.

``` JSON
[{"TerminalKey": "MerchantTerminalKey"},{"Amount": "19200"},{"OrderId": "21090"},{"Description": "Подарочная карта на 1000 рублей"}]
```

2. Добавить в массив пару {`Password`, Значение пароля}. Пароль можно найти в личном кабинете мерчанта.

``` JSON
[{"TerminalKey": "MerchantTerminalKey"},{"Amount": "19200"},{"OrderId": "21090"},{"Description": "Подарочная карта на 1000 рублей"},{"Password": "usaf8fw8fsw21g"}]
```

3. Отсортировать массив по алфавиту по ключу.

```JSON
[{"Amount": "19200"},{"Description": "Подарочная карта на 1000 рублей"},{"OrderId": "21090"},{"Password": "usaf8fw8fsw21g"},{"TerminalKey": "MerchantTerminalKey"}]
```

4. Конкатенировать только значения пар в одну строку.

```JSON
"19200Подарочная карта на 1000 рублей21090usaf8fw8fsw21gMerchantTerminalKey"
```

5. Применить к строке хеш-функцию SHA-256 (с поддержкой UTF-8).

```JSON
"0024a00af7c350a3a67ca168ce06502aa72772456662e38696d48b56ee9c97d9"
```

6. Добавить получившийся результат в значение параметра `Token` в тело запроса и отправить запрос.

```JSON
{
  "TerminalKey": "MerchantTerminalKey",
  "Amount": 19200,
  "OrderId": "21090",
  "Description": "Подарочная карта на 1000 рублей",
  "DATA": {
    "Phone": "+7*********0",
    "Email": "<EMAIL>"
  },
  "Receipt": {
    "Email": "<EMAIL>",
    "Phone": "+***********",
    "Taxation": "osn",
    "Items": [
      {
        "Name": "Наименование товара 1",
        "Price": 10000,
        "Quantity": 1,
        "Amount": 10000,
        "Tax": "vat10",
        "Ean13": "303130323930303030630333435"
      },
      {
        "Name": "Наименование товара 2",
        "Price": 20000,
        "Quantity": 2,
        "Amount": 40000,
        "Tax": "vat20"
      },
      {
        "Name": "Наименование товара 3",
        "Price": 30000,
        "Quantity": 3,
        "Amount": 90000,
        "Tax": "vat10"
      }
    ]
  },
  "Token": "0024a00af7c350a3a67ca168ce06502aa72772456662e38696d48b56ee9c97d9"
}
```

> Информацию о корректности токена также можно проверить в личном кабинете интернет-эквайринга в разделе **Операции**.
Выберите нужный заказ → **Дополнительная информация о заказе** → поле **inittokenisvalid**. Если значение в этом поле
`true` — токен валидный, `false` — некорректный.

## Path Table

| Method | Path | Description |
| --- | --- | --- |
| POST | [/v2/Init](#postv2init) | Инициировать платеж |
| POST | [/v2/Check3dsVersion](#postv2check3dsversion) | Проверить версию 3DS |
| POST | [/v2/3DSMethod](#postv23dsmethod) | Прохождение этапа “3DS Method” |
| POST | [/v2/FinishAuthorize](#postv2finishauthorize) | Подтвердить платеж |
| POST | [/v2/AddCard](#postv2addcard) | Инициировать привязку карты к клиенту |
| POST | [/v2/AttachCard](#postv2attachcard) | Привязать карту |
| POST | [/v2/ACSUrl](#postv2acsurl) | Запрос в банк-эмитент для прохождения 3DS |
| POST | [/v2/Confirm](#postv2confirm) | Подтвердить платеж |
| POST | [/v2/Cancel](#postv2cancel) | Отменить платеж |
| POST | [/v2/Charge](#postv2charge) | Автоплатеж |
| POST | [/v2/GetState](#postv2getstate) | Получить статуса платежа |
| POST | [/v2/AddCustomer](#postv2addcustomer) | Зарегистрировать клиента |
| POST | [/v2/GetCustomer](#postv2getcustomer) | Получить данные клиента |
| POST | [/v2/RemoveCustomer](#postv2removecustomer) | Удалить данные клиента |
| POST | [/v2/GetAddCardState](#postv2getaddcardstate) | Получить статус привязки карты |
| POST | [/v2/GetCardList](#postv2getcardlist) | Получить список карт клиента |
| POST | [/v2/RemoveCard](#postv2removecard) | Удалить привязанную карту клиента |
| SERVERS | [/v2/GetQr](#serversv2getqr) |  |
| POST | [/v2/GetQr](#postv2getqr) | Сформировать QR |
| POST | [/v2/SubmitRandomAmount](#postv2submitrandomamount) | SubmitRandomAmount |
| POST | [/v2/Submit3DSAuthorization](#postv2submit3dsauthorization) | Подтвердить прохождение 3DS v1.0 |
| POST | [/v2/Submit3DSAuthorizationV2](#postv2submit3dsauthorizationv2) | Подтвердить прохождение 3DS v2.1 |
| GET | [/v2/TinkoffPay/terminals/{TerminalKey}/status](#getv2tinkoffpayterminalsterminalkeystatus) | Определить возможность проведения платежа |
| GET | [/v2/TinkoffPay/transactions/{paymentId}/versions/{version}/link](#getv2tinkoffpaytransactionspaymentidversionsversionlink) | Получить ссылку |
| GET | [/v2/TinkoffPay/{paymentId}/QR](#getv2tinkoffpaypaymentidqr) | Получить QR |
| GET | [/v2/SberPay/{paymentId}/QR](#getv2sberpaypaymentidqr) | Получить QR |
| GET | [/v2/SberPay/transactions/{paymentId}/link](#getv2sberpaytransactionspaymentidlink) | Получить ссылку |
| SERVERS | [/v2/QrMembersList](#serversv2qrmemberslist) |  |
| POST | [/v2/QrMembersList](#postv2qrmemberslist) | Получить список банков-пользователей QR |
| SERVERS | [/v2/AddAccountQr](#serversv2addaccountqr) |  |
| POST | [/v2/AddAccountQr](#postv2addaccountqr) | Привязать счёт к магазину |
| SERVERS | [/v2/GetAddAccountQrState](#serversv2getaddaccountqrstate) |  |
| POST | [/v2/GetAddAccountQrState](#postv2getaddaccountqrstate) | Получить статус привязки счета к магазину |
| SERVERS | [/v2/GetAccountQrList](#serversv2getaccountqrlist) |  |
| POST | [/v2/GetAccountQrList](#postv2getaccountqrlist) | Получить список счетов, привязанных к магазину |
| SERVERS | [/v2/ChargeQr](#serversv2chargeqr) |  |
| POST | [/v2/ChargeQr](#postv2chargeqr) | Автоплатеж по QR |
| SERVERS | [/v2/SbpPayTest](#serversv2sbppaytest) |  |
| POST | [/v2/SbpPayTest](#postv2sbppaytest) | Создать тестовую платежную сессию |
| SERVERS | [/v2/GetQrState](#serversv2getqrstate) |  |
| POST | [/v2/GetQrState](#postv2getqrstate) | Получить статус возврата |
| POST | [/v2/CheckOrder](#postv2checkorder) | Получить статус заказа |
| POST | [/cashbox/SendClosingReceipt](#postcashboxsendclosingreceipt) | Отправить закрывающий чек в кассу |
| POST | [/v2/Notification](#postv2notification) | Уведомления |
| POST | [/v2/MirPay/GetDeepLink](#postv2mirpaygetdeeplink) | Получить DeepLink |
| GET | [/v2/GetTerminalPayMethods](#getv2getterminalpaymethods) | Проверить доступность методов на SDK |
| POST | [/v2/getConfirmOperation](#postv2getconfirmoperation) | Получить справку по операции |

## Reference Table

| Name | Path | Description |
| --- | --- | --- |
| Common | [#/components/schemas/Common](#componentsschemascommon) |  |
| T-Pay | [#/components/schemas/T-Pay](#componentsschemast-pay) |  |
| LongPay1 | [#/components/schemas/LongPay1](#componentsschemaslongpay1) |  |
| LongPay2 | [#/components/schemas/LongPay2](#componentsschemaslongpay2) | `%` — порядковый номер пассажира от 1 до 4.
 |
| LongPay3 | [#/components/schemas/LongPay3](#componentsschemaslongpay3) | \`#` — порядковый номер пассажира от 1 до 4. |
| LongPay | [#/components/schemas/LongPay](#componentsschemaslongpay) | Расширенный набор параметров авиабилета передается при создании платежа (метод **Init**) в параметре `DATA`. |
| AgentData | [#/components/schemas/AgentData](#componentsschemasagentdata) | Данные агента. Обязателен, если используется агентская схема.
 |
| SupplierInfo | [#/components/schemas/SupplierInfo](#componentsschemassupplierinfo) | Данные поставщика платежного агента. 
Обязателен, если передается значение `AgentSign` в объекте `AgentData`.
 |
| Items_FFD_105 | [#/components/schemas/Items_FFD_105](#componentsschemasitems_ffd_105) |  |
| Payments | [#/components/schemas/Payments](#componentsschemaspayments) | Детали платежа.


Если объект не передан, автоматически указывается итоговая сумма чека с видом оплаты «Безналичный». 
 
Если передан объект `receipt.Payments`, значение в `Electronic` должно быть равно итоговому значению `Amount` в методе **Init**.
При этом сумма введенных значений по всем видам оплат, включая `Electronic`, должна быть равна сумме (**Amount**) всех товаров,
переданных в объекте `receipt.Items`.
 |
| Receipt_FFD_105 | [#/components/schemas/Receipt_FFD_105](#componentsschemasreceipt_ffd_105) | Объект с информацией о видах суммы платежа. Если объект не передан, автоматически указывается итоговая сумма чека с видом оплаты «Безналичная». |
| ClientInfo | [#/components/schemas/ClientInfo](#componentsschemasclientinfo) | Информация по клиенту.
 |
| MarkCode | [#/components/schemas/MarkCode](#componentsschemasmarkcode) | Код маркировки в машиночитаемой форме,
представленный в виде одного из видов кодов,
формируемых в соответствии с требованиями,
предусмотренными правилами, для нанесения
на потребительскую упаковку, или на товары,
или на товарный ярлык


Включается в чек, если предметом расчета является товар, подлежащий обязательной маркировке средством идентификации — соответствующий 
код в поле `paymentObject`.
 |
| MarkQuantity | [#/components/schemas/MarkQuantity](#componentsschemasmarkquantity) | Реквизит «дробное количество маркированного товара».
Передается, только если расчет осуществляется 
за маркированный товар — соответствующий код в поле 
`paymentObject`, и значение в поле `measurementUnit` 
равно `0`.

`MarkQuantity` не является обязательным объектом, в том числе для товаров с маркировкой. Этот объект можно передавать, 
 если товар с маркировкой. То есть даже при ФФД 1.2 этот объект не является обязательным.
 

Пример: 
```
      {
      "numenator": "1"
      "denominator" "2"  
      }
```
 |
| SectoralItemProps | [#/components/schemas/SectoralItemProps](#componentsschemassectoralitemprops) | Отраслевой реквизит предмета расчета. Указывается только для товаров подлежащих обязательной маркировке средством
идентификации. Включение этого реквизита предусмотрено НПА отраслевого регулирования для
соответствующей товарной группы.
 |
| Items_FFD_12 | [#/components/schemas/Items_FFD_12](#componentsschemasitems_ffd_12) |  |
| Receipt_FFD_12 | [#/components/schemas/Receipt_FFD_12](#componentsschemasreceipt_ffd_12) | Объект с информацией о видах суммы платежа. Если объект не передан, автоматически указывается итоговая сумма чека с видом оплаты «Безналичная». |
| Shops | [#/components/schemas/Shops](#componentsschemasshops) | JSON-объект с данными маркетплейса. Обязательный для маркетплейсов. |
| Init_FULL | [#/components/schemas/Init_FULL](#componentsschemasinit_full) |  |
| Response | [#/components/schemas/Response](#componentsschemasresponse) |  |
| 3DSMethod | [#/components/schemas/3DSMethod](#componentsschemas3dsmethod) |  |
| 3DSMethod-2 | [#/components/schemas/3DSMethod-2](#componentsschemas3dsmethod-2) |  |
| 3DSv2 | [#/components/schemas/3DSv2](#componentsschemas3dsv2) |  |
| FinishAuthorize_FULL | [#/components/schemas/FinishAuthorize_FULL](#componentsschemasfinishauthorize_full) |  |
| FinishAuthorize | [#/components/schemas/FinishAuthorize](#componentsschemasfinishauthorize) |  |
| Without3DS | [#/components/schemas/Without3DS](#componentsschemaswithout3ds) |  |
| With3DS | [#/components/schemas/With3DS](#componentsschemaswith3ds) |  |
| With3DSv2APP | [#/components/schemas/With3DSv2APP](#componentsschemaswith3dsv2app) |  |
| With3DSv2BRW | [#/components/schemas/With3DSv2BRW](#componentsschemaswith3dsv2brw) |  |
| AddCard_FULL | [#/components/schemas/AddCard_FULL](#componentsschemasaddcard_full) |  |
| AddCardResponse_FULL | [#/components/schemas/AddCardResponse_FULL](#componentsschemasaddcardresponse_full) |  |
| AttachCard | [#/components/schemas/AttachCard](#componentsschemasattachcard) |  |
| AttachCardResponse | [#/components/schemas/AttachCardResponse](#componentsschemasattachcardresponse) |  |
| ACSUrl_V1 | [#/components/schemas/ACSUrl_V1](#componentsschemasacsurl_v1) |  |
| ACSUrl_V2 | [#/components/schemas/ACSUrl_V2](#componentsschemasacsurl_v2) |  |
| ACSUrlResponseV1 | [#/components/schemas/ACSUrlResponseV1](#componentsschemasacsurlresponsev1) |  |
| ACSUrlResponseV2 | [#/components/schemas/ACSUrlResponseV2](#componentsschemasacsurlresponsev2) |  |
| Confirm | [#/components/schemas/Confirm](#componentsschemasconfirm) |  |
| Items_Params | [#/components/schemas/Items_Params](#componentsschemasitems_params) | Информация по способу оплаты или деталям для платежей в рассрочку. |
| Confirm-2 | [#/components/schemas/Confirm-2](#componentsschemasconfirm-2) |  |
| ShopsCancel | [#/components/schemas/ShopsCancel](#componentsschemasshopscancel) | JSON-объект с данными маркетплейса. Обязательный для маркетплейсов. |
| Cancel | [#/components/schemas/Cancel](#componentsschemascancel) |  |
| Cancel-2 | [#/components/schemas/Cancel-2](#componentsschemascancel-2) |  |
| Charge_FULL | [#/components/schemas/Charge_FULL](#componentsschemascharge_full) |  |
| GetState_FULL | [#/components/schemas/GetState_FULL](#componentsschemasgetstate_full) |  |
| AddCustomer | [#/components/schemas/AddCustomer](#componentsschemasaddcustomer) |  |
| AddCustomerResponse | [#/components/schemas/AddCustomerResponse](#componentsschemasaddcustomerresponse) |  |
| GetOrRemoveCustomer | [#/components/schemas/GetOrRemoveCustomer](#componentsschemasgetorremovecustomer) |  |
| GetCustomerResponse | [#/components/schemas/GetCustomerResponse](#componentsschemasgetcustomerresponse) |  |
| RemoveCustomerResponse | [#/components/schemas/RemoveCustomerResponse](#componentsschemasremovecustomerresponse) |  |
| GetAddCardState | [#/components/schemas/GetAddCardState](#componentsschemasgetaddcardstate) |  |
| GetAddCardStateResponse | [#/components/schemas/GetAddCardStateResponse](#componentsschemasgetaddcardstateresponse) |  |
| GetCardList_FULL | [#/components/schemas/GetCardList_FULL](#componentsschemasgetcardlist_full) |  |
| RemoveCard | [#/components/schemas/RemoveCard](#componentsschemasremovecard) |  |
| RemoveCardResponse | [#/components/schemas/RemoveCardResponse](#componentsschemasremovecardresponse) |  |
| GetQr | [#/components/schemas/GetQr](#componentsschemasgetqr) |  |
| QrResponse_FULL | [#/components/schemas/QrResponse_FULL](#componentsschemasqrresponse_full) |  |
| Member | [#/components/schemas/Member](#componentsschemasmember) |  |
| AddAccountQr | [#/components/schemas/AddAccountQr](#componentsschemasaddaccountqr) |  |
| AddAccountQrResponse | [#/components/schemas/AddAccountQrResponse](#componentsschemasaddaccountqrresponse) |  |
| GetAddAccountQrState | [#/components/schemas/GetAddAccountQrState](#componentsschemasgetaddaccountqrstate) |  |
| GetAddAccountQrStateResponse | [#/components/schemas/GetAddAccountQrStateResponse](#componentsschemasgetaddaccountqrstateresponse) |  |
| GetAccountQrList | [#/components/schemas/GetAccountQrList](#componentsschemasgetaccountqrlist) |  |
| GetAccountQrListResponse | [#/components/schemas/GetAccountQrListResponse](#componentsschemasgetaccountqrlistresponse) |  |
| ChargeQr | [#/components/schemas/ChargeQr](#componentsschemaschargeqr) |  |
| ChargeQrResponse | [#/components/schemas/ChargeQrResponse](#componentsschemaschargeqrresponse) |  |
| SbpPayTest | [#/components/schemas/SbpPayTest](#componentsschemassbppaytest) |  |
| SbpPayTestResponse | [#/components/schemas/SbpPayTestResponse](#componentsschemassbppaytestresponse) |  |
| GetQRStateResponse_FULL | [#/components/schemas/GetQRStateResponse_FULL](#componentsschemasgetqrstateresponse_full) |  |
| CheckOrder | [#/components/schemas/CheckOrder](#componentsschemascheckorder) |  |
| PaymentsCheckOrder | [#/components/schemas/PaymentsCheckOrder](#componentsschemaspaymentscheckorder) | Детали |
| CheckOrder-2 | [#/components/schemas/CheckOrder-2](#componentsschemascheckorder-2) |  |
| SendClosingReceipt | [#/components/schemas/SendClosingReceipt](#componentsschemassendclosingreceipt) |  |
| SendClosingReceipt-2 | [#/components/schemas/SendClosingReceipt-2](#componentsschemassendclosingreceipt-2) |  |
| DataNotification | [#/components/schemas/DataNotification](#componentsschemasdatanotification) | Дополнительные параметры платежа, переданные при создании заказа. Являются обязательными для платежей в рассрочку. В ответе параметр приходит в формате <code>Data</code> — не полностью в верхнем регистре. |
| NotificationPayment | [#/components/schemas/NotificationPayment](#componentsschemasnotificationpayment) | **Уведомление о платеже**
 |
| NotificationAddCard | [#/components/schemas/NotificationAddCard](#componentsschemasnotificationaddcard) | **Уведомления о привязке**

Уведомления магазину о статусе выполнения метода привязки карты — **AttachCard**.
После успешного выполнения метода **AttachCard** Т‑Бизнес отправляет POST-запрос с информацией о привязке карты.
Уведомление отправляется на ресурс мерчанта на адрес `Notification URL` синхронно и ожидает ответа в течение 10 секунд. 
После получения ответа или не получения его за заданное время сервис переадресует клиента на `Success AddCard URL` 
или `Fail AddCard URL` — в зависимости от результата привязки карты.
В случае успешной обработки нотификации мерчант должен вернуть ответ с телом сообщения `OK` — без тегов, заглавными английскими буквами.

Если тело сообщения отлично от `OK`, любая нотификация считается неуспешной, и сервис будет повторно отправлять
нотификацию раз в час в течение 24 часов. Если за это время нотификация так и не доставлена, она складывается в дамп.
 |
| Receipt_FFD_12-2 | [#/components/schemas/Receipt_FFD_12-2](#componentsschemasreceipt_ffd_12-2) | Объект с информацией о видах суммы платежа |
| Receipt_FFD_105-2 | [#/components/schemas/Receipt_FFD_105-2](#componentsschemasreceipt_ffd_105-2) | Объект с информацией о видах суммы платежа. |
| NotificationFiscalization | [#/components/schemas/NotificationFiscalization](#componentsschemasnotificationfiscalization) | **Уведомление о фискализации**

Если используется подключенная онлайн касса, по результату фискализации будет
отправлено уведомление с фискальными данными.
 |
| NotificationQr | [#/components/schemas/NotificationQr](#componentsschemasnotificationqr) | **Уведомление о статусе привязки счета по QR**

После привязки счета по QR магазину отправляется статус привязки и токен.
Уведомление будет приходить по статусам `ACTIVE` и `INACTIVE`.
 |
| GetDeepLink | [#/components/schemas/GetDeepLink](#componentsschemasgetdeeplink) |  |
| GetDeepLinkResponse | [#/components/schemas/GetDeepLinkResponse](#componentsschemasgetdeeplinkresponse) |  |
| GetTerminalPayMethods | [#/components/schemas/GetTerminalPayMethods](#componentsschemasgetterminalpaymethods) |  |
| Paymethod | [#/components/schemas/Paymethod](#componentsschemaspaymethod) | Перечень доступных методов оплаты |
| GetTerminalPayMethodsResponse | [#/components/schemas/GetTerminalPayMethodsResponse](#componentsschemasgetterminalpaymethodsresponse) |  |
| by_url | [#/components/schemas/by_url](#componentsschemasby_url) |  |
| by_email | [#/components/schemas/by_email](#componentsschemasby_email) |  |
| PaymentIdListForGCO | [#/components/schemas/PaymentIdListForGCO](#componentsschemaspaymentidlistforgco) | JSON-массив с объектами, содержащими информацию по запросу. |
| response_by_url | [#/components/schemas/response_by_url](#componentsschemasresponse_by_url) |  |
| response_by_email | [#/components/schemas/response_by_email](#componentsschemasresponse_by_email) |  |

## Path Details

***

### [POST]/v2/Init

- Summary  
Инициировать платеж

- Description  
Метод инициирует платежную сессию.  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала. <br>
  // Выдается мерчанту в Т‑Бизнес при заведении терминала.
  // 
  TerminalKey: string
  // * Сумма в копейках. Например, 3 руб. 12коп. — это число 312.
  // * Параметр должен быть равен сумме всех параметров `Amount`, переданных в объекте `Items`.
  // * Минимальная сумма операции с помощью СБП составляет 10 руб.
  // 
  Amount: number
  // Идентификатор заказа в системе мерчанта. Должен быть уникальным для каждой операции.
  OrderId: string
  // Подпись запроса.
  Token: string
  // Описание заказа.
  // Значение параметра будет отображено на платежной форме.
  //  
  //  
  // Для привязки и одновременной оплаты по СБП поле обязательное. При оплате через СБП эта информация
  // отобразится в мобильном банке клиента.
  // 
  Description?: string
  // Идентификатор клиента в системе мерчанта.
  // 
  // * Обязателен, если передан атрибут `Recurrent`.
  // * Если был передан в запросе, в нотификации будет указан `CustomerKey` и его `CardId`. Подробнее — в методе
  // [GetCardList](#tag/Metody-raboty-s-kartami/paths/~1GetCardList/post).
  // * Нужен для сохранения карт на платежной форме — платежи в один клик.
  // * Необязателен при рекуррентных платежах через СБП.
  // 
  CustomerKey?: string
  // Признак родительского рекуррентного платежа. Обязателен для регистрации автоплатежа. 
  // 
  // 
  // Если передается и установлен в `Y`, регистрирует платеж как рекуррентный. 
  // В этом случае после оплаты в нотификации на `AUTHORIZED` будет передан параметр `RebillId` для использования в методе **Charge**.
  // Для привязки и одновременной оплаты по CБП передавайте `Y`. 
  // 
  // Значение зависит от атрибутов:
  // 
  // * `OperationInitiatorType` — в методе `/init`,
  // * `Recurrent` — в методе `/Init`. 
  // 
  // 
  // Подробнее — в описании методов [Рекуррентный платёж](#tag/Rekurrentnyj-platyozh) и [Инициализация платежа](#tag/Standartnyj-platyozh/paths/~1Init/post).
  // 
  Recurrent?: string
  // Определяет тип проведения платежа — двухстадийная или одностадийная оплата:
  // 
  // * `O` — одностадийная оплата,
  // * `T` — двухстадийная оплата. 
  // 
  // 
  // Если параметр передан — используется его значение, если нет — значение из настроек терминала.
  // 
  PayType?: enum[O, T]
  // Язык платежной формы:
  // 
  // * `ru` — русский,
  // * `en` — английский. 
  // 
  // 
  // Если не передан, форма откроется на русском языке.
  // 
  Language?: string
  // URL на веб-сайте мерчанта, куда будет отправлен POST-запрос о статусе выполнения вызываемых методов — настраивается 
  // в личном кабинете. Если параметр:
  // 
  // * передан — используется его значение,
  // * не передан — значение из настроек терминала.
  // 
  NotificationURL?: string
  // URL на веб-сайте мерчанта, куда будет
  // переведен клиент в случае успешной оплаты — настраивается в личном кабинете. Если параметр:
  // * передан — используется его значение,
  // * не передан — значение из настроек терминала.
  // 
  SuccessURL?: string
  // URL на веб-сайте мерчанта, куда будет
  // переведен клиент в случае неуспешной
  // оплаты — настраивается в личном кабинете. Если параметр:
  // * передан — используется его значение,
  // * не передан — значение из настроек терминала.
  // 
  FailURL?: string
  // JSON-объект, который позволяет передавать дополнительные параметры по операции и задавать определенные настройки в 
  // формате `ключ:значение`.
  // 
  // Максимальная длина для каждого передаваемого параметра:
  //   * ключ — 20 знаков;
  //   * значение — 100 знаков.
  // 
  // Максимальное количество пар `ключ:значение` — 20.
  // 
  // 1. Если у терминала включена опция привязки клиента после 
  // успешной оплаты и передается параметр `CustomerKey`, в передаваемых 
  // параметрах `DATA` могут быть параметры метода **AddCustomer**. 
  // Если они есть, они автоматически привязываются к клиенту.
  // 
  // Например, если указать `"DATA":{"Phone":"+7*********0", "Email":"<EMAIL>"}`,
  // к клиенту автоматически будут привязаны данные электронной почты и телефон, 
  // и они будут возвращаться при вызове метода **GetCustomer**.
  // 
  // Для МСС 4814 обязательно передать значение в параметре `Phone`.
  // Требования по заполнению: 
  // 
  //   * минимум — 7 символов,
  //   * максимум — 20 символов,
  //   * разрешены только цифры, исключение — первый символ может быть `+`.
  // 
  // Для МСС 6051 и 6050 обязательно передавать параметр `account` — номер электронного кошелька, не должен превышать 30 символов. 
  // 
  // Пример: `"DATA": {"account":"*********"}`.
  // 
  // 2. Если используется функционал сохранения карт на платежной форме, 
  // при помощи опционального параметра `DefaultCard` можно задать, 
  // какая карта будет выбираться по умолчанию. 
  // 
  //     Возможные варианты:
  //     
  //     * Оставить платежную форму пустой. Пример:
  //     
  //       ```
  //       "DATA":{"DefaultCard":"none"}
  //       ```
  //     
  //     * Заполнить параметр данными передаваемой карты. В этом случае передается `CardId`. Пример:
  //     
  //       ```
  //        "DATA":{"DefaultCard":"894952"}
  //       ```
  //     
  //     * Заполнить параметр данными последней сохраненной карты. Применяется, если параметр `DefaultCard` не передан, 
  //     передан с некорректным значением или в значении `null`.
  //     По умолчанию возможность сохранение карт на платежной форме может быть отключена. Для активации обратитесь в 
  //     техническую поддержку.
  // 
  // 3. Если вы подключаете оплату через T‑Pay, то вы можете передавать параметры устройства, с которого будет осуществлен переход в объекте `Data`.
  // Пример:
  // 
  //   ```
  //   "DATA": {
  //     "TinkoffPayWeb": "true",
  //     "Device": "Desktop",
  //     "DeviceOs": "iOS",
  //     "DeviceWebView": "true",
  //     "DeviceBrowser": "Safari"
  //    }
  //   ```
  // 
  // Рекомендации для заполнения поля `Device`:
  // 
  //   * Mobile  — при оплате c мобильного устройства;
  //   * Desktop — при оплате c десктопного устройства.
  // 
  // Рекомендации для заполнения поля `DeviceOs`:
  // 
  //   * iOS,
  //   * Android,
  //   * macOS,
  //   * Windows,
  //   * Linux.
  // 
  // Рекомендации для заполнения поля `DeviceBrowser`:
  // 
  //   * Chrome,
  //   * Firefox,
  //   * JivoMobile,
  //   * Microsoft Edge,
  //   * Miui,
  //   * Opera,
  //   * Safari,
  //   * Samsung,
  //   * WebKit,
  //   * WeChat,
  //   * Yandex.
  // 
  // 4. Параметр `notificationEnableSource` позволяет отправлять нотификации, только если Source платежа входит в перечень
  // указанных в параметре — он также есть в параметрах сессии. Возможные значения — T‑Pay, sbpqr. Пример:
  // 
  //  ```
  //  notificationEnableSource=TinkoffPay
  //  ``` 
  // 
  // 5. Для привязки и одновременной оплаты по CБП передавайте параметр `QR = true`.
  // 
  // 6. При передаче в объекте `DATA` атрибута `OperationInitiatorType` учитывайте взаимосвязь его значений:
  // 
  //    * Со значением атрибута `Recurrent` в методе **/Init**.
  //    * Со значением атрибута `RebillId` в методе **/Charge**.
  //    * С типом терминала, который используется для проведения операций — ECOM/AFT.
  //   
  //   [Подробная таблица — передача признака инициатора операции](#section/Peredacha-priznaka-iniciatora-operacii)
  // 
  //   Если передавать значения атрибутов, которые не соответствуют таблице, MAPI вернет ошибку 1126 —
  //   несопоставимые значения `rebillId` или `Recurrent` с переданным значением `OperationInitiatorType`.
  // 
  DATA?: #/components/schemas/Common | #/components/schemas/T-Pay | #/components/schemas/LongPay
  // JSON-объект с данными чека. Обязателен, если подключена онлайн-касса.
  Receipt?: #/components/schemas/Receipt_FFD_105 | #/components/schemas/Receipt_FFD_12
  // JSON-объект с данными маркетплейса. Обязательный для маркетплейсов.
  Shops: {
    // Код магазина
    // 
    ShopCode: string
    // Cумма в копейках, которая относится к
    // указанному `ShopCode`.
    // 
    Amount: number
    // Наименование товара.
    // 
    Name?: string
    // Сумма комиссии в копейках, удерживаемая из
    // возмещения партнера в пользу маркетплейса.
    // Если не передано, используется комиссия,
    // указанная при регистрации.
    // 
    Fee?: string
  }[]
  // Динамический дескриптор точки.
  Descriptor?: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Сумма в копейках.
  // 
  Amount: number
  // Идентификатор заказа в системе мерчанта.
  // 
  OrderId: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Статус транзакции.
  // 
  Status: string
  // Идентификатор платежа в системе Т‑Бизнес.
  // 
  PaymentId: string
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Ссылка на платежную форму. Параметр возвращается только `для мерчантов без PCI DSS`.
  // 
  PaymentURL?: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
}
```

- Examples

  - InitResponseExample

```json
{
  "summary": "Пример ответа",
  "value": {
    "Success": true,
    "ErrorCode": "0",
    "TerminalKey": "TinkoffBankTest",
    "Status": "NEW",
    "PaymentId": "**********",
    "OrderId": "21090",
    "Amount": 140000,
    "PaymentURL": "https://securepay.tinkoff.ru/new/fU1ppgqa"
  }
}
```

***

### [POST]/v2/Check3dsVersion

- Summary  
Проверить версию 3DS

- Description  
`Для мерчантов c PCI DSS и собственной платежной формой`  
 <br><br> Метод возвращает версию протокола 3DS, по которому может аутентифицироваться карта.  
  
 При определении 3DS v2.1 возможно получение данных для прохождения дополнительного метода [3DS Method](#tag/Standartnyj-platezh/operation/3DSMethod). Он позволяет эмитенту собрать данные браузера клиента, что может быть полезно при принятии решения в пользу Frictionless Flow (аутентификация клиента без редиректа на страницу ACS).  


#### RequestBody

- application/json

```ts
{
  // Идентификатор платежа в системе Т‑Бизнес.
  // 
  PaymentId: string
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Зашифрованные данные карты в формате: 
  //   ```
  //   PAN=****************;ExpDate=0519;CardHolder=IVAN PETROV;CVV=111
  //   ```
  // 
  CardData: string
  // Подпись запроса.
  // 
  Token: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Версия протокола 3DS. 
  // Примеры:
  // * `1.0.0` — первая версия,
  // * `2.1.0` — вторая версия.
  // 
  Version: string
  // Уникальный идентификатор транзакции,
  // который генерируется 3DS-Server. Обязательный
  // параметр для 3DS второй версии.
  // 
  TdsServerTransID?: string
  // Дополнительный параметр для 3DS второй
  // версии, который позволяет пройти этап по
  // сбору данных браузера ACS-ом.
  // 
  ThreeDSMethodURL?: string
  // Платежная система карты.
  // 
  PaymentSystem: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
}
```

***

### [POST]/v2/3DSMethod

- Summary  
Прохождение этапа “3DS Method”

- Description  
`Для мерчантов c PCI DSS и собственной платежной формой`   
  
<br>Метод используется для сбора информации ACS-ом о девайсе. Обязателен, если для 3DSv2.1 в ответе метода [Check3dsVersion](#tag/Standartnyj-platezh/operation/Check3dsVersion) был получен параметр `ThreeDSMethodURL`.<br><br>Условия выполнения:<br>• В скрытом iframe на стороне браузера<br>• С таймаутом ожидания ответа на запрос - 10 секунд"  


- Security  

#### RequestBody

- application/x-www-form-urlencoded

```ts
{
  // JSON с параметрами threeDSMethodNotificationURL, threeDSServerTransID, **закодированный в формат base-64** 
  // 
  //   | Название параметра | Тип | Описание             |
  //  | --------------------- | ------------ | -------------------- |
  //  | threeDSMethodNotificationURL | string | Обратный адрес, на который будет отправлен запрос после прохождения threeDSMethod |
  //  | threeDSServerTransID | string | Идентификатор транзакции из ответа метода |
  // 
  //  
  threeDSMethodData: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор транзакции
  // 
  threeDSServerTransID: string
}
```

***

### [POST]/v2/FinishAuthorize

- Summary  
Подтвердить платеж

- Description  
`Для мерчантов c PCI DSS и собственной платежной формой`  
<br><br> Метод подтверждает платеж передачей реквизитов. При одностадийной оплате — списывает средства   
с карты клиента, при двухстадийной — блокирует указанную сумму. Используется, если у площадки есть сертификация PCI DSS и   
собственная платежная форма.  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала. <br>
  // Выдается мерчанту в Т‑Бизнес при заведении терминала.
  // 
  TerminalKey: string
  // Уникальный идентификатор транзакции в
  // системе Т‑Бизнес.
  // 
  PaymentId: string
  // Подпись запроса.
  // 
  Token: string
  // IP-адрес клиента. <br>
  // Передача адреса допускается в формате IPv4 и IPv6.
  // 
  // Обязательный параметр для 3DS второй
  // версии. DS платежной системы требует 
  // передавать данный адрес в полном формате, 
  // без каких-либо сокращений — 8 групп по 4 символа.
  // 
  // Этот формат регламентируется на уровне
  // спецификации EMVCo.<br>
  // 
  // Пример правильного адреса — `2011:0db8:85a3:0101:0101:8a2e:0370:7334`.
  // 
  // Пример неправильного адреса — `2a00:1fa1:c7da:9285:0:51:838b:1001`.
  // 
  IP?: string
  // * `true` — отправлять клиенту информацию об оплате на
  // почту;
  // * `false` — не отправлять.
  // 
  SendEmail?: boolean
  // Источник платежа.
  // Значение параметра зависит от параметра `Route`:
  // - `ACQ` — `cards`. Также поддерживается написание `Cards`.
  // - `MC` — `beeline`, `mts`, `tele2`, `megafon`.
  // - `EINV` — `einvoicing`.
  // - `WM` — `webmoney`.
  // 
  Source?: enum[cards, beeline, mts, tele2, megafon, einvoicing, webmoney]
  // JSON-объект, который содержит дополнительные
  // параметры в виде `ключ`:`значение`. Эти параметры будут переданы на страницу
  // оплаты, если она кастомизирована.
  // 
  // 
  // Максимальная длина для каждого передаваемого параметра:
  // * ключ — 20 знаков,
  // * значение — 100 знаков. 
  // 
  // 
  // Максимальное количество пар `ключ`:`значение` — не больше 20.
  // 
  DATA: {
  }
  // Электронная почта для отправки информации об оплате.
  // Обязателен при передаче `SendEmail`.
  // 
  InfoEmail?: string
  // Данные карты.
  // Используется и является обязательным только 
  // для ApplePay или GooglePay.
  // 
  EncryptedPaymentData?: string
  // Объект `CardData` собирается в виде списка `ключ`=`значение` c разделителем `;`.
  // Объект зашифровывается открытым ключом (X509 RSA 2048), и получившееся бинарное значение кодируется в `Base64`.
  // Открытый ключ генерируется в Т‑Бизнес и выдается при регистрации терминала.
  // Доступен в личном кабинете Интернет-эквайринга в разделе **Магазины** при изменении типа подключения на «Мобильное».
  // 
  // |Наименование|Тип данных| Обязательность | Описание                                                                                                                                           |
  // |---|---|----------------|----------------------------------------------------------------------------------------------------------------------------------------------------|
  // |PAN|Number| Да             | Номер карты.                                                                                                                                       |
  // |ExpDate| Number| Да             | Месяц и год срока действия карты в формате `MMYY`.                                                                                                 |
  // |CardHolder |String| Нет            | Имя и фамилия держателя карты — как на карте.                                                                                                      |
  // |CVV |String| Нет            | Код защиты с обратной стороны карты. Для платежей по Apple Pay с расшифровкой токена на своей стороне необязательный.                              |
  // |ECI |String | Нет            | Electronic Commerce Indicator. Индикатор, который показывает степень защиты, применяемую при предоставлении клиентом своих данных ТСП. |
  // |CAVV |String | Нет            | Cardholder Authentication Verification Value или Accountholder Authentication Value.                                                               |
  // 
  // 
  // Пример значения элемента формы `CardData`:
  // 
  // ```
  // PAN=****************;ExpDate=0519;CardHolder=IVAN PETROV;CVV=111
  // ```
  // 
  // Для MirPay, если интеграция с НСПК для получения платежного токена:
  // 1. Передавайте `Route=ACQ` и `Source= MirPay`.
  // 2. ПВ `DATA.transId` передавайте значение `transId`.
  // 3. В `DATA.tavv` передавайте значение `cav`.
  // 4. Передавайте параметр `CardData`:
  // 
  //     - **Pan** заполняйте `tan`,
  //     - **ExpDate** заполняйте `tem + tey`.<br>
  //    
  // Если мерчант интегрируется только с банком для проведения платежа по MirPay, 
  // метод не вызывается. Эквайер самостоятельно получает платежный токен и инициирует авторизацию
  // вместо мерчанта.<br> 
  // 
  // При получении **CAVV** в **CardData** оплата будет проводиться как оплата токеном — иначе прохождение 3DS будет
  // регулироваться стандартными настройками треминала или платежа.
  // 
  // Не используется и не является обязательным, если передается `EncryptedPaymentData`.
  CardData: string
  // Сумма в копейках.
  // 
  Amount?: number
  // Канал устройства.
  // Поддерживаются следующие
  // каналы:
  // * `01` = Application (APP),
  // * `02` = Browser (BRW) — используется по умолчанию, передавать параметр не требуется.
  // 
  deviceChannel?: string
  // Способ платежа.
  // Обязательный для ApplePay или GooglePay.
  // 
  Route?: enum[ACQ, MC, EINV, WM]
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  "oneOf": [
    {
      "$ref": "#/components/schemas/Without3DS"
    },
    {
      "$ref": "#/components/schemas/With3DS"
    },
    {
      "$ref": "#/components/schemas/With3DSv2APP"
    },
    {
      "$ref": "#/components/schemas/With3DSv2BRW"
    }
  ]
}
```

- Examples

  - Without3DS

```json
{
  "summary": "Without3DS",
  "value": {
    "TerminalKey": "TinkoffBankTest",
    "Amount": 100000,
    "OrderId": "21050",
    "Success": true,
    "Status": "NEW",
    "PaymentId": "13660",
    "ErrorCode": "0",
    "Message": "Неверные параметры",
    "Details": "0",
    "RebillId": "********",
    "CardId": "string"
  }
}
```

  - With3DS

```json
{
  "summary": "With3DS",
  "value": {
    "TerminalKey": "TinkoffBankTest",
    "Amount": 100000,
    "OrderId": "21050",
    "Success": true,
    "Status": "NEW",
    "PaymentId": "13660",
    "ErrorCode": "0",
    "Message": "Неверные параметры",
    "Details": "0",
    "RebillId": "********",
    "CardId": "string",
    "MD": "ACQT-*********",
    "PaReq": "eJxVUl1TwjAQ/CtM30s+KLTDHGHQwsiogFh09C2kp1RpC2nLh7/eBAtqnnYvN3ubvUD/kK4bO9RFkmc9hzWp08BM5XGSvfecRTRyA6cvIFppxP\\nARVaVRwD0WhXzHRhL3HMUU73itwKVtyl1Pcs8Nli3pymUQK+z2Sww6joDZYI5bAfUgYeY0OZAzNYparWRWCpBqezWeiDZnLe3BqSmkqMeh4PRy2p\\n02BfJThkymKCIsSiAnCCqvslIfhXEG5Eyg0muxKstN0SVkv983yyT7zN/emroiQOwlkF8js8qiwogdklg8rEfT5WK0jj6G7D4cepNo8TWNBmwSDXtAbAfEskTjkPk0\\noF6DeV3a6jLj8VQHmVoXglFTqTFs7IjBn4u/BTBZa7OK8yPODPCwyTM0HSbACwby6/f6xsaoSpNMMN89+uHdV/iUPz2nyat/uxrPXz5nuX/c2nBPTVYxMflwzthJ0hIgVobUeyP1yg469xW+AedOuuM=\\",
    "ACSUrl": "https://secure.tcsbank.ru/acs/auth/start.do"
  }
}
```

  - With3DSv2APP

```json
{
  "summary": "With3DSv2APP",
  "value": {
    "TerminalKey": "TinkoffBankTest",
    "Amount": 100000,
    "OrderId": "21050",
    "Success": true,
    "Status": "NEW",
    "PaymentId": "13660",
    "ErrorCode": "0",
    "Message": "Неверные параметры",
    "Details": "0",
    "RebillId": "********",
    "CardId": "string",
    "TdsServerTransId": "d93f7c66-3ecf-4d10-ba62-46046e7b7596",
    "AcsTransId": "aceca6af-56ee-43f0-80ef-ea8d30d5c5b0",
    "AcsInterface": "02",
    "AcsUiTemplate": "03",
    "AcsSignedContent": "eyJ4NWMiOlsiTUlJRGtUQ0NBbm1nQXdJQkFnSVVRU1VEV05VZEFicWozS1Uya0M0VHpaSEpVVHd3RFFZSktvWklodmNOQVFFTEJRQXdXREVMTUFrR0ExVUVCaE1DVWxVeER6QU5CZ05WQkFnTUJrMXZjMk52ZHpFUE1BMEdBMVVFQnd3R1RXOXpZMjkzTVJJ d0VBWURWUVFLREFsVGIyMWxJR0poYm1zeEV6QVJCZ05WQkFNTUNtUnpMbTF2WTJzdWNuVXdIaGNOTWpBd056RTRNVFExT1RNM1doY05NakV3TnpFNE1UUTFPVE0zV2pCWU1Rc3dDUVlEVlFRR0V3SlNWVEVQTUEwR0ExVUVDQXdHVFc5elkyOTNNUTh3RF FZRFZRUUhEQVpOYjNOamIzY3hFakFRQmdOVkJBb01DVk52YldVZ1ltRnVhekVUTUJFR0ExVUVBd3dLWkhNdWJXOWpheTV5ZFRDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTUhNdXB1Wlg3VUFWR3Z5dm9uZ1o4U3BJcisrRD RnMjBRaFwvZ0NGb3JUN1pDUkRaVWhRamlDSzdXSWpiVHRKQUFKVG1yelhcLzlMSGJIdHpIcFFvRFVTNXZPTnRqVWFaVGVQUE91SklMRWl6NDBBVjJCUVZRd0xnRzBjbm9oK21Qa0dNMEZ4VmJFcHFEVHk3SHB0dFAwdm96cGxHNjdFWk1HTXdKSUpESmlDYUdG OGZ0aTlYR3M4MXB3NUhWZElmOHNpQnFaWW94cGt0QWJ1dnpBTFJEUnp3dFBhclFHOTZyQStPM0dJaE53VDhZXC9pallwS0hWNkJCWDBKNmxZdFdoaVY5blhBVktYNTNlVTJ4M1E2Njh4U3BLa2dwSVh1N2xiNUN2M2dDTlIrelVqK0lTODNZYjJhUlR2WkF6MFI1 V3dBNW5Zb2J6V3Vta1wvdE5iV1FYdzBWTUNBd0VBQWFOVE1GRXdIUVlEVlIwT0JCWUVGRmVWN0dzR0tCSzhUTDljaVk4UFF2N0RhY290TUI4R0ExVWRJd1FZTUJhQUZGZVY3R3NHS0JLOFRMOWNpWThQUXY3RGFjb3RNQThHQTFVZEV3RUJcL3dRRk1BT UJBZjh3RFFZSktvWklodmNOQVFFTEJRQURnZ0VCQUZqVGppUkxKOFpaWld5dXFLNTZHVkR6dnJiXC9uRlVDTHVjVXZEV2toK09lRWkxWUFPOUJZV3RFVTVzdmRNNTlsOWVTMGtjbGxrRzVDTklcL1U4S2dKSnUzV0tEVXp5cU80eVRNU3g3RWZDXC9qVE1oT2d2Y UJubktWK2hvV3FQZTlKNHZVYzZ2R0wzWE1cL0FNeWpoVDlBRko1ZjZBaVdZMk5QYkxHczQ2N0ZPY2Vwb1RJMkdseHBtcWdaMFVGKzlsblNZbDU0WEg2dGNZYUszWjcxS2NES0I0QkUySWVmV1Y3MUM3anBVdjFFSlFsNTY4XC8xaGpsZktXUExWcE5NTzVlTlNMR 1ZKd1VmdFA0V0tKU2Y2VmdtbG5XOU1yVStiK3hvZW44MFF1dUxrSWs1ZXBIM2l1ZDV4a1IxcVVXQU1aTUZTQW4yUHJDdjQrZFFMRDd2OG83d3BrPSJdLCJhbGciOiJQUzI1NiJ9.eyJhY3NFcGhlbVB1YktleSI6eyJrdHkiOiJFQyIsImNydiI6IlAtMjU2IiwieCI6IlRoRj NJY3BIMVVLanliQW5lNWhHcy1BNnpyYXo2aUxiYVk0WmVEOU1oSU0iLCJ5Ijoid0VuVXNvNlRLZDlfbjZSc2NjUXRCeFc2Q1gzLXFSTGk0UWJBU3pNbm4tTSJ9LCJzZGtFcGhlbVB1YktleSI6eyJrdHkiOiJFQyIsImNydiI6IlAtMjU2IiwieCI6Ikp6R2tGM2w3WGxnclJ6NU 1PTl9ncDg3WUxfd0NkVVJpVUlxOXJmNnVyR2MiLCJ5IjoiTnI4UmllTE9vVzJXUkhiX2RFazFmdHRoWEZXTHdYaWZFUzNZZkFnMkhvWSJ9LCJhY3NVUkwiOiJodHRwczovL2VhY3EtZHMtbW9jay1zZXJ2aWNlLXRlc3QudGNzYmFuay5ydS9jaGFsbGVuZ2UvZDkzZjdj NjYtM2VjZi00ZDEwLWJhNjItNDYwNDZlN2I3NTk2In0.hQLVTT5YMAY8TjISRdYX2IT04zH8Z8DgoB4kIAyVfkuJ0X6AGIKXSVcIVSNgC-A_SEkCZRqAyUeu0ZJtpoIVyOf1mumBGEK-uC6yVQlX5WSPidQUj4nuBvpYsfdrGPeoHWvNsrBpMMxvvW4559jtbAUY00NcW3rwDShAi4gVKgJcssMPAM1zOOR5vi0_ClUsCW1k9a201Hv6cYcEBuO2JQ8NPLampEkZ55nOmwcPPTEziXeZsq9VjROXNfBewbA4wLuQmh8aSrcOcwFtJo0CPpdrsKiY77KPT0c8XMmZZK_FiAxzrWocfHraqC7cRJNQ5glEBakXvSfrwGg_xXA",
    "AcsReferenceNumber": "12345",
    "SdkTransID": "d5a44dfe-673b-4666-82f9-96346107e424"
  }
}
```

  - With3DSv2BRW

```json
{
  "summary": "With3DSv2BRW",
  "value": {
    "TerminalKey": "TinkoffBankTest",
    "Amount": 100000,
    "OrderId": "21050",
    "Success": true,
    "Status": "NEW",
    "PaymentId": "13660",
    "ErrorCode": "0",
    "Message": "Неверные параметры",
    "Details": "0",
    "RebillId": "********",
    "CardId": "string",
    "TdsServerTransId": "d7171a06-7159-4bdd-891a-a560fe9938d2",
    "AcsTransId": "e176d5d3-2f19-40f5-8234-46d3464e0b08",
    "ACSUrl": "https://acs.vendorcert.mirconnect.ru/mdpayacs/creq"
  }
}
```

***

### [POST]/v2/AddCard

- Summary  
Инициировать привязку карты к клиенту

- Description  
<br><br> Метод инициирует привязку карты к клиенту.   
При успешной привязке переадресует клиента на `Success Add Card URL`,   
при неуспешной — на `Fail Add Card URL`.   
Можно использовать форму Т‑Бизнес или заменить ее на кастомную.  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  // 
  CustomerKey: string
  // Подпись запроса.
  // 
  Token: string
  // Если `CheckType` не передается, автоматически проставляется значение `NO`.
  //  Возможные значения:
  //  * `NO` — сохранить карту без проверок. `RebillID` для рекуррентных платежей не возвращается.
  //  * `HOLD` — при сохранении сделать списание на 0 руб. `RebillID` возвращается для терминалов без
  //  поддержки 3DS.
  //  * `3DS` — при сохранении карты выполнить проверку 3DS и выполнить списание на 0 р. В этом случае `RebillID` будет только для
  //  3DS карт. Карты, не поддерживающие 3DS, привязаны не будут.
  //  * `3DSHOLD` – при привязке карты выполнить проверку, поддерживает карта 3DS или нет. Если карта не поддерживает 3DS, выполняется
  //  списание на 0 руб.
  // 
  CheckType?: enum[NO, HOLD, 3DS, 3DSHOLD]
  // IP-адрес запроса.
  // 
  IP?: string
  // Признак резидентности добавляемой карты:
  // Возможные значения:
  // * `true` — карта РФ,
  // * `false` — карта не РФ,
  // * `null` — не специфицируется, универсальная карта.
  // 
  ResidentState?: boolean
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор платежа в системе Т‑Бизнес.
  // 
  PaymentId: number
  // Идентификатор терминала. Выдается мерчанту Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  // 
  CustomerKey: string
  // Идентификатор запроса на привязку карты.
  // 
  RequestKey: string
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
  // UUID, используется для работы без PCI DSS.
  // 
  PaymentURL: string
}
```

- Examples

  - AddCardResponse_FULL

```json
{
  "summary": "Пример ответа (FULL)",
  "value": {
    "PaymentId": 6155312072,
    "TerminalKey": "1111133333",
    "CustomerKey": "906540",
    "RequestKey": "ed989549-d3be-4758-95c7-22647e03f9ec",
    "ErrorCode": "0",
    "Success": true,
    "Message": "Неверные параметры",
    "Details": "Терминал не найден",
    "PaymentURL": "82a31a62-6067-4ad8-b379-04bf13e37642d"
  }
}
```

***

### [POST]/v2/AttachCard

- Summary  
Привязать карту

- Description  
`Для мерчантов c PCI DSS и собственной платежной формой`  
  
 <br> Завершает привязку карты к клиенту.    
 В случае успешной привязки переадресует клиента на **Success Add Card URL**  
 в противном случае на **Fail Add Card URL**.    
 Для прохождения 3DS второй версии перед вызовом метода должен быть вызван [Check3dsVersion](#tag/Standartnyj-platezh/operation/Check3dsVersion)  
 и выполнен [3DS Method](#tag/Standartnyj-platezh/operation/3DSMethod), который является обязательным при прохождении 3DS по протоколу версии  
 2.0.  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Идентификатор запроса на привязку карты.
  // 
  RequestKey: string
  // Зашифрованные данные карты в формате: 
  //   ```
  //   PAN=****************;ExpDate=0519;CardHolder=IVAN PETROV;CVV=111
  //   ```
  // 
  CardData: string
  // В объекте передаются дополнительные параметры в формате `ключ:значение`.
  // Например, меняем на JSON-объект, который содержит дополнительные параметры в виде `ключ:значение`.
  // 
  // Если ключи или значения содержат в себе специальные символы, получившееся значение должно быть закодировано
  // функцией `urlencode`. Максимальная длина для каждого передаваемого параметра:
  // * ключ — 20 знаков,
  // * значение — 100 знаков. 
  // 
  // Максимальное количество пар `ключ:значение` — не больше 20.
  // 
  // >**Важно** Для 3DS второй версии в `DATA` передаются параметры, описанные в объекте
  // `3DSv2`. В `HttpHeaders` запроса обязательны заголовки `User-Agent` и `Accept`.
  // 
  DATA?: {
   } | #/components/schemas/3DSv2
  // Подпись запроса.
  // 
  Token: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Платежный ключ, выдается мерчанту при заведении
  // терминала.
  // 
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  // 
  CustomerKey: string
  // Идентификатор запроса на привязку карты.
  // 
  RequestKey: string
  // Идентификатор карты в системе Т‑Бизнес. <br>
  // При сценарии 3-D Secure Authentication Challenge — `CardId` можно получить после успешного прохождения 3DS.
  // 
  CardId?: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Статус привязки карты:
  // * `NEW` — новая сессия привязки карты;
  // * `FORM_SHOWED` — показ формы привязки карты;
  // * `THREE_DS_CHECKING` — отправка клиента на проверку 3DS;
  // * `THREE_DS_CHECKED` — клиент успешно прошел проверку 3DS;
  // * `AUTHORIZING` — отправка платежа на 0 руб;
  // * `AUTHORIZED` — платеж на 0 руб прошел успешно;
  // * `COMPLETED` — карта успешно привязана;
  // * `REJECTED` — привязать карту не удалось.
  // 
  Status?: enum[NEW, FORM_SHOWED, THREE_DS_CHECKING, THREE_DS_CHECKED, AUTHORIZING, AUTHORIZED, COMPLETED, REJECTED]
  // Идентификатор рекуррентного платежа.
  // 
  RebillId?: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
  // Адрес сервера управления доступом для проверки 3DS —
  // возвращается в ответе на статус `3DS_CHECKING`.
  // 
  ACSUrl?: string
  // Уникальный идентификатор транзакции в системе
  // Т‑Бизнес — возвращается в ответе на статус `3DS_CHECKING`.
  // 
  MD?: string
  // Результат аутентификации 3-D Secure — возвращается в ответе на статус `3DS_CHECKING`.
  // 
  PaReq?: string
}
```

- Examples

  - AttachCardRespV1

```json
{
  "summary": "Пример ответа 3DS v1.0",
  "value": {
    "TerminalKey": "testRegress",
    "CustomerKey": "testCustomerKey",
    "RequestKey": "8de92934-26c9-474c-a4ce-424f2021d24d",
    "CardId": "5555",
    "Success": true,
    "ErrorCode": "0",
    "Status": "NEW",
    "RebillId": "*********",
    "Message": "Неверные параметры",
    "Details": "string",
    "ACSUrl": "https://secure.tcsbank.ru/acs/auth/start.do",
    "MD": "ACQT-*********",
    "PaReq": "eJxVUl1TwjAQ/CtM30s+KLTDHGHQwsiogFh09C2kp1RpC2nLh7/eBAtqnnYvN3ubvUD/kK4bO9RFkmc9hzWp08BM5XGSvfecRT RyA6cvIFppxPARVaVRwD0WhXzHRhL3HMUU73itwKVtyl1Pcs8Nli3pymUQK+z2Sww6joDZYI5bAfUgYeY0OZAzNYparWRWCpBqe zWeiDZnLe3BqSmkqMeh4PRy2p02BfJThkymKCIsSiAnCCqvslIfhXEG5Eyg0muxKstN0SVkv983yyT7zN/emroiQOwlkF8js8qiwogdk lg8rEfT5WK0jj6G7D4cepNo8TWNBmwSDXtAbAfEskTjkPk0oF6DeV3a6jLj8VQHmVoXglFTqTFs7IjBn4u/BTBZa7OK8yPODPCwyT M0HSbACwby6/f6xsaoSpNMMN89+uHdV/iUPz2nyat/uxrPXz5nuX/c2nBPTVYxMflwzthJ0hIgVobUeyP1yg469xW+AedOuuM="
  }
}
```

  - AttachCardRespV2

```json
{
  "summary": "Пример ответа 3DS v2.1",
  "value": {
    "TerminalKey": "testRegress",
    "CustomerKey": "testCustomerKey",
    "RequestKey": "8de92934-26c9-474c-a4ce-424f2021d24d",
    "CardId": "5555",
    "Success": true,
    "ErrorCode": "0",
    "Status": "3DS_CHECKING",
    "RebillId": "*********",
    "Message": "Неверные параметры",
    "Details": "string",
    "ACSUrl": "https://acs.vendorcert.mirconnect.ru/mdpayacs/creq",
    "TdsServerTransId": "d7171a06-7159-4bdd-891a-a560fe9938d2",
    "AcsTransId": "e176d5d3-2f19-40f5-8234-46d3464e0b08"
  }
}
```

***

### [POST]/v2/ACSUrl

- Summary  
Запрос в банк-эмитент для прохождения 3DS

- Description  
`Для мерчантов c PCI DSS и собственной платежной формой`   
  
<br>`ACSUrl` возвращается в ответе метода [FinishAuthorize](#tag/Standartnyj-platezh/operation/FinishAuthorize).<br>Если в ответе метода [FinishAuthorize](#tag/Standartnyj-platezh/operation/FinishAuthorize) возвращается статус `3DS_CHECKING`, то мерчанту необходимо сформировать запрос на URL ACS банка, выпустившего карту (параметр `ACSUrl`).<br>  
**Для 3DS v2.1**: Компонент ACS использует пары сообщений `CReq` и `CRes` для выполнения проверки (Challenge). В ответ на полученное сообщение `CReq` компонент ACS формирует сообщение `CRes`, которое запрашивает держателя карты ввести данные для аутентификации. <br> <br> **Формат ответа:** `Cres`, полученный по `NotificationUrl` из запроса [FinishAuthorize](#tag/Standartnyj-platezh/operation/FinishAuthorize). <br>  
При успешном результате прохождения 3-D Secure подтверждается инициированный платеж с помощью методов [Submit3DSAuthorization](#tag/Prohozhdenie-3DS/operation/Submit3DSAuthorization) или [Submit3DSAuthorizationV2](#tag/Prohozhdenie-3DS/operation/Submit3DSAuthorizationV2) в зависимости от версии 3DS<br>  
**URL**: `ACSUrl` (возвращается в ответе метода [FinishAuthorize](#tag/Standartnyj-platezh/operation/FinishAuthorize)).  


#### RequestBody

- application/x-www-form-urlencoded

```ts
{
  "oneOf": [
    {
      "$ref": "#/components/schemas/ACSUrl_V1"
    },
    {
      "$ref": "#/components/schemas/ACSUrl_V2"
    }
  ]
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  "oneOf": [
    {
      "$ref": "#/components/schemas/ACSUrlResponseV1"
    },
    {
      "$ref": "#/components/schemas/ACSUrlResponseV2"
    }
  ]
}
```

- Examples

  - AcsURLResp21

```json
{
  "summary": "Ответ на URL, который указан в методе Check3DSVersion",
  "value": {
    "cres": "FwlGfSwmRARfDXsgt1PBvbtYTIMY2l2SThPEeei6aFwlGfSwmRARfDXsgt1PBvbtYTIMY2l2SThPEeei6aGIdXfZ3psSfuKZt3O35yCVpfAbYs8AmIdIHQmJyskyNxYZyGIdXfZ3psSfuKZt3O35yCVpNkQwzuH68WlB9oiEnt6NdGaegzJ6ljDlKAl7tvQNCPw2FjDWbhHlxj34ut0hhivaJBNHSmvumv7sndTpA7AzxJYNUhkp67fG411fAbYs8AmIdIHQmJyskyNxYZy"
  }
}
```

***

### [POST]/v2/Confirm

- Summary  
Подтвердить платеж

- Description  
Метод для списания заблокированных денежных средств. Используется при двухстадийном проведении платежа. Применим   
только к платежам в статусе `AUTHORIZED`. Статус транзакции перед разблокировкой  
— `CONFIRMING`. Сумма списания может быть меньше или равна сумме авторизации.  
  
  
[Подробнее про двухстадийный платеж](https://www.tbank.ru/kassa/dev/payments/#tag/Scenarii-oplaty-po-karte/Scenarii-platezha)  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор платежа в системе Т‑Бизнес.
  PaymentId: string
  // Подпись запроса — хэш `SHA-256`.
  Token: string
  // IP-адрес клиента.
  IP?: string
  // Сумма в копейках. Если не передан, используется `Amount`, переданный в методе **Init**.
  Amount?: number
  // JSON-объект с данными чека. Обязателен, если подключена онлайн-касса.
  Receipt: {
  }
  // JSON-объект с данными маркетплейса. Обязательный для маркетплейсов.
  Shops: {
    // Код магазина
    // 
    ShopCode: string
    // Cумма в копейках, которая относится к
    // указанному `ShopCode`.
    // 
    Amount: number
    // Наименование товара.
    // 
    Name?: string
    // Сумма комиссии в копейках, удерживаемая из
    // возмещения партнера в пользу маркетплейса.
    // Если не передано, используется комиссия,
    // указанная при регистрации.
    // 
    Fee?: string
  }[]
  // Способ платежа.
  // 
  Route?: enum[TCB, BNPL]
  // Источник платежа.
  // 
  Source?: enum[installment, BNPL]
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор заказа в системе мерчанта.
  OrderId: string
  // Успешность прохождения запроса — `true`/`false`.
  Success: boolean
  // Статус транзакции.
  Status: enum[NEW, AUTHORIZING, AUTHORIZED, AUTH_FAIL, CANCELED, CHECKING, CHECKED, COMPLETING, COMPLETED, CONFIRMING, CONFIRMED, DEADLINE_EXPIRED, FORM_SHOWED, PARTIAL_REFUNDED, PREAUTHORIZING, PROCESSING, 3DS_CHECKING, 3DS_CHECKED, REVERSING, REVERSED, REFUNDING, REFUNDED, REJECTED, UNKNOWN]
  // Идентификатор транзакции в системе Т‑Бизнес.
  PaymentId: string
  // Код ошибки. `0` в случае успеха.
  ErrorCode: string
  // Краткое описание ошибки.
  Message?: string
  // Подробное описание ошибки.
  Details?: string
  // Информация по способу оплаты или деталям для платежей в рассрочку.
  Params: {
    // Возможные значения:
    // * `Route` — способ оплаты.
    // * `Source` — источник платежа.
    // * `CreditAmount` — сумма выданного кредита в копейках. Возвращается только для платежей в рассрочку.
    // 
    Key?: enum[Route, Source, CreditAmount]
    // Возможные значения:
    // * `ACQ`, `BNPL`, `TCB`, `SBER` — для Route.
    // * `BNPL`, `cards`, `Installment`, `MirPay`, `qrsbp`, `SberPay`, `TinkoffPay`, `YandexPay` — для Source.
    // * Сумма в копейках — для CreditAmount.
    // 
    Value?: enum[ACQ, BNPL, TCB, SBER, BNPL, cards, Installment, MirPay, qrsbp, SberPay, TinkoffPay, YandexPay]
  }[]
}
```

***

### [POST]/v2/Cancel

- Summary  
Отменить платеж

- Description  
Отменяет платежную сессию. В зависимости от статуса платежа, переводит его в следующие состояния:   
* `NEW` — `CANCELED`;  
* `AUTHORIZED` — `PARTIAL_REVERSED`, если отмена не на полную сумму;  
* `AUTHORIZED` — `REVERSED`, если отмена на полную сумму;  
* `CONFIRMED` — `PARTIAL_REFUNDED`, если возврат не на полную сумму;  
* `CONFIRMED` — `REFUNDED`, если возврат на полную сумму.  
  
При оплате в рассрочку платеж можно отменить только в статусе `AUTHORIZED`.   
При оплате «Долями» делается частичный или полный возврат, если операция в статусе `CONFIRMED` или `PARTIAL_REFUNDED`.  
  
Если платеж находился в статусе `AUTHORIZED`, холдирование средств на карте  
клиента отменяется. При переходе из статуса `CONFIRMED` денежные средства возвращаются на карту клиента.  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор платежа в системе Т‑Бизнес.
  PaymentId: string
  // Подпись запроса — хэш `SHA-256`.
  Token: string
  // IP-адрес клиента.
  IP?: string
  // Сумма в копейках. Если не передан, используется `Amount`, переданный в методе **Init**.
  // 
  // 
  // При отмене статуса `NEW` поле `Amount` игнорируется, даже если оно заполнено. Отмена производится на полную сумму.
  // 
  Amount?: number
  // JSON-объект с данными чека. Обязателен, если подключена онлайн-касса.
  // 
  // Если отмена делается только по части товаров, данные, переданные в этом запросе, могут отличаться данных, переданных в **Init**.
  // При полной отмене структура чека не передается, при частичной — передаются товары, которые нужно отменить.
  Receipt: {
  }
  // JSON-объект с данными маркетплейса. Обязательный для маркетплейсов.
  Shops: {
    // Код магазина.
    // 
    ShopCode: string
    // Cумма в копейках, которая относится к
    // указанному `ShopCode`.
    // 
    Amount: number
    // Наименование товара.
    // 
    Name?: string
  }[]
  // Код банка в классификации СБП, в который нужно выполнить возврат. Смотрите параметр `MemberId` методе [**QrMembersList**](#tag/Oplata-cherez-SBP/paths/~1QrMembersList/post).
  QrMemberId?: string
  // Способ платежа.
  // 
  Route?: enum[TCB, BNPL]
  // Источник платежа.
  // 
  Source?: enum[installment, BNPL]
  // Идентификатор операции на стороне мерчанта. Параметр не работает для операций по СБП. Обязателен для операций «Долями» и в рассрочку.
  // 
  // * Если поле не передано или пустое (""), запрос будет обработан без проверки ранее созданных возвратов.
  // * Если поле заполнено, перед проведением возврата проверяется запрос на отмену с таким `ExternalRequestId`.
  // * Если такой запрос уже есть, в ответе вернется текущее состояние платежной операции, если нет — платеж отменится.
  // * Для операций «Долями» при заполнении параметра нужно генерировать значение в формате `UUID v4`.
  // * Для операций в рассрочку при заполнении параметра нужно генерировать значение с типом `string` — ограничение 100 символов.
  // 
  ExternalRequestId?: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор заказа в системе мерчанта.
  OrderId: string
  // Успешность прохождения запроса — `true`/`false`.
  Success: boolean
  // Статус транзакции.
  Status: string
  // Сумма в копейках до операции отмены.
  OriginalAmount: number
  // Сумма в копейках после операции отмены.
  NewAmount: number
  // Уникальный идентификатор транзакции в системе Т‑Бизнес.
  PaymentId: number
  // Код ошибки. `0` в случае успеха.
  ErrorCode: string
  // Краткое описание ошибки.
  Message?: string
  // Подробное описание ошибки.
  Details?: string
  // Идентификатор операции на стороне мерчанта.
  ExternalRequestId?: string
}
```

***

### [POST]/v2/Charge

- Summary  
Автоплатеж

- Description  
## Схема проведения рекуррентного платежа  
  
Метод проводит рекуррентный (повторный) платеж — безакцептное списание денежных средств со счета банковской карты клиента.  
Чтобы его использовать, клиент должен совершить хотя бы один платеж в пользу мерчанта, который должен быть указан как рекуррентный   
(параметр `Recurrent` методе **Init**), но фактически являющийся первичным.   
  
После завершения оплаты в уведомлении на `AUTHORIZED` или `CONFIRMED` будет передан параметр `RebillId`.  
  
В дальнейшем для проведения рекуррентного платежа мерчант должен вызвать метод **Init**, указать нужную сумму для списания  
в параметре `Amount`, а затем без переадресации на `PaymentURL` вызвать метод **Charge** для оплаты по тем же реквизитам  
и передать параметр `RebillId`, полученный при совершении первичного платежа.  
  
Метод **Charge** работает по одностадийной и двухстадийной схеме оплаты. Чтобы перейти на двухстадийную схему, нужно   
переключить терминал в [личном кабинете](https://business.tbank.ru/oplata/main) и написать на <<EMAIL>> с просьбой переключить схему рекуррентов.  
  
>По умолчанию метод **Charge** отключен. Чтобы его включить:  
>- на DEMO-терминале — напишите на <<EMAIL>>;  
>- на боевом терминале — обратитесь к своему персональному менеджеру.  
  
При проведении рекуррентного платежа учитывайте взаимосвязь атрибута `RebillId` метода **Charge**:  
* Со значениями атрибутов `OperationInitiatorType` и `Recurrent` метода **Init**;  
* С типом терминала, который используется для проведения операций — ECOM или AFT.  
      
Допустимые сценарии взаимосвязи:  
  
|CIT/MIT|Тип операции|`OperationInitiator` в **Init**|`RebillId` в **Charge**|`Recurrent` в **Init**| AFT-терминал | ECOM-терминал |  
|---|---|---|---|---|--------------|---------------|  
|CIT|Credential-Not-Captured|0|null|N| Разрешено    | Разрешено     |  
|CIT|Credential-Captured|1|null|Y| Разрешено    | Разрешено     |  
|CIT|Credential-on-File|2|not null|N| Запрещено    | Разрешено     |  
|MIT|Credential-on-File, Recurring|R|not null|N| Запрещено    | Разрешено     |  
|MIT|Credential-on-File, Installment|I|not null|N| Разрешено    | Запрещено     |  
  
Если передавать значения атрибутов, которые не соответствуют значениям в таблице, MAPI вернет ошибку `1126` —  
несопоставимые значения `RebillId` или `Recurrent` с переданным значением `OperationInitiatorType`.  
  
## Одностадийная оплата  
  
1. Проведите родительский платеж через метод **Init** с указанием дополнительных параметров `Recurrent=Y` и `CustomerKey`.   
2. Только для `мерчантов без PCI DSS` — переадресуйте клиента на `PaymentUrl`.  
3. После того как клиент оплатит заказ, в уведомлении о статусе `AUTHORIZED` или `CONFIRMED` будет передан параметр `RebillId`.  
Сохраните его.  
4. Через некоторое время для выполнения рекуррентного платежа вызовите метод **Init** со стандартными параметрами —  
параметры `Recurrent` и `CustomerKey` в этом случае не нужны. Вернется параметр `PaymentId` — сохраните его.  
5. Вызовите метод **Charge** с параметром `RebillId` из пункта 3 и `PaymentId` из пункта 4.   
При успешном сценарии операция перейдет в статус `CONFIRMED`.  
  
  
## Двухстадийная оплата  
  
1. Проведите родительский платеж через метод **Init** с указанием дополнительных параметров `Recurrent=Y` и `CustomerKey`.  
2. Только для `мерчантов без PCI DSS` — переадресуйте клиента на `PaymentUrl`.  
3. После того как клиент оплатит заказ, в уведомлении о статусе `AUTHORIZED` или `CONFIRMED` будет передан параметр `RebillId`.  
Сохраните его.  
4. Через некоторое время для выполнения рекуррентного платежа вызовите метод **Init** со стандартными параметрами —  
параметры `Recurrent` и `CustomerKey` в этом случае не нужны. Вернется параметр `PaymentId` — сохраните его.  
5. Вызовите метод **Charge** с параметром `RebillId` из пункта 3 и `PaymentId` из пункта 4.   
6. Вызовите метод **Confirm** для подтверждения платежа.  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала. <br>
  // Выдается мерчанту в Т‑Бизнес при заведении терминала.
  // 
  TerminalKey: string
  // Уникальный идентификатор транзакции в
  // системе Т‑Бизнес.
  // 
  PaymentId: string
  // Идентификатор рекуррентного платежа. Значение зависит от атрибутов:
  //   * `OperationInitiatorType` в методе **init**,
  //   * `Recurrent` в методе **Init**.
  // 
  // Подробнее — в описаниях [Рекуррентный платёж](#tag/Rekurrentnyj-platyozh) и [Инициализация платежа](#tag/Standartnyj-platyozh/paths/~1Init/post)
  // 
  RebillId: string
  // Подпись запроса.
  // 
  Token: string
  // IP-адрес клиента.
  // 
  IP?: string
  // `true` — если клиент хочет получать
  // уведомления на почту.
  // 
  SendEmail?: boolean
  // Адрес почты клиента.
  // Обязателен при передаче `SendEmail`.
  // 
  InfoEmail?: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес 
  // при заведении терминала.
  // 
  TerminalKey: string
  // Сумма в копейках.
  // 
  Amount: number
  // Идентификатор заказа в системе мерчанта.
  // 
  OrderId: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Статус платежа. Возвращается один из статусов:
  // * `CONFIRMED` — если платеж выполнен;
  // * `REJECTED` — если платеж не выполнен.
  // 
  Status: string
  // Идентификатор платежа в системе Т‑Бизнес.
  // 
  PaymentId: string
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
}
```

***

### [POST]/v2/GetState

- Summary  
Получить статуса платежа

- Description  
Метод возвращает статус платежа.  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала. <br>
  // Выдается мерчанту в Т‑Бизнес при заведении терминала.
  // 
  TerminalKey: string
  // Идентификатор платежа в системе Т‑Бизнес.
  // 
  PaymentId: string
  // Подпись запроса.
  // 
  Token: string
  // IP-адрес клиента.
  // 
  IP?: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Сумма в копейках.
  // 
  Amount: number
  // Идентификатор заказа в системе мерчанта.
  // 
  OrderId: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Статус платежа. Подробнее — в разделе [Статусная модель платежа](#tag/Scenarii-oplaty-po-karte/Statusnaya-model-platezha).
  // 
  Status: string
  // Идентификатор платежа в системе Т‑Бизнес.
  // 
  PaymentId: string
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
  // Информация по способу оплаты или деталям для платежей в рассрочку.
  Params: {
    // Возможные значения:
    // * `Route` — способ оплаты.
    // * `Source` — источник платежа.
    // * `CreditAmount` — сумма выданного кредита в копейках. Возвращается только для платежей в рассрочку.
    // 
    Key?: enum[Route, Source, CreditAmount]
    // Возможные значения:
    // * `ACQ`, `BNPL`, `TCB`, `SBER` — для Route.
    // * `BNPL`, `cards`, `Installment`, `MirPay`, `qrsbp`, `SberPay`, `TinkoffPay`, `YandexPay` — для Source.
    // * Сумма в копейках — для CreditAmount.
    // 
    Value?: enum[ACQ, BNPL, TCB, SBER, BNPL, cards, Installment, MirPay, qrsbp, SberPay, TinkoffPay, YandexPay]
  }[]
}
```

***

### [POST]/v2/AddCustomer

- Summary  
Зарегистрировать клиента

- Description  
Регистрирует клиента в связке с терминалом.  
  
>Можно автоматически связать клиента с картой, которой был произведен платеж, если в методе **Init** передать параметр `CustomerKey`.  
Это позволит сохранить и позже показывать клиенту замаскированный номер карты, по которой будет совершен рекуррентный платеж.  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  CustomerKey: string
  // Подпись запроса.
  Token: string
  // IP-адрес запроса.
  IP?: string
  // Электронная почта клиента.
  Email?: string
  // Телефон клиента в формате `+{Ц}`.
  // 
  Phone?: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор терминала, выдается продавцу Т‑Бизнес.
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  CustomerKey: string
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
}
```

- Examples

  - AddCustomerResponse

```json
{
  "summary": "Пример ответа",
  "value": {
    "TerminalKey": "TinkoffBankTest",
    "CustomerKey": "05d65baa-9718-445e-8212-76fa0dd4c1d2",
    "ErrorCode": "0",
    "Success": true,
    "Message": "Неверные параметры",
    "Details": "Терминал не найден"
  }
}
```

***

### [POST]/v2/GetCustomer

- Summary  
Получить данные клиента

- Description  
Возвращает данные клиента, сохраненные в связке с терминалом   


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  CustomerKey: string
  // Подпись запроса.
  Token: string
  // IP-адрес запроса.
  IP?: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  CustomerKey: string
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
  // Электронная почта клиента.
  Email?: string
  // Телефон клиента в формате `+{Ц}`.
  // 
  Phone?: string
}
```

- Examples

  - GetCustomerResponse

```json
{
  "summary": "Пример ответа",
  "value": {
    "TerminalKey": "TinkoffBankTest",
    "CustomerKey": "4264aa7b-08ab-4429-ab5a-2a171d841ced",
    "ErrorCode": "0",
    "Success": true,
    "Message": "Неверный статус клиента",
    "Details": "Клиент не найден.",
    "Email": "<EMAIL>",
    "Phone": "+***********"
  }
}
```

***

### [POST]/v2/RemoveCustomer

- Summary  
Удалить данные клиента

- Description  
Метод для удаления сохраненных данных клиента.  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  CustomerKey: string
  // Подпись запроса.
  Token: string
  // IP-адрес запроса.
  IP?: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  CustomerKey: string
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
}
```

- Examples

  - RemoveCustomerResponse

```json
{
  "summary": "Пример ответа",
  "value": {
    "TerminalKey": "TinkoffBankTest",
    "CustomerKey": "string",
    "ErrorCode": "0",
    "Success": true,
    "Message": "Неверные параметры",
    "Details": "string"
  }
}
```

***

### [POST]/v2/GetAddCardState

- Summary  
Получить статус привязки карты

- Description  
`Для мерчантов c PCI DSS и собственной платежной формой`  
   
<br> Метод возвращает статус привязки карты  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Идентификатор запроса на привязку карты.
  // 
  RequestKey: string
  // Подпись запроса.
  // 
  Token: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Идентификатор запроса на привязку карты.
  // 
  RequestKey: string
  // Статус привязки карты:
  // * `NEW` — новая сессия привязки карты,
  // * `FORM_SHOWED` — показ формы привязки карты,
  // * `THREE_DS_CHECKING` — отправка клиента на проверку 3DS;
  // * `THREE_DS_CHECKED` — клиент успешно прошел проверку 3DS;
  // * `AUTHORIZING` — отправка платежа на 0 руб;
  // * `AUTHORIZED` — платеж на 0 руб прошел успешно;
  // * `COMPLETED` — карта успешно привязана,
  // * `REJECTED` — привязать карту не удалось.
  // 
  Status: enum[NEW, FORM_SHOWED, THREE_DS_CHECKING, THREE_DS_CHECKED, AUTHORIZING, AUTHORIZED, COMPLETED, REJECTED]
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Идентификатор карты в системе Т‑Бизнес.
  // 
  CardId?: string
  // Идентификатор рекуррентного платежа.
  // 
  RebillId?: string
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode?: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
  // Идентификатор клиента в системе мерчанта.
  // 
  CustomerKey?: string
}
```

- Examples

  - GetAddCardStateResponse

```json
{
  "summary": "Пример ответа",
  "value": {
    "TerminalKey": "TinkoffBankTest",
    "RequestKey": "13021e10-a3ed-4f14-bcd1-823b5ac37390",
    "Status": "NEW",
    "Success": true,
    "CardId": "*********",
    "RebillId": "*********",
    "ErrorCode": "0",
    "Message": "Неверные параметры",
    "Details": "Данный RequestKey не найден.",
    "CustomerKey": "testCustomer1234"
  }
}
```

***

### [POST]/v2/GetCardList

- Summary  
Получить список карт клиента

- Description  
Возвращает список всех привязанных карт клиента, включая удаленные  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  // 
  CustomerKey: string
  // Признак сохранения карты для оплаты в 1 клик.
  // 
  SavedCard?: boolean
  // Подпись запроса
  // 
  Token: string
  // IP-адрес запроса
  // 
  IP?: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор карты в системе Т‑Бизнес
  // 
  CardId: string
  // Номер карты
  // 
  Pan: string
  // Статус карты:
  // * A — активная,  
  // * D — удаленная.
  // 
  Status: enum[A, D]
  // Идентификатор рекуррентного платежа
  // 
  RebillId?: string
  // Тип карты:
  // * карта списания (0),
  // * карта пополнения (1),
  // * карта пополнения и списания (2).
  // 
  CardType: enum[0, 1, 2]
  // Срок действия карты
  // 
  ExpDate?: string
}[]
```

***

### [POST]/v2/RemoveCard

- Summary  
Удалить привязанную карту клиента

- Description  
Метод для удаления привязанной карты клиента.  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  // 
  CustomerKey: string
  // Идентификатор карты в системе Т‑Бизнес.
  // 
  CardId: string
  // Подпись запроса.
  // 
  Token: string
  // IP-адрес запроса.
  // 
  IP?: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Статус карты. `D` — удалена.
  // 
  Status: string
  // Идентификатор клиента в системе мерчанта.
  // 
  CustomerKey: string
  // Идентификатор карты в системе Т‑Бизнес.
  // 
  CardId: string
  // Тип карты:
  // * `0` — карта списания,
  // * `1` — карта пополнения,
  // * `2` — карта пополнения и списания.
  // 
  CardType: enum[0, 1, 2]
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
}
```

- Examples

  - RemoveCardResponse

```json
{
  "summary": "Пример ответа",
  "value": {
    "TerminalKey": "TinkoffBankTest",
    "Status": "D",
    "CustomerKey": "testCustomer1234",
    "CardId": "*********",
    "CardType": 0,
    "Success": true,
    "ErrorCode": "0",
    "Message": "Неверные параметры",
    "Details": "Не удалось удалить карту клиента, для данного клиента такая карта не существует"
  }
}
```

***

### [SERVERS]/v2/GetQr

***

### [POST]/v2/GetQr

- Summary  
Сформировать QR

- Description  
Метод регистрирует QR и возвращает информацию о нем.   
Вызывается после метода **Init**.  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Уникальный идентификатор транзакции в системе Т‑Бизнес. Запрос будет работать, даже если указать значение в формате `string`.
  // 
  PaymentId: number
  // Тип возвращаемых данных:
  // * `PAYLOAD` — в ответе возвращается только Payload — по умолчанию;
  // * `IMAGE` — в ответе возвращается SVG изображение QR.
  // 
  DataType?: enum[PAYLOAD, IMAGE] //default: PAYLOAD
  // Подпись запроса.
  Token: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Номер заказа в системе мерчанта.
  // 
  OrderId: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // В зависимости от параметра `DataType` в запросе:
  //   * `Payload` — информация, которая должна быть закодирована в QR;
  //   * `SVG` — изображение QR, в котором уже закодирован Payload.
  // 
  Data: string
  // Уникальный идентификатор транзакции в системе Т‑Бизнес.
  // 
  PaymentId: number
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
  // Идентификатор запроса на привязку счета. Передается в случае привязки и одновременной оплаты по CБП.
  // 
  RequestKey: string
}
```

- Examples

  - QrResponse_FULL

```json
{
  "summary": "Пример ответа (FULL)",
  "value": {
    "TerminalKey": "TinkoffBankTest",
    "OrderId": "21057",
    "Success": true,
    "Data": "https://qr.nspk.ru/********************************?type=01&bank=************&sum=10000&cur=RUB&crc=C08B",
    "PaymentId": 10063,
    "ErrorCode": "0",
    "Message": "Неверные параметры",
    "Details": "Подробное описание ошибки",
    "RequestKey": "Идентификатор запроса"
  }
}
```

***

### [POST]/v2/SubmitRandomAmount

- Summary  
SubmitRandomAmount

- Description  
Метод для подтверждения карты путем блокировки случайной суммы.

#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Идентификатор запроса на привязку карты.
  // 
  RequestKey: string
  // Сумма в копейках.
  // 
  Amount: number
  // Подпись запроса.
  // 
  Token: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес 
  // при заведении терминала.
  // 
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  // 
  CustomerKey: string
  // Идентификатор запроса на привязку карты.
  // 
  RequestKey: string
  // Идентификатор рекуррентного платежа.
  // 
  RebillId?: string
  // Идентификатор карты в системе Т‑Бизнес.
  // 
  CardId: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
  // Статус платежа.
  // 
  Status?: string
}
```

***

### [POST]/v2/Submit3DSAuthorization

- Summary  
Подтвердить прохождение 3DS v1.0

- Description  
`Для мерчантов c PCI DSS и собственной платежной формой`  
  
 Проверяет результаты прохождения 3-D Secure и при успешном прохождении  
 подтверждает инициированный платеж.  
 При использовании:  
 - одностадийной оплаты — списывает денежные средства с карты  
 клиента;  
 - двухстадийной оплаты — блокирует указанную сумму на карте клиента.  
  
 Формат запроса — `x-www-form-urlencoded`.  
  
  
 После того, как мерчант получит ответ ACS с результатами прохождения 3-D Secure на `TermUrl`, нужно  
 отправить запрос через метод **Submit3DSAuthorization**.  


#### RequestBody

- application/x-www-form-urlencoded

```ts
{
  // Уникальный идентификатор транзакции в системе. Возвращается в ответе от ACS.
  // 
  MD: string
  // Шифрованная строка, содержащая результаты 3-D Secure аутентификации. Возвращается в ответе от ACS.
  // 
  PaRes: string
  // Уникальный идентификатор транзакции в системе Т‑Бизнес.
  // 
  PaymentId?: string
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey?: string
  // Подпись запроса.
  // 
  Token?: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Номер заказа в системе мерчанта.
  // 
  OrderId: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Статус транзакции:
  // - `CONFIRMED` — при успешном сценарии и одностадийном проведении платежа;
  // - `AUTHORIZED` — при успешном сценарии и двухстадийном проведении платежа; 
  // - `REJECTED` — при неуспешном сценарии.
  // 
  Status: string
  // Уникальный идентификатор транзакции в системе Т‑Бизнес.
  // 
  PaymentId: string
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
}
```

- Examples

  - Submit3DSAuthorizationResponse

```json
{
  "summary": "Пример ответа",
  "value": {
    "TerminalKey": "TinkoffBankTest",
    "OrderId": "21050",
    "Success": true,
    "Status": "CONFIRMED",
    "PaymentId": "10063",
    "ErrorCode": "0",
    "Message": "string",
    "Details": "string"
  }
}
```

***

### [POST]/v2/Submit3DSAuthorizationV2

- Summary  
Подтвердить прохождение 3DS v2.1

- Description  
`Для мерчантов c PCI DSS и собственной платежной формой`  
  
 Проверяет результаты прохождения 3-D Secure и при успешном прохождении  
 подтверждает инициированный платеж.  
 При использовании:  
 - одностадийной оплаты — списывает денежные средства с карты  
 клиента;  
 - двухстадийной оплаты — блокирует указанную сумму на карте клиента.  
  
 Формат запроса — `x-www-form-urlencoded`.  
  
 После того, как мерчант получит ответ ACS с результатами прохождения 3-D Secure на `cresCallbackUrl`, нужно  
 отправить запрос через метод **Submit3DSAuthorizationV2**.  


#### RequestBody

- application/x-www-form-urlencoded

```ts
{
  // Уникальный идентификатор транзакции в системе Т‑Бизнес.
  // 
  PaymentId: string
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Подпись запроса.
  // 
  Token: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Номер заказа в системе мерчанта.
  // 
  OrderId: string
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Статус транзакции
  // 
  Status: string
  // Уникальный идентификатор транзакции в системе Т‑Бизнес.
  // 
  PaymentId: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
}
```

- Examples

  - Submit3DSAuthorizationV2Response

```json
{
  "summary": "Пример ответа",
  "value": {
    "OrderId": "21050",
    "TerminalKey": "TinkoffBankTest",
    "Status": "CONFIRMED",
    "PaymentId": "10063",
    "Success": true,
    "ErrorCode": "0",
    "Message": "string",
    "Details": "string"
  }
}
```

***

### [GET]/v2/TinkoffPay/terminals/{TerminalKey}/status

- Summary  
Определить возможность проведения платежа

- Description  
Метод для определения возможности проведения платежа T‑Pay на терминале и устройстве.  


#### Responses

- 200 OK

`application/json`

```ts
{
  // Параметры ответа.
  // 
  Params: {
    // Наличие возможности проведения оплаты
    // T‑Pay по API, SDK.
    // 
    Allowed: boolean
    // Версия T‑Pay, доступная на терминале:
    // * `1.0` — e-invoice,
    // * `2.0` — T‑Pay.
    // 
    Version?: string
  }
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
}
```

***

### [GET]/v2/TinkoffPay/transactions/{paymentId}/versions/{version}/link

- Summary  
Получить ссылку

- Description  
Метод получения Link для безусловного редиректа на мобильных устройствах  


#### Responses

- 200 OK

`application/json`

```ts
{
  // Параметры ответа
  // 
  Params: {
    // Link для перехода в приложение MB на мобильных
    // устройствах
    // 
    RedirectUrl: string
    // URL для получения QR
    // 
    WebQR?: string
  }
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки
  // 
  Message?: string
  // Подробное описание ошибки
  // 
  Details?: string
}
```

***

### [GET]/v2/TinkoffPay/{paymentId}/QR

- Summary  
Получить QR

- Description  
Метод получения QR для десктопов.  


#### Responses

- 200 OK

`image/svg`

```ts
{
  "type": "string",
  "format": "binary"
}
```

***

### [GET]/v2/SberPay/{paymentId}/QR

- Summary  
Получить QR

- Description  
Метод получения QR для десктопов.  


#### Responses

- 200 SVG QR, размер — 124*124.

`image/svg`

```ts
{
  "type": "string",
  "format": "binary"
}
```

***

### [GET]/v2/SberPay/transactions/{paymentId}/link

- Summary  
Получить ссылку

- Description  
Метод для получения ссылки SberPay.  


#### Responses

- 200 OK

`application/json`

```ts
{
  // Параметры ответа.
  // 
  Params: {
    // URL для перехода.
    // 
    RedirectUrl: string
  }
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
}
```

***

### [SERVERS]/v2/QrMembersList

***

### [POST]/v2/QrMembersList

- Summary  
Получить список банков-пользователей QR

- Description  
Метод возвращает список участников куда может быть осуществлен возврат платежа, совершенного  
по QR.  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Уникальный идентификатор транзакции в системе
  // Т‑Бизнес
  // 
  PaymentId: string
  // Подпись запроса.
  Token: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  Members: {
    // Идентификатор участника.
    // 
    MemberId: string
    // Наименование участника.
    // 
    MemberName: string
    // * `true` — если данный участник был получателем
    // указанного платежа, 
    // * `false` — если нет.
    // 
    IsPayee: boolean
  }[]
  // Идентификатор заказа в системе мерчанта.
  // 
  OrderId: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
}
```

***

### [SERVERS]/v2/AddAccountQr

***

### [POST]/v2/AddAccountQr

- Summary  
Привязать счёт к магазину

- Description  
Метод инициирует привязку счета клиента к магазину  
и возвращает информацию о нем  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Подробное описание деталей заказа.
  Description: string
  // Тип возвращаемых данных:
  // * `PAYLOAD` — в ответе возвращается только Payload. Значение по умолчанию.
  // * `IMAGE` — в ответе возвращается SVG-изображение QR.
  // 
  DataType?: enum[PAYLOAD, IMAGE] //default: PAYLOAD
  // JSON-объект, содержащий
  // дополнительные параметры в виде `ключ`:`значение`. Эти параметры будут
  // переданы на страницу оплаты, если она
  // кастомизирована. Максимальная длина для
  // каждого передаваемого параметра:
  //   * ключ — 20 знаков,
  //   * значение — 100 знаков.
  // 
  // 
  // Максимальное количество пар `ключ`:`значение` — не больше 20.
  // 
  Data: {
  }
  // Cрок жизни ссылки или динамического QR-кода СБП, если выбран этот способ
  // оплаты. Если параметр `RedirectDueDate` не был передан, проверяется настроечный параметр
  // платежного терминала `REDIRECT_TIMEOUT`, который может содержать значение срока жизни ссылки в 
  // часах. Если его значение больше нуля, оно будет установлено в качестве срока жизни ссылки или
  // динамического QR-кода, если нет — устанавливается значение по умолчанию: 1440 мин (1 сутки).
  // 
  // Если текущая дата превышает дату, переданную в этом параметре, ссылка для оплаты или возможность
  // платежа по QR-коду становятся недоступными и платёж выполнить нельзя.
  // - Максимальное значение — 90 дней от текущей даты.
  // - Минимальное значение — 1 минута от текущей даты.
  // - Формат даты — `YYYY-MM-DDTHH24:MI:SS+GMT`.
  // 
  RedirectDueDate?: string
  // Подпись запроса.
  Token: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // В зависимости от параметра `DataType` в запросе:
  //   * `Payload` — информация, которая должна быть закодирована в QR;
  //   * `SVG` — изображение QR, в котором уже закодирован Payload.
  // 
  Data: string
  // Идентификатор запроса на привязку счета.
  // 
  RequestKey: string
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Краткое описание ошибки.
  // 
  Message?: string
}
```

- Examples

  - AddAccountQrResponse

```json
{
  "summary": "Пример ответа",
  "value": {
    "TerminalKey": "TinkoffBankTest",
    "Description": "string",
    "DataType": "PAYLOAD",
    "Data": "https://qr.nspk.ru/********************************?type=01&bank=************&sum=10000&cur=RUB&crc=C08B",
    "RequestKey": "ed989549-d3be-4758-95c7-22647e03f9ec",
    "ErrorCode\"": "0",
    "Success": true,
    "Message": "OK"
  }
}
```

***

### [SERVERS]/v2/GetAddAccountQrState

***

### [POST]/v2/GetAddAccountQrState

- Summary  
Получить статус привязки счета к магазину

- Description  
Метод возвращает статус привязки счета клиента по магазину  


#### RequestBody

- application/json

```ts
{
  // Идентификатор запроса на привязку счета.
  // 
  RequestKey: string
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Подпись запроса.
  // 
  Token: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Платежный ключ, выдается мерчанту при заведении
  // терминала.
  // 
  TerminalKey: string
  // Идентификатор запроса на привязку счета.
  // 
  RequestKey: number
  // Идентификатор банка клиента, который будет
  // совершать оплату по привязанному счету —
  // заполнен, если статус `ACTIVE` или `INACTIVE`.
  // 
  BankMemberId?: string
  // Наименование банка-эмитента — заполнен если передан `BankMemberId`.
  // 
  BankMemberName?: string
  // Идентификатор привязки счета, назначаемый банком плательщика.
  // 
  AccountToken?: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Статус привязки карты:
  // * `NEW` — получен запрос на привязку счета;
  // * `PROCESSING` — запрос в обработке,
  // * `ACTIVE` — привязка счета успешна,
  // * `INACTIVE` — привязка счета неуспешна или деактивирована.
  // 
  Status: enum[NEW, PROCESSING, ACTIVE, INACTIVE]
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
}
```

- Examples

  - GetAddAccountQrStateResponse

```json
{
  "summary": "Пример ответа",
  "value": {
    "TerminalKey": "TinkoffBankTest",
    "RequestKey": 211258,
    "BankMemberId": "************",
    "BankMemberName": "T-Банк",
    "AccountToken": "a022254a5c3c46a7327c8a12cb5c8389",
    "Success": true,
    "Status": "ACTIVE",
    "ErrorCode": "0",
    "Message": "OK"
  }
}
```

***

### [SERVERS]/v2/GetAccountQrList

***

### [POST]/v2/GetAccountQrList

- Summary  
Получить список счетов, привязанных к магазину

- Description  
Метод возвращает список привязанных счетов клиента по магазину

#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Подпись запроса.
  // 
  Token: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Платежный ключ, выдается мерчанту при заведении
  // терминала.
  // 
  TerminalKey: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
  AccountTokens: {
    // Идентификатор запроса на привязку карты.
    // 
    RequestKey: string
    // Статус привязки карты:
    // * `NEW` — получен запрос на привязку счета;
    // * `PROCESSING` — запрос в обработке,
    // * `ACTIVE` — привязка счета успешна,
    // * `INACTIVE` — привязка счета неуспешна или деактивирована.
    // 
    Status: enum[NEW, PROCESSING, ACTIVE, INACTIVE]
    // Идентификатор привязки счета, назначаемый банком плательщика.
    // 
    AccountToken: {
    }
    // Идентификатор банка клиента (эмитент), который будет
    // совершать оплату по привязанному счету —
    // заполнен, если статус `ACTIVE` или `INACTIVE`.
    // 
    BankMemberId?: string
    // Наименование банка-эмитента — заполнен если передан `BankMemberId`.
    // 
    BankMemberName?: string
  }[]
}
```

- Examples

  - GetAccountQrListResponse

```json
{
  "summary": "Пример ответа",
  "value": {
    "TerminalKey": "TinkoffBankTest",
    "Success": true,
    "ErrorCode": "0",
    "Message": "OK",
    "AccountTokens": [
      {
        "RequestKey": "77520",
        "Status": "ACTIVE",
        "AccountToken": "0b67f2cae19b41809f85d5674de30a1a",
        "BankMemberId": "************",
        "BankMemberName": "T-Банк"
      },
      {
        "RequestKey": "77563",
        "Status": "ACTIVE",
        "AccountToken": "14ac4445811e8225db8ed312j4433a68",
        "BankMemberId": "************",
        "BankMemberName": "T-Банк"
      },
      {
        "RequestKey": "77644",
        "Status": "PROCCESING"
      }
    ]
  }
}
```

***

### [SERVERS]/v2/ChargeQr

***

### [POST]/v2/ChargeQr

- Summary  
Автоплатеж по QR

- Description  
Проведение платежа по привязанному счету по QR через СБП.  
Для возможности его использования клиент должен совершить успешную привязку счета с   
помощью метода **AddAccountQr**. После вызова метода будет отправлена нотификация на Notification  
URL о привязке счета , в которой будет указан AccountToken.  
Для совершения платежа по привязанному счету Мерчант должен вызвать метод **Init**, в котором поля   
**Recurrent= Y** и **DATA= {“QR”:“true”}**, а затем вызвать метод **ChargeQr** для оплаты по тем же самым   
реквизитам и передать параметр **AccountToken**, полученный после привязки счета по QR в   
нотификации.  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала. <br>
  // Выдается мерчанту в Т‑Бизнес при заведении терминала.
  // 
  TerminalKey: string
  // Уникальный идентификатор транзакции в
  // системе Т‑Бизнес.
  // 
  PaymentId: string
  // Идентификатор привязки счета,
  // назначаемый банком-эмитентом.
  // 
  AccountToken: string
  // Подпись запроса.
  // 
  Token: string
  // IP-адрес клиента.
  // 
  IP?: string
  // `true`, если клиент хочет получать
  // уведомления на почту.
  // 
  SendEmail?: boolean
  // Адрес почты клиента. Обязательно, если передан параметр `SendEmail` = `true`.
  // 
  InfoEmail?: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Сумма в копейках.
  // 
  Amount: number
  // Идентификатор заказа в системе мерчанта.
  // 
  OrderId: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Статус платежа. Возвращается один из трех статусов:
  // * `CONFIRMED` — если платеж выполнен;
  // * `REJECTED` — если платеж не выполнен;
  // * `FORM SHOWED` — если форма оплаты пока что только отображается, и клиент еще не успел провести оплату.
  // 
  Status?: enum[CONFIRMED, REJECTED]
  // Идентификатор платежа в системе Т‑Бизнес.
  // 
  PaymentId: string
  // Код ошибки:
  // * `0` — Успешная операция;
  // * `3013` — Рекуррентные платежи недоступны;
  // * `3015` — Неверный статус AccountToken;
  // * `3040` — Техническая ошибка;
  // * `3037` — Повторный вызов метода недоступен;
  // * `3041` — Слишком много неудачных попыток за сутки. Попробуйте еще раз завтра;
  // * `3042` — Слишком много неудачных попыток за час. Попробуйте снова через час;
  // * `9999` — Повторите попытку позже.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
  // Код валюты по `ISO 4217`.
  Currency?: number
}
```

***

### [SERVERS]/v2/SbpPayTest

***

### [POST]/v2/SbpPayTest

- Summary  
Создать тестовую платежную сессию

- Description  
Тестовая платежная сессия с предопределенным статусом по СБП.

#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Идентификатор платежа в системе Т‑Бизнес.
  // 
  PaymentId: string
  // Подпись запроса.
  // 
  Token: string
  // Признак эмуляции отказа проведения платежа банком по таймауту. По умолчанию не используется.
  // * `false` — эмуляция не требуется,
  // * `true` — требуется эмуляция. Не может быть использован вместе с `IsRejected` = `true`.
  // 
  IsDeadlineExpired?: boolean
  // Признак эмуляции отказа банка в проведении платежа. По умолчанию не используется.
  // * `false` — эмуляция не требуется,
  // * `true` — требуется эмуляция. Не может быть использован вместе с `IsDeadlineExpired` = `true`.
  // 
  IsRejected?: boolean
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки
  // 
  Message: string
  // Подробное описание ошибки
  // 
  Details: string
}
```

- Examples

  - SbpPayTestResponse

```json
{
  "summary": "Пример ответа",
  "value": {
    "Success": true,
    "ErrorCode": "0",
    "Message": "OK",
    "Details": "0"
  }
}
```

***

### [SERVERS]/v2/GetQrState

***

### [POST]/v2/GetQrState

- Summary  
Получить статус возврата

- Description  
Возвращает статус возврата платежа по СБП  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес
  // 
  TerminalKey: string
  // Уникальный идентификатор транзакции в системе
  // Т‑Бизнес, полученный в ответе на вызов метода Init
  // 
  PaymentId: string
  // Подпись запроса
  // 
  Token: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: enum[true, false]
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Статус платежа. <br>
  // Обязателен, если не произошло ошибки при получении статуса.
  // 
  Status?: string
  // Код ошибки возврата, полученный от СБП.
  // 
  QrCancelCode?: string
  // Дополнительное описание ошибки, произошедшей при возврате по QR.
  // 
  QrCancelMessage?: string
  // Номер заказа в системе мерчанта.
  // 
  OrderId?: string
  // Сумма отмены в копейках.
  // 
  Amount?: number
  // Краткое описание ошибки, произошедшей при запросе статуса.
  // 
  Message?: string
}
```

- Examples

  - GetQRStateResponse_FULL

```json
{
  "summary": "GetQRStateResponse_FULL",
  "value": {
    "Success": true,
    "ErrorCode": "0",
    "Status": "CONFIRMED",
    "QrCancelCode": "I05043",
    "QrCancelMessage": "У клиента нет расчетного счета в этом банке. Попробуйте вернуть деньги на счет этого клиента в другом банке",
    "OrderId": "7830122",
    "Amount": 10000,
    "Message": "OK"
  }
}
```

***

### [POST]/v2/CheckOrder

- Summary  
Получить статус заказа

- Description  
Метод возвращает статус заказа.  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Номер заказа в системе мерчанта. 
  // 
  // Не является уникальным идентификатором.
  // 
  OrderId: string
  // Подпись запроса
  Token: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор заказа в системе мерчанта.
  OrderId: string
  // Успешность прохождения запроса — `true`/`false`.
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  ErrorCode: string
  // Краткое описание ошибки.
  Message?: string
  // Подробное описание ошибки.
  Details?: string
  // Детали
  Payments: {
    // Уникальный идентификатор транзакции в системе Т‑Бизнес.
    // 
    PaymentId: string
    // Сумма операции в копейках.
    // 
    Amount?: number
    // Статус операции.
    // 
    Status: string
    // RRN операции.
    // 
    RRN?: string
    // Успешность прохождения запроса — `true`/`false`.
    // 
    Success: string
    // Код ошибки.
    // 
    ErrorCode?: number
    // Краткое описание ошибки.
    // 
    Message?: string
    // Идентификатор платежа в СБП.
    // 
    SbpPaymentId?: string
    // Хэшированный номер телефона покупателя.
    // 
    SbpCustomerId?: string
  }[]
}
```

- Examples

  - CheckOrderResponse

```json
{
  "summary": "Пример ответа",
  "value": {
    "TerminalKey": "TinkoffBankTest",
    "OrderId": "21057",
    "Success": true,
    "ErrorCode": "0",
    "Message": "OK",
    "Details": "None",
    "Payments": [
      {
        "PaymentId": "*********",
        "Amount": 13660,
        "Status": "NEW",
        "RRN": "********",
        "Success": "true",
        "ErrorCode": 0,
        "Message": "None",
        "SbpPaymentId": "A42631655397753A0000030011340501",
        "SbpCustomerId": "c4494ca1c0888b3fb0e2bfd0b83576aaae0d2c71161c5f472133ea9401473aee"
      }
    ]
  }
}
```

***

### [POST]/cashbox/SendClosingReceipt

- Summary  
Отправить закрывающий чек в кассу

- Description  
Метод позволяет отправить закрывающий чек в кассу.  
Условия работы метода:  
- Закрывающий чек может быть отправлен, если платежная сессия по первому чеку находится в  
  статусе `CONFIRMED`.  
- В платежной сессии был передан объект `Receipt`.  
- В объекте `Receipt` был передан хотя бы один объект — `Receipt.Items.PaymentMethod` =  
  `full_prepayment`, `prepayment` или `advance`.  


#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор платежа в системе Т‑Бизнес.
  PaymentId: string
  // Объект с данными чека.
  Receipt: {
  }
  // Подпись запроса
  Token: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Успешность прохождения запроса — `true`/`false`.
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  ErrorCode: string
  // Краткое описание ошибки
  Message?: string
}
```

***

### [POST]/v2/Notification

- Summary  
Уведомления

- Description  
Метод для получения уведомлений об изменении статуса платежа. Реализуется на стороне мерчанта.  
  
**Уведомление о привязке (NotificationAddCard)**   
  
`Для мерчантов c PCI DSS и собственной платежной формой`  
  
Уведомления магазину о статусе выполнения метода привязки карты — **AttachCard**.  
После успешного выполнения метода **AttachCard** Т‑Бизнес отправляет POST-запрос с информацией о привязке карты.  
Уведомление отправляется на ресурс мерчанта на адрес `Notification URL` синхронно и ожидает ответа в течение 10 секунд.   
После получения ответа или не получения его за заданное время сервис переадресует клиента на `Success AddCard URL`   
или `Fail AddCard URL` — в зависимости от результата привязки карты.  
В случае успешной обработки нотификации мерчант должен вернуть ответ с телом сообщения `OK` — без тегов, заглавными английскими буквами.  
  
Если тело сообщения отлично от `OK`, любая нотификация считается неуспешной, и сервис будет повторно отправлять  
нотификацию раз в час в течение 24 часов. Если за это время нотификация так и не доставлена, она складывается в дамп.  
  
  
**Нотификация о фискализации (NotificationFiscalization)**  
<br><br> Если используется подключенная онлайн касса, по результату фискализации будет  
отправлена нотификация с фискальными данными. Такие нотификации не отправляются маркетплейсам.  
  
  
**Нотификация о статусе привязки счета по QR (NotificationQr)**  
<br><br> После привязки счета по QR магазину отправляется статус привязки и токен.  
Нотификация будет приходить по статусам `ACTIVE` и `INACTIVE`.  


#### RequestBody

- application/json

```ts
{
  "oneOf": [
    {
      "$ref": "#/components/schemas/NotificationPayment"
    },
    {
      "$ref": "#/components/schemas/NotificationAddCard"
    },
    {
      "$ref": "#/components/schemas/NotificationFiscalization"
    },
    {
      "$ref": "#/components/schemas/NotificationQr"
    }
  ]
}
```

#### Responses

- 200 OK

***

### [POST]/v2/MirPay/GetDeepLink

- Summary  
Получить DeepLink

- Description  
Получение deeplink с включенным подписанным JWT-токеном. Предназначен для запроса по API

#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала. <br> Выдается мерчанту в Т‑Бизнес при заведении терминала.
  TerminalKey: string
  // Уникальный идентификатор транзакции в системе Банка.
  PaymentId: string
  // Подпись запроса.
  Token: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Успешность прохождения запроса — `true`/`false`.
  Success: enum[true, false]
  // Код ошибки. `0` в случае успеха.
  ErrorCode: string
  // Сформированный и подписанный JWT-токеном deeplink.
  Deeplink?: string
  // Краткое описание ошибки.
  Message?: string
  // Подробное описание ошибки.
  Details?: string
}
```

***

### [GET]/v2/GetTerminalPayMethods

- Summary  
Проверить доступность методов на SDK

- Description  
Метод определяет доступность методов оплаты на терминале для SDK и API. Запрос не шифруется токеном

#### RequestBody

- application/json

```ts
{
  // Идентификатор терминала. <br> Выдается мерчанту в Т‑Бизнес при заведении терминала.
  TerminalKey: string
  // Тип подключения:   
  // - API;   
  // - SDK.
  // 
  Paysource: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  // Успешность прохождения запроса — `true`/`false`.
  Success: enum[true, false]
  // Код ошибки. `0` в случае успеха.
  ErrorCode: string
  // Краткое описание ошибки.
  Message?: string
  // Подробное описание ошибки.
  Details?: string
  // Характеристики терминала.
  TerminalInfo: {
  }
  // Перечень доступных методов оплаты
  TerminalInfo.Paymethods: {
    // Доступные методы оплаты:
    // * TinkoffPay,
    // * YandexPay,
    // * ApplePay,
    // * GooglePay,
    // * SBP,
    // * MirPay.
    // 
    PayMethod: string
    // Перечень параметров подключения в формате ключзначение
    Params: {
    }
  }[]
  // Признак возможности сохранения карт.
  TerminalInfo.AddCardScheme: enum[true, false]
  // Признак необходимости подписания токеном.
  TerminalInfo.TokenRequired: enum[true, false]
  // Признак необходимости подписания токеном запроса **Init**.
  TerminalInfo.InitTokenRequired: enum[true, false]
}
```

***

### [POST]/v2/getConfirmOperation

- Summary  
Получить справку по операции

- Description  
Справку по конкретной операции можно получить на: <br><ul><li> URL-сервиса, который развернут на вашей стороне;</li><li> электронную почту.</li></ul> Чтобы сформировать токен, нужно использовать только <code>PASSWORD</code> и <code>TERMINAL_KEY</code>.

#### RequestBody

- application/json

```ts
{
  "oneOf": [
    {
      "$ref": "#/components/schemas/by_url"
    },
    {
      "$ref": "#/components/schemas/by_email"
    }
  ]
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  "oneOf": [
    {
      "$ref": "#/components/schemas/response_by_url"
    },
    {
      "$ref": "#/components/schemas/response_by_email"
    }
  ]
}
```

- Examples

  - URL

```json
{
  "summary": "URL",
  "value": {
    "Success": true,
    "ErrorCode": 0,
    "Message": "OK",
    "PaymentIdList": [
      {
        "Success": true,
        "ErrorCode": 0,
        "Message": "Запрос на отправку документа принят",
        "PaymentId": "**********"
      }
    ]
  }
}
```

  - Email

```json
{
  "summary": "Email",
  "value": {
    "Success": true,
    "ErrorCode": 0,
    "Message": "OK",
    "PaymentIdList": [
      {
        "Success": true,
        "ErrorCode": 0,
        "Message": "Запрос на отправку документа принят",
        "PaymentId": "**********"
      }
    ]
  }
}
```

## References

### #/components/schemas/Common

```ts
{
  additionalProperties?: string
  // Признак инициатора операции:
  // * `0` — оплата без сохранения реквизитов карты для последующего использования. Сценарий «0 — CIT, Credential-Not-Captured».
  // * `1` — используется, если мерчант сохраняет карту. Сценарий «1 — Consumer-Initiated, Credential-Captured».
  // * `2` — операция по ранее сохранённой карте, инициирована клиентом. Сценарий «2 — Consumer-Initiated, Credential-on-File».
  // * `R` — повторяющаяся операция по сохранённой карте без графика. Является Merchant Initiated сценарием — «R = Merchant-Initiated, Credential-on-File, Recurring».
  // * `I` — повторяющаяся операция по сохраненной карте в соответствии с графиком платежей для погашения кредита. Является Merchant Initiated сценарием — «I = Merchant-Initiated, Credential-on-File, Installment». 
  // 
  // При передаче в объекте `DATA` атрибута `OperationInitiatorType` учитывайте взаимосвязь его значений:
  //   * со значением атрибута `Recurrent` в методе **Init**;
  //   * со значением атрибута `RebillId` в методе **Charge**;
  //   * с типом терминала, используемом для проведения операций — ECOM или AFT.
  //  
  //   Подробная таблица — в разделе [Передача признака инициатора операции](#section/Peredacha-priznaka-iniciatora-operacii)
  // 
  //   Если передавать значения атрибутов, которые не соответствуют таблице, MAPI вернет ошибку 1126 —  несопоставимые 
  //   значения `rebillId` или `Recurrent` с переданным значением `OperationInitiatorType`.
  // 
  OperationInitiatorType?: enum[0, 1, 2, R, I]
}
```

### #/components/schemas/T-Pay

```ts
{
  // Тип устройства:
  // * `SDK` — вызов из мобильных приложений;
  // * `Desktop` — вызов из браузера с десктопа;
  // * `Mobile` — вызов из браузера с мобильных устройств.
  // 
  Device?: enum[SDK, Desktop, Mobile]
  // ОС устройства.
  DeviceOs?: string
  // Признак открытия в WebView.
  DeviceWebView?: boolean
  // Браузер.
  DeviceBrowser?: string
  // Признак проведения операции через T‑Pay по API.
  TinkoffPayWeb?: boolean
}
```

### #/components/schemas/LongPay1

```ts
{
  "properties": {
    "ticketNumber": {
      "description": "Номера билетов с контрольной цифрой для всех пассажиров, летящих по одному маршруту. В качестве разделителя используйте `;`.\n\nНеобязателен, если передан параметр `ticketReservationNumber`.\n",
      "type": "string"
    },
    "ticketReservationNumber": {
      "description": "Номера билетов с контрольной цифрой для всех пассажиров, летящих по одному маршруту. В качестве разделителя используйте `;`.\n\nНеобязателен, если передан параметр `ticketNumber`.\n",
      "type": "string"
    },
    "ticketSystem": {
      "description": "Код системы продажи.",
      "type": "string"
    },
    "ticketAgencyCode": {
      "description": "Код агентства.",
      "type": "string"
    },
    "ticketAgencyName": {
      "description": "Название агентства.",
      "type": "string"
    },
    "ticketRestricted": {
      "description": "Ограничения билета:\n* `0` — без ограничений,\n* `1` — невозвратный.\n",
      "type": "string"
    }
  }
}
```

### #/components/schemas/LongPay2

```ts
{
  "description": "`%` — порядковый номер пассажира от 1 до 4.\n",
  "required": [
    "ticketPassengerSurname%",
    "ticketPassengerFirstname%"
  ],
  "properties": {
    "ticketPassengerSurname%": {
      "description": "Фамилия латиницей.",
      "type": "string"
    },
    "ticketPassengerFirstname%": {
      "description": "Имя латиницей.",
      "type": "string"
    },
    "passengerPassport%": {
      "description": "Серия и номер паспорта.",
      "type": "string"
    },
    "passengerCountry%": {
      "description": "Гражданство по стандарту `ISO 3166-1 alpha-3`.",
      "type": "string"
    },
    "ticketPassengerBirthDate%": {
      "description": "Дата рождения в формате `YYYY-DD-MM`.",
      "type": "string"
    }
  }
}
```

### #/components/schemas/LongPay3

```ts
{
  "description": "\\`#` — порядковый номер пассажира от 1 до 4.",
  "required": [
    "triplegDate#",
    "triplegCarrier#",
    "triplegDestinationFrom#",
    "triplegDestinationTo#"
  ],
  "properties": {
    "triplegDate#": {
      "description": "Дата вылета в формате` YYYY-DD-MM`.",
      "type": "string"
    },
    "triplegTime#": {
      "description": "Время вылета в формате `hh24:mm:ss`.",
      "type": "string"
    },
    "triplegCarrier#": {
      "description": "Код перевозчика `ИАТА`.",
      "type": "string"
    },
    "triplegFlightNumber#": {
      "description": "Номер рейса.",
      "type": "string"
    },
    "triplegClass#": {
      "description": "Класс перелета.",
      "type": "string"
    },
    "triplegDestinationFrom#": {
      "description": "Код аэропорта вылета ИАТА.",
      "type": "string"
    },
    "triplegDestinationTo#": {
      "description": "Код аэропорта прилета ИАТА.",
      "type": "string"
    },
    "triplegCountryFrom#": {
      "description": "Код страны вылета по стандарту `ISO 3166-1 numeric`.",
      "type": "string"
    },
    "triplegCountryTo#": {
      "description": "Код страны прилета по стандарту `ISO 3166-1 numeric`.",
      "type": "string"
    },
    "triplegStopover#": {
      "description": "Остановка при пересадке:\n* `0` — разрешена,\n* `X` — запрещена.\n",
      "type": "string"
    },
    "triplegFareBasisCode#": {
      "description": "Код тарифа.",
      "type": "string"
    }
  }
}
```

### #/components/schemas/LongPay

```ts
// Расширенный набор параметров авиабилета передается при создании платежа (метод **Init**) в параметре `DATA`.
{
}
```

### #/components/schemas/AgentData

```ts
// Данные агента. Обязателен, если используется агентская схема.
// 
{
  // `Тег ФФД: 1222`
  // 
  // 
  // Признак агента. Возможные значения:
  // * `bank_paying_agent` — банковский платежный агент,
  // * `bank_paying_subagent` — банковский платежный субагент,
  // * `paying_agent` — платежный агент,
  // * `paying_subagent` — платежный субагент,
  // * `attorney` — поверенный,
  // * `commission_agent` — комиссионер,
  // * `another` — другой тип агента.
  // 
  AgentSign?: string
  // `Тег ФФД: 1044`
  // 
  // 
  // Наименование операции.
  // Параметр обязательный, если `AgentSign` передан в значениях:
  // * `bank_paying_agent`,
  // * `bank_paying_subagent`.
  // 
  OperationName?: string
  Phones?: string[]
  // `Тег ФФД: 1026`
  // 
  // 
  // Наименование оператора перевода.
  // Параметр обязательный, если в `AgentSign` передан в значениях:
  // * `bank_paying_agent`,
  // * `bank_paying_subagent`.
  // 
  OperatorName?: string
  // `Тег ФФД: 1005`
  // 
  // 
  // Адрес оператора перевода.
  // Параметр обязательный, если в `AgentSign` передан в значениях:
  // * `bank_paying_agent`,
  // * `bank_paying_subagent`.
  // 
  OperatorAddress?: string
  // `Тег ФФД: 1016`
  // 
  // 
  // ИНН оператора перевода.
  // Параметр обязательный, если в `AgentSign` передан в значениях:
  // * `bank_paying_agent`,
  // * `bank_paying_subagent`.
  // 
  OperatorInn?: string
}
```

### #/components/schemas/SupplierInfo

```ts
// Данные поставщика платежного агента. 
// Обязателен, если передается значение `AgentSign` в объекте `AgentData`.
// 
{
  Phones?: string[]
  // `Тег ФФД: 1225`
  // 
  // 
  // Наименование поставщика.
  // Атрибут обязателен, если передается значение `AgentSign` 
  // в объекте `AgentData`.
  // Внимание: в эти 239 символов включаются телефоны поставщика 
  // + 4 символа на каждый телефон.
  // 
  // 
  // Например, если передано два телефона поставщика длиной 12 и 14 символов, 
  // максимальная длина наименования поставщика будет 
  // 239 – (12 + 4) – (14 + 4) = 205 символов.
  // 
  Name?: string
  // `Тег ФФД: 1226`
  // 
  // 
  // ИНН поставщика в формате `ЦЦЦЦЦЦЦЦЦЦ`.
  // Атрибут обязателен, если передается значение `AgentSign` 
  // в объекте `AgentData`.
  // 
  Inn?: string
}
```

### #/components/schemas/Items_FFD_105

```ts
{
  // `Тег ФФД: 1030`
  // 
  // 
  // Наименование товара.
  // 
  Name: string
  // `Тег ФФД: 1078`
  // 
  // 
  // Цена в копейках.
  // 
  Price: number
  // `Тег ФФД: 1023`
  // 
  // 
  // Количество или вес товара.
  // Максимальное количество символов — 8, где целая часть — не больше 5 знаков, дробная — не больше 3 знаков для АТОЛ, 
  // и 2 знаков для CloudPayments.
  // 
  Quantity: number
  // `Тег ФФД: 1043`
  // 
  // 
  // Стоимость товара в копейках.
  // Произведение `Quantity` и `Price`.
  // 
  Amount: number
  // `Тег ФФД: 1214`
  // 
  // 
  // Возможные значения:
  // * `full_prepayment` — предоплата 100%,
  // * `prepayment` — предоплата,
  // * `advance` — аванс,
  // * `full_payment` — полный расчет,
  // * `partial_payment` — частичный расчет и кредит,
  // * `credit` — передача в кредит,
  // * `credit_payment` — оплата кредита.
  // 
  // 
  // Если значение не
  // передано, по умолчанию в онлайн-кассу
  // передается признак способа расчёта
  // `full_payment`.
  // 
  PaymentMethod?: enum[full_prepayment, prepayment, advance, full_payment, partial_payment, credit, credit_payment] //default: full_payment
  // `Тег ФФД: 1212`
  // 
  // 
  // Признак предмета расчета.
  // Возможные значения:
  // * `commodity` — товар,
  // * `excise` — подакцизный товар,
  // * `job` — работа,
  // * `service` — услуга,
  // * `gambling_bet` — ставка азартной игры,
  // * `gambling_prize` — выигрыш азартной игры,
  // * `lottery` — лотерейный билет,
  // * `lottery_prize` — выигрыш лотереи,
  // * `intellectual_activity` — предоставление результатов интеллектуальной деятельности,
  // * `payment` — платеж,
  // * `agent_commission` — агентское вознаграждение,
  // * `composite` — составной предмет расчета,
  // * `another` — иной предмет расчета,
  // 
  // 
  // Если значение не передано, по умолчанию в онлайн-кассу
  // отправляется признак предмета расчёта `commodity`.
  // 
  PaymentObject?: enum[commodity, excise, job, service, gambling_bet, gambling_prize, lottery, lottery_prize, intellectual_activity, payment, agent_commission, composite, another] //default: commodity
  // `Тег ФФД: 1199`
  // 
  // 
  // Ставка НДС.
  // Перечисление со значениями:
  // * `none` — без НДС,
  // * `vat0` — НДС по ставке 0%,
  // * `vat5` — НДС по ставке 5%,
  // * `vat7` — НДС по ставке 7%,
  // * `vat10` — НДС по ставке 10%,
  // * `vat20` — НДС по ставке 20%,
  // * `vat105` — НДС чека по расчетной ставке 5/105,
  // * `vat107` — НДС чека по расчетной ставке 7/107,
  // * `vat110` — НДС чека по расчетной ставке 10/110,
  // * `vat120` — НДС чека по расчетной ставке 20/120.
  // 
  Tax: enum[none, vat0, vat5, vat7, vat10, vat20, vat105, vat107, vat110, vat120]
  // `Тег ФФД: 1162`
  // 
  // 
  // Штрих-код в требуемом формате. В зависимости от
  // типа кассы требования могут отличаться:
  // * АТОЛ Онлайн — шестнадцатеричное
  // представление с пробелами. Максимальная
  // длина – 32 байта (^[a-fA-F0-9]{2}$)|(^([afA-F0-9]{2}\\s){1,31}[a-fA-F0-9]{2}$).
  // 
  // Пример:
  // `00 00 00 01 00 21 FA 41 00 23 05 41 00
  // 00 00 00 00 00 00 00 00 00 00 00 00 00
  // 00 00 12 00 AB 00`
  // 
  // * CloudKassir — длина строки: четная, от 8 до
  // 150 байт. То есть от 16 до 300 ASCII символов
  // ['0' - '9' , 'A' - 'F' ] шестнадцатеричного
  // представления кода маркировки товара.
  // 
  // Пример:
  // `303130323930303030630333435`
  // 
  // 
  // * OrangeData — строка, содержащая `base64`-
  // кодированный массив от 8 до 32 байт.
  // 
  // Пример:
  // `igQVAAADMTIzNDU2Nzg5MDEyMwAAAAAAAQ==`
  // 
  // Если в запросе передается параметр Ean13, не
  // прошедший валидацию, возвращается неуспешный
  // ответ с текстом ошибки в параметре `message` =
  // `Неверный параметр Ean13`.
  // 
  Ean13?: string
  // Код магазина. Для параметра `ShopСode`
  // нужно использовать значение параметра
  // `Submerchant_ID`, который возвращается в ответн при
  // регистрации магазинов через XML. Если XML не
  // используется, передавать поле не нужно.
  // 
  ShopCode?: string
  // Данные агента. Обязателен, если используется агентская схема.
  // 
  AgentData: {
    // `Тег ФФД: 1222`
    // 
    // 
    // Признак агента. Возможные значения:
    // * `bank_paying_agent` — банковский платежный агент,
    // * `bank_paying_subagent` — банковский платежный субагент,
    // * `paying_agent` — платежный агент,
    // * `paying_subagent` — платежный субагент,
    // * `attorney` — поверенный,
    // * `commission_agent` — комиссионер,
    // * `another` — другой тип агента.
    // 
    AgentSign?: string
    // `Тег ФФД: 1044`
    // 
    // 
    // Наименование операции.
    // Параметр обязательный, если `AgentSign` передан в значениях:
    // * `bank_paying_agent`,
    // * `bank_paying_subagent`.
    // 
    OperationName?: string
    Phones?: string[]
    // `Тег ФФД: 1026`
    // 
    // 
    // Наименование оператора перевода.
    // Параметр обязательный, если в `AgentSign` передан в значениях:
    // * `bank_paying_agent`,
    // * `bank_paying_subagent`.
    // 
    OperatorName?: string
    // `Тег ФФД: 1005`
    // 
    // 
    // Адрес оператора перевода.
    // Параметр обязательный, если в `AgentSign` передан в значениях:
    // * `bank_paying_agent`,
    // * `bank_paying_subagent`.
    // 
    OperatorAddress?: string
    // `Тег ФФД: 1016`
    // 
    // 
    // ИНН оператора перевода.
    // Параметр обязательный, если в `AgentSign` передан в значениях:
    // * `bank_paying_agent`,
    // * `bank_paying_subagent`.
    // 
    OperatorInn?: string
  }
  // Данные поставщика платежного агента. 
  // Обязателен, если передается значение `AgentSign` в объекте `AgentData`.
  // 
  SupplierInfo: {
    Phones?: string[]
    // `Тег ФФД: 1225`
    // 
    // 
    // Наименование поставщика.
    // Атрибут обязателен, если передается значение `AgentSign` 
    // в объекте `AgentData`.
    // Внимание: в эти 239 символов включаются телефоны поставщика 
    // + 4 символа на каждый телефон.
    // 
    // 
    // Например, если передано два телефона поставщика длиной 12 и 14 символов, 
    // максимальная длина наименования поставщика будет 
    // 239 – (12 + 4) – (14 + 4) = 205 символов.
    // 
    Name?: string
    // `Тег ФФД: 1226`
    // 
    // 
    // ИНН поставщика в формате `ЦЦЦЦЦЦЦЦЦЦ`.
    // Атрибут обязателен, если передается значение `AgentSign` 
    // в объекте `AgentData`.
    // 
    Inn?: string
  }
}
```

### #/components/schemas/Payments

```ts
// Детали платежа.
// 
// 
// Если объект не передан, автоматически указывается итоговая сумма чека с видом оплаты «Безналичный». 
//  
// Если передан объект `receipt.Payments`, значение в `Electronic` должно быть равно итоговому значению `Amount` в методе **Init**.
// При этом сумма введенных значений по всем видам оплат, включая `Electronic`, должна быть равна сумме (**Amount**) всех товаров,
// переданных в объекте `receipt.Items`.
// 
{
  // `Тег ФФД: 1031.`<br>
  // 
  // Вид оплаты «Наличные».
  // Сумма к оплате в копейках.
  // 
  Cash?: number
  // `Тег ФФД: 1081.`<br>
  // 
  // Вид оплаты «Безналичный».
  // 
  Electronic: number
  // `Тег ФФД: 1215.`<br>
  // 
  // Вид оплаты «Предварительная оплата (Аванс)».
  // 
  AdvancePayment?: number
  // `Тег ФФД: 1216.`<br>
  // 
  // Вид оплаты «Постоплата (Кредит)».
  // 
  Credit?: number
  // `Тег ФФД: 1217.`<br>
  // 
  // Вид оплаты «Иная форма оплаты».
  // 
  Provision?: number
}
```

### #/components/schemas/Receipt_FFD_105

```ts
// Объект с информацией о видах суммы платежа. Если объект не передан, автоматически указывается итоговая сумма чека с видом оплаты «Безналичная».
{
  Items: {
    // `Тег ФФД: 1030`
    // 
    // 
    // Наименование товара.
    // 
    Name: string
    // `Тег ФФД: 1078`
    // 
    // 
    // Цена в копейках.
    // 
    Price: number
    // `Тег ФФД: 1023`
    // 
    // 
    // Количество или вес товара.
    // Максимальное количество символов — 8, где целая часть — не больше 5 знаков, дробная — не больше 3 знаков для АТОЛ, 
    // и 2 знаков для CloudPayments.
    // 
    Quantity: number
    // `Тег ФФД: 1043`
    // 
    // 
    // Стоимость товара в копейках.
    // Произведение `Quantity` и `Price`.
    // 
    Amount: number
    // `Тег ФФД: 1214`
    // 
    // 
    // Возможные значения:
    // * `full_prepayment` — предоплата 100%,
    // * `prepayment` — предоплата,
    // * `advance` — аванс,
    // * `full_payment` — полный расчет,
    // * `partial_payment` — частичный расчет и кредит,
    // * `credit` — передача в кредит,
    // * `credit_payment` — оплата кредита.
    // 
    // 
    // Если значение не
    // передано, по умолчанию в онлайн-кассу
    // передается признак способа расчёта
    // `full_payment`.
    // 
    PaymentMethod?: enum[full_prepayment, prepayment, advance, full_payment, partial_payment, credit, credit_payment] //default: full_payment
    // `Тег ФФД: 1212`
    // 
    // 
    // Признак предмета расчета.
    // Возможные значения:
    // * `commodity` — товар,
    // * `excise` — подакцизный товар,
    // * `job` — работа,
    // * `service` — услуга,
    // * `gambling_bet` — ставка азартной игры,
    // * `gambling_prize` — выигрыш азартной игры,
    // * `lottery` — лотерейный билет,
    // * `lottery_prize` — выигрыш лотереи,
    // * `intellectual_activity` — предоставление результатов интеллектуальной деятельности,
    // * `payment` — платеж,
    // * `agent_commission` — агентское вознаграждение,
    // * `composite` — составной предмет расчета,
    // * `another` — иной предмет расчета,
    // 
    // 
    // Если значение не передано, по умолчанию в онлайн-кассу
    // отправляется признак предмета расчёта `commodity`.
    // 
    PaymentObject?: enum[commodity, excise, job, service, gambling_bet, gambling_prize, lottery, lottery_prize, intellectual_activity, payment, agent_commission, composite, another] //default: commodity
    // `Тег ФФД: 1199`
    // 
    // 
    // Ставка НДС.
    // Перечисление со значениями:
    // * `none` — без НДС,
    // * `vat0` — НДС по ставке 0%,
    // * `vat5` — НДС по ставке 5%,
    // * `vat7` — НДС по ставке 7%,
    // * `vat10` — НДС по ставке 10%,
    // * `vat20` — НДС по ставке 20%,
    // * `vat105` — НДС чека по расчетной ставке 5/105,
    // * `vat107` — НДС чека по расчетной ставке 7/107,
    // * `vat110` — НДС чека по расчетной ставке 10/110,
    // * `vat120` — НДС чека по расчетной ставке 20/120.
    // 
    Tax: enum[none, vat0, vat5, vat7, vat10, vat20, vat105, vat107, vat110, vat120]
    // `Тег ФФД: 1162`
    // 
    // 
    // Штрих-код в требуемом формате. В зависимости от
    // типа кассы требования могут отличаться:
    // * АТОЛ Онлайн — шестнадцатеричное
    // представление с пробелами. Максимальная
    // длина – 32 байта (^[a-fA-F0-9]{2}$)|(^([afA-F0-9]{2}\\s){1,31}[a-fA-F0-9]{2}$).
    // 
    // Пример:
    // `00 00 00 01 00 21 FA 41 00 23 05 41 00
    // 00 00 00 00 00 00 00 00 00 00 00 00 00
    // 00 00 12 00 AB 00`
    // 
    // * CloudKassir — длина строки: четная, от 8 до
    // 150 байт. То есть от 16 до 300 ASCII символов
    // ['0' - '9' , 'A' - 'F' ] шестнадцатеричного
    // представления кода маркировки товара.
    // 
    // Пример:
    // `303130323930303030630333435`
    // 
    // 
    // * OrangeData — строка, содержащая `base64`-
    // кодированный массив от 8 до 32 байт.
    // 
    // Пример:
    // `igQVAAADMTIzNDU2Nzg5MDEyMwAAAAAAAQ==`
    // 
    // Если в запросе передается параметр Ean13, не
    // прошедший валидацию, возвращается неуспешный
    // ответ с текстом ошибки в параметре `message` =
    // `Неверный параметр Ean13`.
    // 
    Ean13?: string
    // Код магазина. Для параметра `ShopСode`
    // нужно использовать значение параметра
    // `Submerchant_ID`, который возвращается в ответн при
    // регистрации магазинов через XML. Если XML не
    // используется, передавать поле не нужно.
    // 
    ShopCode?: string
    // Данные агента. Обязателен, если используется агентская схема.
    // 
    AgentData: {
      // `Тег ФФД: 1222`
      // 
      // 
      // Признак агента. Возможные значения:
      // * `bank_paying_agent` — банковский платежный агент,
      // * `bank_paying_subagent` — банковский платежный субагент,
      // * `paying_agent` — платежный агент,
      // * `paying_subagent` — платежный субагент,
      // * `attorney` — поверенный,
      // * `commission_agent` — комиссионер,
      // * `another` — другой тип агента.
      // 
      AgentSign?: string
      // `Тег ФФД: 1044`
      // 
      // 
      // Наименование операции.
      // Параметр обязательный, если `AgentSign` передан в значениях:
      // * `bank_paying_agent`,
      // * `bank_paying_subagent`.
      // 
      OperationName?: string
      Phones?: string[]
      // `Тег ФФД: 1026`
      // 
      // 
      // Наименование оператора перевода.
      // Параметр обязательный, если в `AgentSign` передан в значениях:
      // * `bank_paying_agent`,
      // * `bank_paying_subagent`.
      // 
      OperatorName?: string
      // `Тег ФФД: 1005`
      // 
      // 
      // Адрес оператора перевода.
      // Параметр обязательный, если в `AgentSign` передан в значениях:
      // * `bank_paying_agent`,
      // * `bank_paying_subagent`.
      // 
      OperatorAddress?: string
      // `Тег ФФД: 1016`
      // 
      // 
      // ИНН оператора перевода.
      // Параметр обязательный, если в `AgentSign` передан в значениях:
      // * `bank_paying_agent`,
      // * `bank_paying_subagent`.
      // 
      OperatorInn?: string
    }
    // Данные поставщика платежного агента. 
    // Обязателен, если передается значение `AgentSign` в объекте `AgentData`.
    // 
    SupplierInfo: {
      Phones?: string[]
      // `Тег ФФД: 1225`
      // 
      // 
      // Наименование поставщика.
      // Атрибут обязателен, если передается значение `AgentSign` 
      // в объекте `AgentData`.
      // Внимание: в эти 239 символов включаются телефоны поставщика 
      // + 4 символа на каждый телефон.
      // 
      // 
      // Например, если передано два телефона поставщика длиной 12 и 14 символов, 
      // максимальная длина наименования поставщика будет 
      // 239 – (12 + 4) – (14 + 4) = 205 символов.
      // 
      Name?: string
      // `Тег ФФД: 1226`
      // 
      // 
      // ИНН поставщика в формате `ЦЦЦЦЦЦЦЦЦЦ`.
      // Атрибут обязателен, если передается значение `AgentSign` 
      // в объекте `AgentData`.
      // 
      Inn?: string
    }
  }[]
  // Версия ФФД. Возможные значения:
  // * FfdVersion: `1.2`,
  // * FfdVersion: `1.05`.
  // 
  // Версия ФФД по умолчанию — `1.05`.
  // 
  FfdVersion?: string //default: 1.05
  // `Тег ФФД: 1008.`<br>
  // 
  // Электронная почта клиента.
  // Параметр должен быть заполнен, если не передано значение 
  // в параметре `Phone`.
  // 
  Email?: string
  // `Тег ФФД: 1008.`<br>
  // 
  // Телефон клиента в формате `+{Ц}`.
  // Параметр должен быть заполнен, если не передано значение 
  // в параметре `Email`.
  // 
  Phone?: string
  // `Тег ФФД: 1055.`
  // 
  // 
  // Система налогообложения. Возможные значения:
  // * `osn` — общая СН;
  // * `usn_income` — упрощенная СН (доходы);
  // * `usn_income_outcome` — упрощенная СН (доходы минус расходы);
  // * `esn` — единый сельскохозяйственный налог;
  // * `patent` — патентная СН.
  // 
  Taxation: enum[osn, usn_income, usn_income_outcome, esn, patent]
  // Детали платежа.
  // 
  // 
  // Если объект не передан, автоматически указывается итоговая сумма чека с видом оплаты «Безналичный». 
  //  
  // Если передан объект `receipt.Payments`, значение в `Electronic` должно быть равно итоговому значению `Amount` в методе **Init**.
  // При этом сумма введенных значений по всем видам оплат, включая `Electronic`, должна быть равна сумме (**Amount**) всех товаров,
  // переданных в объекте `receipt.Items`.
  // 
  Payments: {
    // `Тег ФФД: 1031.`<br>
    // 
    // Вид оплаты «Наличные».
    // Сумма к оплате в копейках.
    // 
    Cash?: number
    // `Тег ФФД: 1081.`<br>
    // 
    // Вид оплаты «Безналичный».
    // 
    Electronic: number
    // `Тег ФФД: 1215.`<br>
    // 
    // Вид оплаты «Предварительная оплата (Аванс)».
    // 
    AdvancePayment?: number
    // `Тег ФФД: 1216.`<br>
    // 
    // Вид оплаты «Постоплата (Кредит)».
    // 
    Credit?: number
    // `Тег ФФД: 1217.`<br>
    // 
    // Вид оплаты «Иная форма оплаты».
    // 
    Provision?: number
  }
}
```

### #/components/schemas/ClientInfo

```ts
// Информация по клиенту.
// 
{
  // `Тег ФФД: 1243`
  // 
  // 
  // Дата рождения клиента в формате `ДД.ММ.ГГГГ`.
  // 
  Birthdate?: string
  // `Тег ФФД: 1244`
  // 
  // 
  // Числовой код страны, гражданином которой является
  // клиент. Код страны указывается в соответствии с
  // Общероссийским классификатором стран мира [ОКСМ](https://classifikators.ru/oksm).
  // 
  Citizenship?: string
  // `Тег ФФД: 1245`
  // 
  // 
  // Числовой код вида документа, удостоверяющего
  // личность.
  // 
  // Может принимать только следующие значения:
  // 
  // |Код|Описание|
  // |---|---|
  // | 21 | Паспорт гражданина Российской Федерации|
  // | 22 | Паспорт гражданина Российской Федерации, дипломатический паспорт, служебный паспорт, удостоверяющие личность гражданина Российской Федерации за пределами Российской Федерации|
  // | 26 | Временное удостоверение личности гражданина Российской Федерации, выдаваемое на период оформления паспорта гражданина Российской Федерации|
  // | 27 | Свидетельство о рождении гражданина Российской Федерации — для граждан Российской Федерации в возрасте до 14 лет|
  // | 28 | Иные документы, признаваемые документами, удостоверяющими личность гражданина Российской Федерации в соответствии с законодательством Российской Федерации|
  // | 31 | Паспорт иностранного гражданина|
  // | 32 | Иные документы, признаваемые документами, удостоверяющими личность иностранного гражданина в соответствии с законодательством Российской Федерации и международным договором Российской Федерации|
  // | 33 | Документ, выданный иностранным государством и признаваемый в соответствии с международным договором Российской Федерации в качестве документа, удостоверяющего личность лица безгражданства.|
  // | 34 | Вид на жительство — для лиц без гражданства|
  // | 35 | Разрешение на временное проживание — для лиц без гражданства|
  // | 36 | Свидетельство о рассмотрении ходатайства о признании лица без гражданства беженцем на территории Российской Федерации по существу|
  // | 37 | Удостоверение беженца|
  // | 38 | Иные документы, признаваемые документами, удостоверяющими личность лиц без гражданства в соответствии с законодательством Российской Федерации и международным договором Российской Федерации|
  // | 40 | Документ, удостоверяющий личность лица, не имеющего действительного документа, удостоверяющего личность, на период рассмотрения заявления о признании гражданином Российской Федерации или о приеме в гражданство Российской Федерации|
  // 
  DocumentСode?: string
  // `Тег ФФД: 1246`
  // 
  // 
  // Реквизиты документа, удостоверяющего личность — например, серия и номер паспорта.
  // 
  DocumentData?: string
  // `Тег ФФД: 1254`
  // 
  // 
  // Адрес клиента-грузополучателя.
  // 
  Address?: string
}
```

### #/components/schemas/MarkCode

```ts
// Код маркировки в машиночитаемой форме,
// представленный в виде одного из видов кодов,
// формируемых в соответствии с требованиями,
// предусмотренными правилами, для нанесения
// на потребительскую упаковку, или на товары,
// или на товарный ярлык
// 
// 
// Включается в чек, если предметом расчета является товар, подлежащий обязательной маркировке средством идентификации — соответствующий 
// код в поле `paymentObject`.
// 
{
  // Тип штрих кода.
  // Возможные значения:
  // * `UNKNOWN` — код товара, формат которого не
  // идентифицирован, как один из реквизитов;
  // * `EAN8` — код товара в формате EAN-8;
  // * `EAN13` — код товара в формате EAN-13;
  // * `ITF14` — код товара в формате ITF-14;
  // * `GS10` — код товара в формате GS1,
  // нанесенный на товар, не подлежащий
  // маркировке;
  // * `GS1M` — код товара в формате GS1,
  // нанесенный на товар, подлежащий
  // маркировке;
  // * `SHORT` — код товара в формате короткого кода
  // маркировки, нанесенный на товар;
  // * `FUR` — контрольно-идентификационный знак
  // мехового изделия;
  // * `EGAIS20` — код товара в формате ЕГАИС-2.0;
  // * `EGAIS30` — код товара в формате ЕГАИС-3.0;
  // * `RAWCODE` — код маркировки, как он был прочитан сканером.
  // 
  MarkCodeType: string
  // Код маркировки.
  // 
  Value: string
}
```

### #/components/schemas/MarkQuantity

```ts
// Реквизит «дробное количество маркированного товара».
// Передается, только если расчет осуществляется 
// за маркированный товар — соответствующий код в поле 
// `paymentObject`, и значение в поле `measurementUnit` 
// равно `0`.
// 
// `MarkQuantity` не является обязательным объектом, в том числе для товаров с маркировкой. Этот объект можно передавать, 
//  если товар с маркировкой. То есть даже при ФФД 1.2 этот объект не является обязательным.
//  
// 
// Пример: 
// ```
//       {
//       "numenator": "1"
//       "denominator" "2"  
//       }
// ```
// 
{
  // `Тег ФФД: 1293`
  // 
  // 
  // Числитель дробной части предмета расчета. 
  // Значение должно быть строго меньше
  // значения реквизита «знаменатель».
  // 
  Numerator?: number
  // `Тег ФФД: 1294`
  // 
  // 
  // Знаменатель дробной части предмета расчета. 
  // Значение равно количеству товара в партии (упаковке), 
  // имеющей общий код маркировки товара.
  // 
  Denominator?: number
}
```

### #/components/schemas/SectoralItemProps

```ts
// Отраслевой реквизит предмета расчета. Указывается только для товаров подлежащих обязательной маркировке средством
// идентификации. Включение этого реквизита предусмотрено НПА отраслевого регулирования для
// соответствующей товарной группы.
// 
{
  // `Тег ФФД: 1262`
  // 
  // 
  // Идентификатор ФОИВ — федеральный орган
  // исполнительной власти.
  // 
  FederalId: string
  // `Тег ФФД: 1263`
  // 
  // 
  // Дата нормативного акта ФОИВ.
  // 
  Date: string
  // `Тег ФФД: 1264`
  // 
  // 
  // Номер нормативного акта ФОИВ.
  // 
  Number: string
  // `Тег ФФД: 1265`
  // 
  // 
  // Состав значений, определенных нормативным актом ФОИВ.
  // 
  Value: string
}
```

### #/components/schemas/Items_FFD_12

```ts
{
  // Данные агента. Обязателен, если используется агентская схема.
  // 
  AgentData: {
    // `Тег ФФД: 1222`
    // 
    // 
    // Признак агента. Возможные значения:
    // * `bank_paying_agent` — банковский платежный агент,
    // * `bank_paying_subagent` — банковский платежный субагент,
    // * `paying_agent` — платежный агент,
    // * `paying_subagent` — платежный субагент,
    // * `attorney` — поверенный,
    // * `commission_agent` — комиссионер,
    // * `another` — другой тип агента.
    // 
    AgentSign?: string
    // `Тег ФФД: 1044`
    // 
    // 
    // Наименование операции.
    // Параметр обязательный, если `AgentSign` передан в значениях:
    // * `bank_paying_agent`,
    // * `bank_paying_subagent`.
    // 
    OperationName?: string
    Phones?: string[]
    // `Тег ФФД: 1026`
    // 
    // 
    // Наименование оператора перевода.
    // Параметр обязательный, если в `AgentSign` передан в значениях:
    // * `bank_paying_agent`,
    // * `bank_paying_subagent`.
    // 
    OperatorName?: string
    // `Тег ФФД: 1005`
    // 
    // 
    // Адрес оператора перевода.
    // Параметр обязательный, если в `AgentSign` передан в значениях:
    // * `bank_paying_agent`,
    // * `bank_paying_subagent`.
    // 
    OperatorAddress?: string
    // `Тег ФФД: 1016`
    // 
    // 
    // ИНН оператора перевода.
    // Параметр обязательный, если в `AgentSign` передан в значениях:
    // * `bank_paying_agent`,
    // * `bank_paying_subagent`.
    // 
    OperatorInn?: string
  }
  // Данные поставщика платежного агента. 
  // Обязателен, если передается значение `AgentSign` в объекте `AgentData`.
  // 
  SupplierInfo: {
    Phones?: string[]
    // `Тег ФФД: 1225`
    // 
    // 
    // Наименование поставщика.
    // Атрибут обязателен, если передается значение `AgentSign` 
    // в объекте `AgentData`.
    // Внимание: в эти 239 символов включаются телефоны поставщика 
    // + 4 символа на каждый телефон.
    // 
    // 
    // Например, если передано два телефона поставщика длиной 12 и 14 символов, 
    // максимальная длина наименования поставщика будет 
    // 239 – (12 + 4) – (14 + 4) = 205 символов.
    // 
    Name?: string
    // `Тег ФФД: 1226`
    // 
    // 
    // ИНН поставщика в формате `ЦЦЦЦЦЦЦЦЦЦ`.
    // Атрибут обязателен, если передается значение `AgentSign` 
    // в объекте `AgentData`.
    // 
    Inn?: string
  }
  // `Тег ФФД: 1030`
  // 
  // 
  // Наименование товара.
  // 
  Name: string
  // `Тег ФФД: 1079`
  // 
  // 
  // Цена в копейках.
  // 
  Price: number
  // `Тег ФФД: 1023`
  // 
  // 
  // Количество или вес товара.
  // * Максимальное количество символов — 8, где целая часть — не больше 5 знаков, дробная — не больше 3 знаков для Атол и
  // 2 знаков для CloudPayments.
  // * Значение `1`, если передан объект `MarkCode`.
  // 
  Quantity: number
  // `Тег ФФД: 1043`
  // 
  // 
  // Стоимость товара в копейках.
  // Произведение `Quantity` и `Price`.
  // 
  Amount: number
  // `Тег ФФД: 1199`
  // 
  // 
  // Ставка НДС.
  // Возможные значения:
  // * `none` — без НДС,
  // * `vat0` — НДС по ставке 0%;
  // * `vat5` — НДС по ставке 5%;
  // * `vat7` — НДС по ставке 7%;
  // * `vat10` — НДС по ставке 10%;
  // * `vat20` — НДС по ставке 20%;
  // * `vat105` — НДС чека по расчетной ставке 5/105;
  // * `vat107` — НДС чека по расчетной ставке 7/107;
  // * `vat110` — НДС чека по расчетной ставке 10/110;
  // * `vat120` — НДС чека по расчетной ставке 20/120.
  // 
  Tax: enum[none, vat0, vat5, vat7, vat10, vat20, vat105, vat107, vat110, vat120]
  // `Тег ФФД: 1214`
  // 
  //  
  // Возможные значения:
  //  * `full_prepayment` — предоплата 100%,
  //  * `prepayment` — предоплата,
  //  * `advance` — аванс,
  //  * `full_payment` — полный расчет,
  //  * `partial_payment` — частичный расчет и кредит,
  //  * `credit` — передача в кредит,
  //  * `credit_payment` — оплата кредита. 
  // 
  // 
  // Если значение не передано, по умолчанию в онлайн-кассу
  // передается признак способа расчёта
  // `full_payment`.
  // 
  PaymentMethod: enum[full_prepayment, prepayment, advance, full_payment, partial_payment, credit, credit_payment]
  // `Тег ФФД: 1212`
  // 
  // 
  // Значения реквизита «признак предмета расчета» — тег 1212, таблица 101.
  // Возможные значения:
  // * `commodity` — товар,
  // * `excise` — подакцизный товар,
  // * `job` — работа,
  // * `service` — услуга,
  // * `gambling_bet` — ставка азартной игры,
  // * `gambling_prize` — выигрыш азартной игры,
  // * `lottery` — лотерейный билет,
  // * `lottery_prize` — выигрыш лотереи,
  // * `intellectual_activity` — предоставление,
  //   результатов интеллектуальной деятельности,
  // * `payment` — платеж,
  // * `agent_commission` — агентское
  //   вознаграждение,
  // * `contribution` — выплата,
  // * `property_rights` — имущественное право,
  // * `unrealization` — внереализационный доход,
  // * `tax_reduction` — иные платежи и взносы,
  // * `trade_fee` — торговый сбор,
  // * `resort_tax` — курортный сбор,
  // * `pledge` — залог,
  // * `income_decrease` — расход,
  // * `ie_pension_insurance_without_payments` — взносы на ОПС ИП,
  // * `ie_pension_insurance_with_payments` — взносы на ОПС,
  // * `ie_medical_insurance_without_payments` — взносы на ОМС ИП,
  // * `ie_medical_insurance_with_payments` — взносы на ОМС,
  // * `social_insurance` — взносы на ОСС,
  // * `casino_chips` — платеж казино,
  // * `agent_payment` — выдача ДС,
  // * `excisable_goods_without_marking_code` — АТНМ,
  // * `excisable_goods_with_marking_code` — АТМ,
  // * `goods_without_marking_code` — ТНМ,
  // * `goods_with_marking_code` — ТМ,
  // * `another` — иной предмет расчета.
  // 
  PaymentObject: enum[commodity, excise, job, service, gambling_bet, gambling_prize, lottery, lottery_prize, intellectual_activity, payment, agent_commission, contribution, property_rights, unrealization, tax_reduction, trade_fee, resort_tax, pledge, income_decrease, ie_pension_insurance_without_payments, ie_pension_insurance_with_payments, ie_medical_insurance_without_payments, ie_medical_insurance_with_payments, social_insurance, casino_chips, agent_payment, excisable_goods_without_marking_code, excisable_goods_with_marking_code, goods_without_marking_code, goods_with_marking_code, another]
  // `Тег ФФД: 1191`
  // 
  // 
  // Дополнительный реквизит предмета расчета.
  // 
  UserData?: string
  // `Тег ФФД: 1229`
  // 
  // 
  // Сумма акциза в рублях с учетом копеек,
  // включенная в стоимость предмета расчета:
  // * целая часть — не больше 8 знаков;
  // * дробная часть — не больше 2 знаков;
  // * значение не может быть отрицательным.
  // 
  Excise?: string
  // `Тег ФФД: 1230`
  // 
  // 
  // Цифровой код страны происхождения товара в
  // соответствии с Общероссийским
  // классификатором стран мира — 3 цифры.
  // 
  CountryCode?: string
  // `Тег ФФД: 1231`
  // 
  // 
  // Номер таможенной декларации.
  // 
  DeclarationNumber?: string
  // `Тег ФФД: 2108`
  // 
  // 
  // Единицы измерения.
  // 
  // 
  // Возможные варианты описаны в разделе<a href="https://www.tbank.ru/kassa/dev/payments/#tag/Opisanie-dopolnitelnyh-obuektov" target="_blank"> дополнительных объектов</a>. Также возможна передача произвольных значений.
  // 
  // `MeasurementUnit` обязателен, если версия ФД онлайн-кассы — 1.2.
  // 
  MeasurementUnit: string
  // `Тег ФФД: 2102`
  // 
  // 
  // Режим обработки кода маркировки.
  // Должен принимать значение, равное `0`.
  // Включается в чек , если предметом расчета 
  // является товар, подлежащий обязательной
  // маркировке средством идентификации — соответствующий код в поле `paymentObject`.
  // 
  MarkProcessingMode?: string
  // Код маркировки в машиночитаемой форме,
  // представленный в виде одного из видов кодов,
  // формируемых в соответствии с требованиями,
  // предусмотренными правилами, для нанесения
  // на потребительскую упаковку, или на товары,
  // или на товарный ярлык
  // 
  // 
  // Включается в чек, если предметом расчета является товар, подлежащий обязательной маркировке средством идентификации — соответствующий 
  // код в поле `paymentObject`.
  // 
  MarkCode: {
    // Тип штрих кода.
    // Возможные значения:
    // * `UNKNOWN` — код товара, формат которого не
    // идентифицирован, как один из реквизитов;
    // * `EAN8` — код товара в формате EAN-8;
    // * `EAN13` — код товара в формате EAN-13;
    // * `ITF14` — код товара в формате ITF-14;
    // * `GS10` — код товара в формате GS1,
    // нанесенный на товар, не подлежащий
    // маркировке;
    // * `GS1M` — код товара в формате GS1,
    // нанесенный на товар, подлежащий
    // маркировке;
    // * `SHORT` — код товара в формате короткого кода
    // маркировки, нанесенный на товар;
    // * `FUR` — контрольно-идентификационный знак
    // мехового изделия;
    // * `EGAIS20` — код товара в формате ЕГАИС-2.0;
    // * `EGAIS30` — код товара в формате ЕГАИС-3.0;
    // * `RAWCODE` — код маркировки, как он был прочитан сканером.
    // 
    MarkCodeType: string
    // Код маркировки.
    // 
    Value: string
  }
  // Реквизит «дробное количество маркированного товара».
  // Передается, только если расчет осуществляется 
  // за маркированный товар — соответствующий код в поле 
  // `paymentObject`, и значение в поле `measurementUnit` 
  // равно `0`.
  // 
  // `MarkQuantity` не является обязательным объектом, в том числе для товаров с маркировкой. Этот объект можно передавать, 
  //  если товар с маркировкой. То есть даже при ФФД 1.2 этот объект не является обязательным.
  //  
  // 
  // Пример: 
  // ```
  //       {
  //       "numenator": "1"
  //       "denominator" "2"  
  //       }
  // ```
  // 
  MarkQuantity: {
    // `Тег ФФД: 1293`
    // 
    // 
    // Числитель дробной части предмета расчета. 
    // Значение должно быть строго меньше
    // значения реквизита «знаменатель».
    // 
    Numerator?: number
    // `Тег ФФД: 1294`
    // 
    // 
    // Знаменатель дробной части предмета расчета. 
    // Значение равно количеству товара в партии (упаковке), 
    // имеющей общий код маркировки товара.
    // 
    Denominator?: number
  }
  // Отраслевой реквизит предмета расчета. Указывается только для товаров подлежащих обязательной маркировке средством
  // идентификации. Включение этого реквизита предусмотрено НПА отраслевого регулирования для
  // соответствующей товарной группы.
  // 
  SectoralItemProps: {
    // `Тег ФФД: 1262`
    // 
    // 
    // Идентификатор ФОИВ — федеральный орган
    // исполнительной власти.
    // 
    FederalId: string
    // `Тег ФФД: 1263`
    // 
    // 
    // Дата нормативного акта ФОИВ.
    // 
    Date: string
    // `Тег ФФД: 1264`
    // 
    // 
    // Номер нормативного акта ФОИВ.
    // 
    Number: string
    // `Тег ФФД: 1265`
    // 
    // 
    // Состав значений, определенных нормативным актом ФОИВ.
    // 
    Value: string
  }[]
}
```

### #/components/schemas/Receipt_FFD_12

```ts
// Объект с информацией о видах суммы платежа. Если объект не передан, автоматически указывается итоговая сумма чека с видом оплаты «Безналичная».
{
  // Версия ФФД.
  // Возможные значения:
  // * FfdVersion: 1.2,
  // * FfdVersion: 1.05.
  // 
  FfdVersion: string
  // Информация по клиенту.
  // 
  ClientInfo: {
    // `Тег ФФД: 1243`
    // 
    // 
    // Дата рождения клиента в формате `ДД.ММ.ГГГГ`.
    // 
    Birthdate?: string
    // `Тег ФФД: 1244`
    // 
    // 
    // Числовой код страны, гражданином которой является
    // клиент. Код страны указывается в соответствии с
    // Общероссийским классификатором стран мира [ОКСМ](https://classifikators.ru/oksm).
    // 
    Citizenship?: string
    // `Тег ФФД: 1245`
    // 
    // 
    // Числовой код вида документа, удостоверяющего
    // личность.
    // 
    // Может принимать только следующие значения:
    // 
    // |Код|Описание|
    // |---|---|
    // | 21 | Паспорт гражданина Российской Федерации|
    // | 22 | Паспорт гражданина Российской Федерации, дипломатический паспорт, служебный паспорт, удостоверяющие личность гражданина Российской Федерации за пределами Российской Федерации|
    // | 26 | Временное удостоверение личности гражданина Российской Федерации, выдаваемое на период оформления паспорта гражданина Российской Федерации|
    // | 27 | Свидетельство о рождении гражданина Российской Федерации — для граждан Российской Федерации в возрасте до 14 лет|
    // | 28 | Иные документы, признаваемые документами, удостоверяющими личность гражданина Российской Федерации в соответствии с законодательством Российской Федерации|
    // | 31 | Паспорт иностранного гражданина|
    // | 32 | Иные документы, признаваемые документами, удостоверяющими личность иностранного гражданина в соответствии с законодательством Российской Федерации и международным договором Российской Федерации|
    // | 33 | Документ, выданный иностранным государством и признаваемый в соответствии с международным договором Российской Федерации в качестве документа, удостоверяющего личность лица безгражданства.|
    // | 34 | Вид на жительство — для лиц без гражданства|
    // | 35 | Разрешение на временное проживание — для лиц без гражданства|
    // | 36 | Свидетельство о рассмотрении ходатайства о признании лица без гражданства беженцем на территории Российской Федерации по существу|
    // | 37 | Удостоверение беженца|
    // | 38 | Иные документы, признаваемые документами, удостоверяющими личность лиц без гражданства в соответствии с законодательством Российской Федерации и международным договором Российской Федерации|
    // | 40 | Документ, удостоверяющий личность лица, не имеющего действительного документа, удостоверяющего личность, на период рассмотрения заявления о признании гражданином Российской Федерации или о приеме в гражданство Российской Федерации|
    // 
    DocumentСode?: string
    // `Тег ФФД: 1246`
    // 
    // 
    // Реквизиты документа, удостоверяющего личность — например, серия и номер паспорта.
    // 
    DocumentData?: string
    // `Тег ФФД: 1254`
    // 
    // 
    // Адрес клиента-грузополучателя.
    // 
    Address?: string
  }
  // `Тег ФФД: 1055`
  // 
  // 
  // Система налогообложения. Возможные значения:
  // * `osn` — общая СН,
  // * `usn_income` — упрощенная СН (доходы),
  // * `usn_income_outcome` — упрощенная СН (доходы минус расходы),
  // * `esn` — единый сельскохозяйственный налог,
  // * `patent` — патентная СН.
  // 
  Taxation: enum[osn, usn_income, usn_income_outcome, esn, patent]
  // `Тег ФФД: 1008`
  // 
  // 
  // Электронная почта клиента.
  // Параметр должен быть заполнен, если не передано значение 
  // в параметре `Phone`.
  // 
  Email?: string
  // `Тег ФФД: 1008`
  // 
  // 
  // Телефон клиента в формате `+{Ц}`.
  // Параметр должен быть заполнен, если не передано значение 
  // в параметре `Email`.
  // 
  Phone?: string
  // `Тег ФФД: 1227`
  // 
  // 
  // Идентификатор/имя клиента.
  // 
  Customer?: string
  // `Тег ФФД: 1228`
  // 
  // 
  // ИНН клиента.
  // 
  CustomerInn?: string
  Items: {
    // Данные агента. Обязателен, если используется агентская схема.
    // 
    AgentData: {
      // `Тег ФФД: 1222`
      // 
      // 
      // Признак агента. Возможные значения:
      // * `bank_paying_agent` — банковский платежный агент,
      // * `bank_paying_subagent` — банковский платежный субагент,
      // * `paying_agent` — платежный агент,
      // * `paying_subagent` — платежный субагент,
      // * `attorney` — поверенный,
      // * `commission_agent` — комиссионер,
      // * `another` — другой тип агента.
      // 
      AgentSign?: string
      // `Тег ФФД: 1044`
      // 
      // 
      // Наименование операции.
      // Параметр обязательный, если `AgentSign` передан в значениях:
      // * `bank_paying_agent`,
      // * `bank_paying_subagent`.
      // 
      OperationName?: string
      Phones?: string[]
      // `Тег ФФД: 1026`
      // 
      // 
      // Наименование оператора перевода.
      // Параметр обязательный, если в `AgentSign` передан в значениях:
      // * `bank_paying_agent`,
      // * `bank_paying_subagent`.
      // 
      OperatorName?: string
      // `Тег ФФД: 1005`
      // 
      // 
      // Адрес оператора перевода.
      // Параметр обязательный, если в `AgentSign` передан в значениях:
      // * `bank_paying_agent`,
      // * `bank_paying_subagent`.
      // 
      OperatorAddress?: string
      // `Тег ФФД: 1016`
      // 
      // 
      // ИНН оператора перевода.
      // Параметр обязательный, если в `AgentSign` передан в значениях:
      // * `bank_paying_agent`,
      // * `bank_paying_subagent`.
      // 
      OperatorInn?: string
    }
    // Данные поставщика платежного агента. 
    // Обязателен, если передается значение `AgentSign` в объекте `AgentData`.
    // 
    SupplierInfo: {
      Phones?: string[]
      // `Тег ФФД: 1225`
      // 
      // 
      // Наименование поставщика.
      // Атрибут обязателен, если передается значение `AgentSign` 
      // в объекте `AgentData`.
      // Внимание: в эти 239 символов включаются телефоны поставщика 
      // + 4 символа на каждый телефон.
      // 
      // 
      // Например, если передано два телефона поставщика длиной 12 и 14 символов, 
      // максимальная длина наименования поставщика будет 
      // 239 – (12 + 4) – (14 + 4) = 205 символов.
      // 
      Name?: string
      // `Тег ФФД: 1226`
      // 
      // 
      // ИНН поставщика в формате `ЦЦЦЦЦЦЦЦЦЦ`.
      // Атрибут обязателен, если передается значение `AgentSign` 
      // в объекте `AgentData`.
      // 
      Inn?: string
    }
    // `Тег ФФД: 1030`
    // 
    // 
    // Наименование товара.
    // 
    Name: string
    // `Тег ФФД: 1079`
    // 
    // 
    // Цена в копейках.
    // 
    Price: number
    // `Тег ФФД: 1023`
    // 
    // 
    // Количество или вес товара.
    // * Максимальное количество символов — 8, где целая часть — не больше 5 знаков, дробная — не больше 3 знаков для Атол и
    // 2 знаков для CloudPayments.
    // * Значение `1`, если передан объект `MarkCode`.
    // 
    Quantity: number
    // `Тег ФФД: 1043`
    // 
    // 
    // Стоимость товара в копейках.
    // Произведение `Quantity` и `Price`.
    // 
    Amount: number
    // `Тег ФФД: 1199`
    // 
    // 
    // Ставка НДС.
    // Возможные значения:
    // * `none` — без НДС,
    // * `vat0` — НДС по ставке 0%;
    // * `vat5` — НДС по ставке 5%;
    // * `vat7` — НДС по ставке 7%;
    // * `vat10` — НДС по ставке 10%;
    // * `vat20` — НДС по ставке 20%;
    // * `vat105` — НДС чека по расчетной ставке 5/105;
    // * `vat107` — НДС чека по расчетной ставке 7/107;
    // * `vat110` — НДС чека по расчетной ставке 10/110;
    // * `vat120` — НДС чека по расчетной ставке 20/120.
    // 
    Tax: enum[none, vat0, vat5, vat7, vat10, vat20, vat105, vat107, vat110, vat120]
    // `Тег ФФД: 1214`
    // 
    //  
    // Возможные значения:
    //  * `full_prepayment` — предоплата 100%,
    //  * `prepayment` — предоплата,
    //  * `advance` — аванс,
    //  * `full_payment` — полный расчет,
    //  * `partial_payment` — частичный расчет и кредит,
    //  * `credit` — передача в кредит,
    //  * `credit_payment` — оплата кредита. 
    // 
    // 
    // Если значение не передано, по умолчанию в онлайн-кассу
    // передается признак способа расчёта
    // `full_payment`.
    // 
    PaymentMethod: enum[full_prepayment, prepayment, advance, full_payment, partial_payment, credit, credit_payment]
    // `Тег ФФД: 1212`
    // 
    // 
    // Значения реквизита «признак предмета расчета» — тег 1212, таблица 101.
    // Возможные значения:
    // * `commodity` — товар,
    // * `excise` — подакцизный товар,
    // * `job` — работа,
    // * `service` — услуга,
    // * `gambling_bet` — ставка азартной игры,
    // * `gambling_prize` — выигрыш азартной игры,
    // * `lottery` — лотерейный билет,
    // * `lottery_prize` — выигрыш лотереи,
    // * `intellectual_activity` — предоставление,
    //   результатов интеллектуальной деятельности,
    // * `payment` — платеж,
    // * `agent_commission` — агентское
    //   вознаграждение,
    // * `contribution` — выплата,
    // * `property_rights` — имущественное право,
    // * `unrealization` — внереализационный доход,
    // * `tax_reduction` — иные платежи и взносы,
    // * `trade_fee` — торговый сбор,
    // * `resort_tax` — курортный сбор,
    // * `pledge` — залог,
    // * `income_decrease` — расход,
    // * `ie_pension_insurance_without_payments` — взносы на ОПС ИП,
    // * `ie_pension_insurance_with_payments` — взносы на ОПС,
    // * `ie_medical_insurance_without_payments` — взносы на ОМС ИП,
    // * `ie_medical_insurance_with_payments` — взносы на ОМС,
    // * `social_insurance` — взносы на ОСС,
    // * `casino_chips` — платеж казино,
    // * `agent_payment` — выдача ДС,
    // * `excisable_goods_without_marking_code` — АТНМ,
    // * `excisable_goods_with_marking_code` — АТМ,
    // * `goods_without_marking_code` — ТНМ,
    // * `goods_with_marking_code` — ТМ,
    // * `another` — иной предмет расчета.
    // 
    PaymentObject: enum[commodity, excise, job, service, gambling_bet, gambling_prize, lottery, lottery_prize, intellectual_activity, payment, agent_commission, contribution, property_rights, unrealization, tax_reduction, trade_fee, resort_tax, pledge, income_decrease, ie_pension_insurance_without_payments, ie_pension_insurance_with_payments, ie_medical_insurance_without_payments, ie_medical_insurance_with_payments, social_insurance, casino_chips, agent_payment, excisable_goods_without_marking_code, excisable_goods_with_marking_code, goods_without_marking_code, goods_with_marking_code, another]
    // `Тег ФФД: 1191`
    // 
    // 
    // Дополнительный реквизит предмета расчета.
    // 
    UserData?: string
    // `Тег ФФД: 1229`
    // 
    // 
    // Сумма акциза в рублях с учетом копеек,
    // включенная в стоимость предмета расчета:
    // * целая часть — не больше 8 знаков;
    // * дробная часть — не больше 2 знаков;
    // * значение не может быть отрицательным.
    // 
    Excise?: string
    // `Тег ФФД: 1230`
    // 
    // 
    // Цифровой код страны происхождения товара в
    // соответствии с Общероссийским
    // классификатором стран мира — 3 цифры.
    // 
    CountryCode?: string
    // `Тег ФФД: 1231`
    // 
    // 
    // Номер таможенной декларации.
    // 
    DeclarationNumber?: string
    // `Тег ФФД: 2108`
    // 
    // 
    // Единицы измерения.
    // 
    // 
    // Возможные варианты описаны в разделе<a href="https://www.tbank.ru/kassa/dev/payments/#tag/Opisanie-dopolnitelnyh-obuektov" target="_blank"> дополнительных объектов</a>. Также возможна передача произвольных значений.
    // 
    // `MeasurementUnit` обязателен, если версия ФД онлайн-кассы — 1.2.
    // 
    MeasurementUnit: string
    // `Тег ФФД: 2102`
    // 
    // 
    // Режим обработки кода маркировки.
    // Должен принимать значение, равное `0`.
    // Включается в чек , если предметом расчета 
    // является товар, подлежащий обязательной
    // маркировке средством идентификации — соответствующий код в поле `paymentObject`.
    // 
    MarkProcessingMode?: string
    // Код маркировки в машиночитаемой форме,
    // представленный в виде одного из видов кодов,
    // формируемых в соответствии с требованиями,
    // предусмотренными правилами, для нанесения
    // на потребительскую упаковку, или на товары,
    // или на товарный ярлык
    // 
    // 
    // Включается в чек, если предметом расчета является товар, подлежащий обязательной маркировке средством идентификации — соответствующий 
    // код в поле `paymentObject`.
    // 
    MarkCode: {
      // Тип штрих кода.
      // Возможные значения:
      // * `UNKNOWN` — код товара, формат которого не
      // идентифицирован, как один из реквизитов;
      // * `EAN8` — код товара в формате EAN-8;
      // * `EAN13` — код товара в формате EAN-13;
      // * `ITF14` — код товара в формате ITF-14;
      // * `GS10` — код товара в формате GS1,
      // нанесенный на товар, не подлежащий
      // маркировке;
      // * `GS1M` — код товара в формате GS1,
      // нанесенный на товар, подлежащий
      // маркировке;
      // * `SHORT` — код товара в формате короткого кода
      // маркировки, нанесенный на товар;
      // * `FUR` — контрольно-идентификационный знак
      // мехового изделия;
      // * `EGAIS20` — код товара в формате ЕГАИС-2.0;
      // * `EGAIS30` — код товара в формате ЕГАИС-3.0;
      // * `RAWCODE` — код маркировки, как он был прочитан сканером.
      // 
      MarkCodeType: string
      // Код маркировки.
      // 
      Value: string
    }
    // Реквизит «дробное количество маркированного товара».
    // Передается, только если расчет осуществляется 
    // за маркированный товар — соответствующий код в поле 
    // `paymentObject`, и значение в поле `measurementUnit` 
    // равно `0`.
    // 
    // `MarkQuantity` не является обязательным объектом, в том числе для товаров с маркировкой. Этот объект можно передавать, 
    //  если товар с маркировкой. То есть даже при ФФД 1.2 этот объект не является обязательным.
    //  
    // 
    // Пример: 
    // ```
    //       {
    //       "numenator": "1"
    //       "denominator" "2"  
    //       }
    // ```
    // 
    MarkQuantity: {
      // `Тег ФФД: 1293`
      // 
      // 
      // Числитель дробной части предмета расчета. 
      // Значение должно быть строго меньше
      // значения реквизита «знаменатель».
      // 
      Numerator?: number
      // `Тег ФФД: 1294`
      // 
      // 
      // Знаменатель дробной части предмета расчета. 
      // Значение равно количеству товара в партии (упаковке), 
      // имеющей общий код маркировки товара.
      // 
      Denominator?: number
    }
    // Отраслевой реквизит предмета расчета. Указывается только для товаров подлежащих обязательной маркировке средством
    // идентификации. Включение этого реквизита предусмотрено НПА отраслевого регулирования для
    // соответствующей товарной группы.
    // 
    SectoralItemProps: {
      // `Тег ФФД: 1262`
      // 
      // 
      // Идентификатор ФОИВ — федеральный орган
      // исполнительной власти.
      // 
      FederalId: string
      // `Тег ФФД: 1263`
      // 
      // 
      // Дата нормативного акта ФОИВ.
      // 
      Date: string
      // `Тег ФФД: 1264`
      // 
      // 
      // Номер нормативного акта ФОИВ.
      // 
      Number: string
      // `Тег ФФД: 1265`
      // 
      // 
      // Состав значений, определенных нормативным актом ФОИВ.
      // 
      Value: string
    }[]
  }[]
  // Детали платежа.
  // 
  // 
  // Если объект не передан, автоматически указывается итоговая сумма чека с видом оплаты «Безналичный». 
  //  
  // Если передан объект `receipt.Payments`, значение в `Electronic` должно быть равно итоговому значению `Amount` в методе **Init**.
  // При этом сумма введенных значений по всем видам оплат, включая `Electronic`, должна быть равна сумме (**Amount**) всех товаров,
  // переданных в объекте `receipt.Items`.
  // 
  Payments: {
    // `Тег ФФД: 1031.`<br>
    // 
    // Вид оплаты «Наличные».
    // Сумма к оплате в копейках.
    // 
    Cash?: number
    // `Тег ФФД: 1081.`<br>
    // 
    // Вид оплаты «Безналичный».
    // 
    Electronic: number
    // `Тег ФФД: 1215.`<br>
    // 
    // Вид оплаты «Предварительная оплата (Аванс)».
    // 
    AdvancePayment?: number
    // `Тег ФФД: 1216.`<br>
    // 
    // Вид оплаты «Постоплата (Кредит)».
    // 
    Credit?: number
    // `Тег ФФД: 1217.`<br>
    // 
    // Вид оплаты «Иная форма оплаты».
    // 
    Provision?: number
  }
}
```

### #/components/schemas/Shops

```ts
// JSON-объект с данными маркетплейса. Обязательный для маркетплейсов.
{
  // Код магазина
  // 
  ShopCode: string
  // Cумма в копейках, которая относится к
  // указанному `ShopCode`.
  // 
  Amount: number
  // Наименование товара.
  // 
  Name?: string
  // Сумма комиссии в копейках, удерживаемая из
  // возмещения партнера в пользу маркетплейса.
  // Если не передано, используется комиссия,
  // указанная при регистрации.
  // 
  Fee?: string
}
```

### #/components/schemas/Init_FULL

```ts
{
  // Идентификатор терминала. <br>
  // Выдается мерчанту в Т‑Бизнес при заведении терминала.
  // 
  TerminalKey: string
  // * Сумма в копейках. Например, 3 руб. 12коп. — это число 312.
  // * Параметр должен быть равен сумме всех параметров `Amount`, переданных в объекте `Items`.
  // * Минимальная сумма операции с помощью СБП составляет 10 руб.
  // 
  Amount: number
  // Идентификатор заказа в системе мерчанта. Должен быть уникальным для каждой операции.
  OrderId: string
  // Подпись запроса.
  Token: string
  // Описание заказа.
  // Значение параметра будет отображено на платежной форме.
  //  
  //  
  // Для привязки и одновременной оплаты по СБП поле обязательное. При оплате через СБП эта информация
  // отобразится в мобильном банке клиента.
  // 
  Description?: string
  // Идентификатор клиента в системе мерчанта.
  // 
  // * Обязателен, если передан атрибут `Recurrent`.
  // * Если был передан в запросе, в нотификации будет указан `CustomerKey` и его `CardId`. Подробнее — в методе
  // [GetCardList](#tag/Metody-raboty-s-kartami/paths/~1GetCardList/post).
  // * Нужен для сохранения карт на платежной форме — платежи в один клик.
  // * Необязателен при рекуррентных платежах через СБП.
  // 
  CustomerKey?: string
  // Признак родительского рекуррентного платежа. Обязателен для регистрации автоплатежа. 
  // 
  // 
  // Если передается и установлен в `Y`, регистрирует платеж как рекуррентный. 
  // В этом случае после оплаты в нотификации на `AUTHORIZED` будет передан параметр `RebillId` для использования в методе **Charge**.
  // Для привязки и одновременной оплаты по CБП передавайте `Y`. 
  // 
  // Значение зависит от атрибутов:
  // 
  // * `OperationInitiatorType` — в методе `/init`,
  // * `Recurrent` — в методе `/Init`. 
  // 
  // 
  // Подробнее — в описании методов [Рекуррентный платёж](#tag/Rekurrentnyj-platyozh) и [Инициализация платежа](#tag/Standartnyj-platyozh/paths/~1Init/post).
  // 
  Recurrent?: string
  // Определяет тип проведения платежа — двухстадийная или одностадийная оплата:
  // 
  // * `O` — одностадийная оплата,
  // * `T` — двухстадийная оплата. 
  // 
  // 
  // Если параметр передан — используется его значение, если нет — значение из настроек терминала.
  // 
  PayType?: enum[O, T]
  // Язык платежной формы:
  // 
  // * `ru` — русский,
  // * `en` — английский. 
  // 
  // 
  // Если не передан, форма откроется на русском языке.
  // 
  Language?: string
  // URL на веб-сайте мерчанта, куда будет отправлен POST-запрос о статусе выполнения вызываемых методов — настраивается 
  // в личном кабинете. Если параметр:
  // 
  // * передан — используется его значение,
  // * не передан — значение из настроек терминала.
  // 
  NotificationURL?: string
  // URL на веб-сайте мерчанта, куда будет
  // переведен клиент в случае успешной оплаты — настраивается в личном кабинете. Если параметр:
  // * передан — используется его значение,
  // * не передан — значение из настроек терминала.
  // 
  SuccessURL?: string
  // URL на веб-сайте мерчанта, куда будет
  // переведен клиент в случае неуспешной
  // оплаты — настраивается в личном кабинете. Если параметр:
  // * передан — используется его значение,
  // * не передан — значение из настроек терминала.
  // 
  FailURL?: string
  // JSON-объект, который позволяет передавать дополнительные параметры по операции и задавать определенные настройки в 
  // формате `ключ:значение`.
  // 
  // Максимальная длина для каждого передаваемого параметра:
  //   * ключ — 20 знаков;
  //   * значение — 100 знаков.
  // 
  // Максимальное количество пар `ключ:значение` — 20.
  // 
  // 1. Если у терминала включена опция привязки клиента после 
  // успешной оплаты и передается параметр `CustomerKey`, в передаваемых 
  // параметрах `DATA` могут быть параметры метода **AddCustomer**. 
  // Если они есть, они автоматически привязываются к клиенту.
  // 
  // Например, если указать `"DATA":{"Phone":"+7*********0", "Email":"<EMAIL>"}`,
  // к клиенту автоматически будут привязаны данные электронной почты и телефон, 
  // и они будут возвращаться при вызове метода **GetCustomer**.
  // 
  // Для МСС 4814 обязательно передать значение в параметре `Phone`.
  // Требования по заполнению: 
  // 
  //   * минимум — 7 символов,
  //   * максимум — 20 символов,
  //   * разрешены только цифры, исключение — первый символ может быть `+`.
  // 
  // Для МСС 6051 и 6050 обязательно передавать параметр `account` — номер электронного кошелька, не должен превышать 30 символов. 
  // 
  // Пример: `"DATA": {"account":"*********"}`.
  // 
  // 2. Если используется функционал сохранения карт на платежной форме, 
  // при помощи опционального параметра `DefaultCard` можно задать, 
  // какая карта будет выбираться по умолчанию. 
  // 
  //     Возможные варианты:
  //     
  //     * Оставить платежную форму пустой. Пример:
  //     
  //       ```
  //       "DATA":{"DefaultCard":"none"}
  //       ```
  //     
  //     * Заполнить параметр данными передаваемой карты. В этом случае передается `CardId`. Пример:
  //     
  //       ```
  //        "DATA":{"DefaultCard":"894952"}
  //       ```
  //     
  //     * Заполнить параметр данными последней сохраненной карты. Применяется, если параметр `DefaultCard` не передан, 
  //     передан с некорректным значением или в значении `null`.
  //     По умолчанию возможность сохранение карт на платежной форме может быть отключена. Для активации обратитесь в 
  //     техническую поддержку.
  // 
  // 3. Если вы подключаете оплату через T‑Pay, то вы можете передавать параметры устройства, с которого будет осуществлен переход в объекте `Data`.
  // Пример:
  // 
  //   ```
  //   "DATA": {
  //     "TinkoffPayWeb": "true",
  //     "Device": "Desktop",
  //     "DeviceOs": "iOS",
  //     "DeviceWebView": "true",
  //     "DeviceBrowser": "Safari"
  //    }
  //   ```
  // 
  // Рекомендации для заполнения поля `Device`:
  // 
  //   * Mobile  — при оплате c мобильного устройства;
  //   * Desktop — при оплате c десктопного устройства.
  // 
  // Рекомендации для заполнения поля `DeviceOs`:
  // 
  //   * iOS,
  //   * Android,
  //   * macOS,
  //   * Windows,
  //   * Linux.
  // 
  // Рекомендации для заполнения поля `DeviceBrowser`:
  // 
  //   * Chrome,
  //   * Firefox,
  //   * JivoMobile,
  //   * Microsoft Edge,
  //   * Miui,
  //   * Opera,
  //   * Safari,
  //   * Samsung,
  //   * WebKit,
  //   * WeChat,
  //   * Yandex.
  // 
  // 4. Параметр `notificationEnableSource` позволяет отправлять нотификации, только если Source платежа входит в перечень
  // указанных в параметре — он также есть в параметрах сессии. Возможные значения — T‑Pay, sbpqr. Пример:
  // 
  //  ```
  //  notificationEnableSource=TinkoffPay
  //  ``` 
  // 
  // 5. Для привязки и одновременной оплаты по CБП передавайте параметр `QR = true`.
  // 
  // 6. При передаче в объекте `DATA` атрибута `OperationInitiatorType` учитывайте взаимосвязь его значений:
  // 
  //    * Со значением атрибута `Recurrent` в методе **/Init**.
  //    * Со значением атрибута `RebillId` в методе **/Charge**.
  //    * С типом терминала, который используется для проведения операций — ECOM/AFT.
  //   
  //   [Подробная таблица — передача признака инициатора операции](#section/Peredacha-priznaka-iniciatora-operacii)
  // 
  //   Если передавать значения атрибутов, которые не соответствуют таблице, MAPI вернет ошибку 1126 —
  //   несопоставимые значения `rebillId` или `Recurrent` с переданным значением `OperationInitiatorType`.
  // 
  DATA?: #/components/schemas/Common | #/components/schemas/T-Pay | #/components/schemas/LongPay
  // JSON-объект с данными чека. Обязателен, если подключена онлайн-касса.
  Receipt?: #/components/schemas/Receipt_FFD_105 | #/components/schemas/Receipt_FFD_12
  // JSON-объект с данными маркетплейса. Обязательный для маркетплейсов.
  Shops: {
    // Код магазина
    // 
    ShopCode: string
    // Cумма в копейках, которая относится к
    // указанному `ShopCode`.
    // 
    Amount: number
    // Наименование товара.
    // 
    Name?: string
    // Сумма комиссии в копейках, удерживаемая из
    // возмещения партнера в пользу маркетплейса.
    // Если не передано, используется комиссия,
    // указанная при регистрации.
    // 
    Fee?: string
  }[]
  // Динамический дескриптор точки.
  Descriptor?: string
}
```

### #/components/schemas/Response

```ts
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Сумма в копейках.
  // 
  Amount: number
  // Идентификатор заказа в системе мерчанта.
  // 
  OrderId: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Статус транзакции.
  // 
  Status: string
  // Идентификатор платежа в системе Т‑Бизнес.
  // 
  PaymentId: string
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Ссылка на платежную форму. Параметр возвращается только `для мерчантов без PCI DSS`.
  // 
  PaymentURL?: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
}
```

### #/components/schemas/3DSMethod

```ts
{
  // JSON с параметрами threeDSMethodNotificationURL, threeDSServerTransID, **закодированный в формат base-64** 
  // 
  //   | Название параметра | Тип | Описание             |
  //  | --------------------- | ------------ | -------------------- |
  //  | threeDSMethodNotificationURL | string | Обратный адрес, на который будет отправлен запрос после прохождения threeDSMethod |
  //  | threeDSServerTransID | string | Идентификатор транзакции из ответа метода |
  // 
  //  
  threeDSMethodData: string
}
```

### #/components/schemas/3DSMethod-2

```ts
{
  // Идентификатор транзакции
  // 
  threeDSServerTransID: string
}
```

### #/components/schemas/3DSv2

```ts
{
  // `deviceChannel 02 — BRW`
  // 
  // 
  // Идентификатор выполнения 3DS Method:
  // * `Y` — выполнение метода успешно завершено
  // * `N` — выполнение метода завершено неуспешно или метод не выполнялся
  // * `U` — в ответе на вызов метода Check3dsVersion не было получено значение threeDSMethodURL
  // 
  threeDSCompInd: string
  // `deviceChannel 02 — BRW`
  // 
  // 
  // Язык браузера в формате `IETF BCP47`.
  // Рекомендуем получать значение в браузере из глобального объекта navigator — `navigator.language`.
  // 
  language: string
  // `deviceChannel 02 — BRW`
  // 
  // 
  // Time-zone пользователя в минутах.
  // Рекомендуем получать значение в браузере через вызов метода **getTimezoneOffset()**.
  // 
  timezone: string
  // `deviceChannel 02 — BRW`
  // 
  // 
  // Высота экрана в пикселях.
  // Рекомендуем получать значение в браузере из глобального объекта screen — `screen.height`.
  // 
  screen_height: string
  // `deviceChannel 02 — BRW`
  // 
  // 
  // Ширина экрана в пикселях.
  // Рекомендуем получать значение в браузере из глобального объекта screen — `screen.width`.
  // 
  screen_width: string
  // `deviceChannel 02 — BRW`
  // 
  // 
  // URL, который будет использоваться для получения результата (CRES) после завершения Challenge Flow 
  // — аутентификации с дополнительным переходом на страницу ACS.
  // 
  cresCallbackUrl: string
  // `deviceChannel 02 — BRW`
  // 
  // 
  // Глубина цвета в битах.
  // <br> Допустимые значения — 1/4/8/15/16/24/32/48.
  // <br> Рекомендуем получать значение в браузере из глобального объекта screen — `screen.colorDepth`.
  // 
  colorDepth?: string //default: 48
  // `deviceChannel 02 — BRW`
  // 
  // 
  // Поддерживает ли браузер пользователя Java: 
  // * `true`,
  // * `false`.
  // 
  javaEnabled?: string
}
```

### #/components/schemas/FinishAuthorize_FULL

```ts
{
  // Идентификатор терминала. <br>
  // Выдается мерчанту в Т‑Бизнес при заведении терминала.
  // 
  TerminalKey: string
  // Уникальный идентификатор транзакции в
  // системе Т‑Бизнес.
  // 
  PaymentId: string
  // Подпись запроса.
  // 
  Token: string
  // IP-адрес клиента. <br>
  // Передача адреса допускается в формате IPv4 и IPv6.
  // 
  // Обязательный параметр для 3DS второй
  // версии. DS платежной системы требует 
  // передавать данный адрес в полном формате, 
  // без каких-либо сокращений — 8 групп по 4 символа.
  // 
  // Этот формат регламентируется на уровне
  // спецификации EMVCo.<br>
  // 
  // Пример правильного адреса — `2011:0db8:85a3:0101:0101:8a2e:0370:7334`.
  // 
  // Пример неправильного адреса — `2a00:1fa1:c7da:9285:0:51:838b:1001`.
  // 
  IP?: string
  // * `true` — отправлять клиенту информацию об оплате на
  // почту;
  // * `false` — не отправлять.
  // 
  SendEmail?: boolean
  // Источник платежа.
  // Значение параметра зависит от параметра `Route`:
  // - `ACQ` — `cards`. Также поддерживается написание `Cards`.
  // - `MC` — `beeline`, `mts`, `tele2`, `megafon`.
  // - `EINV` — `einvoicing`.
  // - `WM` — `webmoney`.
  // 
  Source?: enum[cards, beeline, mts, tele2, megafon, einvoicing, webmoney]
  // JSON-объект, который содержит дополнительные
  // параметры в виде `ключ`:`значение`. Эти параметры будут переданы на страницу
  // оплаты, если она кастомизирована.
  // 
  // 
  // Максимальная длина для каждого передаваемого параметра:
  // * ключ — 20 знаков,
  // * значение — 100 знаков. 
  // 
  // 
  // Максимальное количество пар `ключ`:`значение` — не больше 20.
  // 
  DATA: {
  }
  // Электронная почта для отправки информации об оплате.
  // Обязателен при передаче `SendEmail`.
  // 
  InfoEmail?: string
  // Данные карты.
  // Используется и является обязательным только 
  // для ApplePay или GooglePay.
  // 
  EncryptedPaymentData?: string
  // Объект `CardData` собирается в виде списка `ключ`=`значение` c разделителем `;`.
  // Объект зашифровывается открытым ключом (X509 RSA 2048), и получившееся бинарное значение кодируется в `Base64`.
  // Открытый ключ генерируется в Т‑Бизнес и выдается при регистрации терминала.
  // Доступен в личном кабинете Интернет-эквайринга в разделе **Магазины** при изменении типа подключения на «Мобильное».
  // 
  // |Наименование|Тип данных| Обязательность | Описание                                                                                                                                           |
  // |---|---|----------------|----------------------------------------------------------------------------------------------------------------------------------------------------|
  // |PAN|Number| Да             | Номер карты.                                                                                                                                       |
  // |ExpDate| Number| Да             | Месяц и год срока действия карты в формате `MMYY`.                                                                                                 |
  // |CardHolder |String| Нет            | Имя и фамилия держателя карты — как на карте.                                                                                                      |
  // |CVV |String| Нет            | Код защиты с обратной стороны карты. Для платежей по Apple Pay с расшифровкой токена на своей стороне необязательный.                              |
  // |ECI |String | Нет            | Electronic Commerce Indicator. Индикатор, который показывает степень защиты, применяемую при предоставлении клиентом своих данных ТСП. |
  // |CAVV |String | Нет            | Cardholder Authentication Verification Value или Accountholder Authentication Value.                                                               |
  // 
  // 
  // Пример значения элемента формы `CardData`:
  // 
  // ```
  // PAN=****************;ExpDate=0519;CardHolder=IVAN PETROV;CVV=111
  // ```
  // 
  // Для MirPay, если интеграция с НСПК для получения платежного токена:
  // 1. Передавайте `Route=ACQ` и `Source= MirPay`.
  // 2. ПВ `DATA.transId` передавайте значение `transId`.
  // 3. В `DATA.tavv` передавайте значение `cav`.
  // 4. Передавайте параметр `CardData`:
  // 
  //     - **Pan** заполняйте `tan`,
  //     - **ExpDate** заполняйте `tem + tey`.<br>
  //    
  // Если мерчант интегрируется только с банком для проведения платежа по MirPay, 
  // метод не вызывается. Эквайер самостоятельно получает платежный токен и инициирует авторизацию
  // вместо мерчанта.<br> 
  // 
  // При получении **CAVV** в **CardData** оплата будет проводиться как оплата токеном — иначе прохождение 3DS будет
  // регулироваться стандартными настройками треминала или платежа.
  // 
  // Не используется и не является обязательным, если передается `EncryptedPaymentData`.
  CardData: string
  // Сумма в копейках.
  // 
  Amount?: number
  // Канал устройства.
  // Поддерживаются следующие
  // каналы:
  // * `01` = Application (APP),
  // * `02` = Browser (BRW) — используется по умолчанию, передавать параметр не требуется.
  // 
  deviceChannel?: string
  // Способ платежа.
  // Обязательный для ApplePay или GooglePay.
  // 
  Route?: enum[ACQ, MC, EINV, WM]
}
```

### #/components/schemas/FinishAuthorize

```ts
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Сумма в копейках.
  // 
  Amount: number
  // Идентификатор заказа в системе мерчанта.
  // 
  OrderId: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Статус транзакции.
  // Возвращается один из четырех статусов платежа:
  //   * `CONFIRMED` — при одностадийной оплате.
  //   * `AUTHORIZED` — при двухстадийной оплате.
  //   * `3DS_CHECKING` — когда нужно пройти проверку 3-D Secure. Если используется своя ПФ (протокол EACQ C PCI DSS) и платеж завис в этом статусе, нужно обратиться к эмитенту для устранения ошибок оплаты.
  //   * `REJECTED` — при неуспешном прохождении платежа.
  // 
  Status: string
  // Идентификатор платежа в системе Т‑Бизнес.
  // 
  PaymentId?: string
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
  // Идентификатор рекуррентного платежа.
  RebillId?: string
  // Идентификатор карты в системе Т‑Бизнес. Передается только для cохраненной карты.
  CardId?: string
}
```

### #/components/schemas/Without3DS

```ts
{
  "allOf": [
    {
      "$ref": "#/components/schemas/FinishAuthorize"
    },
    {
      "type": "object",
      "properties": {
        "CardId": {
          "type": "string",
          "description": "Идентификатор карты в системе Т‑Бизнес. \nПередается только для сохраненной карты.\n"
        }
      }
    }
  ]
}
```

### #/components/schemas/With3DS

```ts
{
  "allOf": [
    {
      "$ref": "#/components/schemas/FinishAuthorize"
    },
    {
      "type": "object",
      "properties": {
        "MD": {
          "type": "string",
          "description": "Идентификатор транзакции в системе Т‑Бизнес.\n",
          "example": "ACQT-*********"
        },
        "PaReq": {
          "type": "string",
          "description": "Шифрованная строка, содержащая результаты 3-D Secure аутентификации. Возвращается в ответе от ACS.\n",
          "example": "\"eJxVUl1TwjAQ/CtM30s+KLTDHGHQwsiogFh09C2kp1RpC2nLh7/eBAtqnnYvN3ubvUD/kK4bO9RFkmc9hzWp08BM5XGSvfecRTRyA6cvIFppxP\nARVaVRwD0WhXzHRhL3HMUU73itwKVtyl1Pcs8Nli3pymUQK+z2Sww6joDZYI5bAfUgYeY0OZAzNYparWRWCpBqezWeiDZnLe3BqSmkqMeh4PRy2p\n02BfJThkymKCIsSiAnCCqvslIfhXEG5Eyg0muxKstN0SVkv983yyT7zN/emroiQOwlkF8js8qiwogdklg8rEfT5WK0jj6G7D4cepNo8TWNBmwSDXtAbAfEskTjkPk0\noF6DeV3a6jLj8VQHmVoXglFTqTFs7IjBn4u/BTBZa7OK8yPODPCwyTM0HSbACwby6/f6xsaoSpNMMN89+uHdV/iUPz2nyat/uxrPXz5nuX/c2nBPTVYxMflwzthJ0hIgVobUeyP1yg469xW+AedOuuM=\"\n"
        },
        "ACSUrl": {
          "type": "string",
          "format": "uri",
          "description": "Если в ответе метода **FinishAuthorize** возвращается статус `3DS_CHECKING`, \nмерчанту нужно сформировать запрос на URL ACS банка, \nкоторый выпустил карту — параметр `ACSUrl` в ответе, и вместе с этим перенаправить клиента на эту же страницу\nACSUrl для прохождения 3DS.\n",
          "example": "https://secure.tcsbank.ru/acs/auth/start.do"
        }
      }
    }
  ]
}
```

### #/components/schemas/With3DSv2APP

```ts
{
  "allOf": [
    {
      "$ref": "#/components/schemas/FinishAuthorize"
    },
    {
      "type": "object"
    },
    {
      "required": [
        "TdsServerTransId",
        "AcsTransId",
        "AcsReferenceNumber",
        "SdkTransID"
      ],
      "properties": {
        "TdsServerTransId": {
          "type": "string",
          "description": "Уникальный идентификатор транзакции,генерируемый 3DS-Server. Обязательный параметр для 3DS второй версии.\n",
          "example": "d93f7c66-3ecf-4d10-ba62-46046e7b7596"
        },
        "AcsTransId": {
          "type": "string",
          "description": "Идентификатор транзакции, присвоенный ACS, который вернулся в ответе **FinishAuthorize**.\n",
          "example": "aceca6af-56ee-43f0-80ef-ea8d30d5c5b0"
        },
        "AcsInterface": {
          "type": "string",
          "format": "uri",
          "description": "`Обязательное поле, если `Transaction Status` = `C`.`<br><br>\nТип пользовательского интерфейса ACS. Возможные значения:\n  - `01` — Native UI,\n  - `02` — HTML UI.\n",
          "example": "02"
        },
        "AcsUiTemplate": {
          "type": "string",
          "format": "uri",
          "description": "`Обязательное поле, если `Transaction Status` = `C`.`<br><br>\n Формат шаблона пользовательского интерфейса ACS. Возможные значения: \n   - `01` — Text;\n   - `02` — Single Select;\n   - `03` — Multi Select;\n   - `04` — OOB;\n   - `05` — HTML Other (valid only for HTML UI).\n",
          "example": "03"
        },
        "AcsSignedContent": {
          "type": "string",
          "format": "uri",
          "description": "`Обязательное поле, если `Transaction Status` = `C`.`<br><br>\n JWS object, представленный как string, который создан ACS для ARes. Содержит:\n   - ACS URL — 3DS SDK должен отправить Challenge Request на этот URL,\n   - ACS Ephemeral Public Key (QT),\n   - SDK Ephemeral Public Key (QC).\n",
          "example": "eyJ4NWMiOlsiTUlJRGtUQ0NBbm1nQXdJQkFnSVVRU1VEV05VZEFicWozS1Uya0M0VHpaSEpVVHd3RFFZSktvWklodmNOQVFFTEJRQXdXREVMTUFrR0ExVUVCaE1DVWxVeER6QU5CZ05WQkFnTUJrMXZjMk52ZHpFUE1BMEdBMVVFQnd3R1RXOXpZMjkzTVJJ d0VBWURWUVFLREFsVGIyMWxJR0poYm1zeEV6QVJCZ05WQkFNTUNtUnpMbTF2WTJzdWNuVXdIaGNOTWpBd056RTRNVFExT1RNM1doY05NakV3TnpFNE1UUTFPVE0zV2pCWU1Rc3dDUVlEVlFRR0V3SlNWVEVQTUEwR0ExVUVDQXdHVFc5elkyOTNNUTh3RF FZRFZRUUhEQVpOYjNOamIzY3hFakFRQmdOVkJBb01DVk52YldVZ1ltRnVhekVUTUJFR0ExVUVBd3dLWkhNdWJXOWpheTV5ZFRDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTUhNdXB1Wlg3VUFWR3Z5dm9uZ1o4U3BJcisrRD RnMjBRaFwvZ0NGb3JUN1pDUkRaVWhRamlDSzdXSWpiVHRKQUFKVG1yelhcLzlMSGJIdHpIcFFvRFVTNXZPTnRqVWFaVGVQUE91SklMRWl6NDBBVjJCUVZRd0xnRzBjbm9oK21Qa0dNMEZ4VmJFcHFEVHk3SHB0dFAwdm96cGxHNjdFWk1HTXdKSUpESmlDYUdG OGZ0aTlYR3M4MXB3NUhWZElmOHNpQnFaWW94cGt0QWJ1dnpBTFJEUnp3dFBhclFHOTZyQStPM0dJaE53VDhZXC9pallwS0hWNkJCWDBKNmxZdFdoaVY5blhBVktYNTNlVTJ4M1E2Njh4U3BLa2dwSVh1N2xiNUN2M2dDTlIrelVqK0lTODNZYjJhUlR2WkF6MFI1 V3dBNW5Zb2J6V3Vta1wvdE5iV1FYdzBWTUNBd0VBQWFOVE1GRXdIUVlEVlIwT0JCWUVGRmVWN0dzR0tCSzhUTDljaVk4UFF2N0RhY290TUI4R0ExVWRJd1FZTUJhQUZGZVY3R3NHS0JLOFRMOWNpWThQUXY3RGFjb3RNQThHQTFVZEV3RUJcL3dRRk1BT UJBZjh3RFFZSktvWklodmNOQVFFTEJRQURnZ0VCQUZqVGppUkxKOFpaWld5dXFLNTZHVkR6dnJiXC9uRlVDTHVjVXZEV2toK09lRWkxWUFPOUJZV3RFVTVzdmRNNTlsOWVTMGtjbGxrRzVDTklcL1U4S2dKSnUzV0tEVXp5cU80eVRNU3g3RWZDXC9qVE1oT2d2Y UJubktWK2hvV3FQZTlKNHZVYzZ2R0wzWE1cL0FNeWpoVDlBRko1ZjZBaVdZMk5QYkxHczQ2N0ZPY2Vwb1RJMkdseHBtcWdaMFVGKzlsblNZbDU0WEg2dGNZYUszWjcxS2NES0I0QkUySWVmV1Y3MUM3anBVdjFFSlFsNTY4XC8xaGpsZktXUExWcE5NTzVlTlNMR 1ZKd1VmdFA0V0tKU2Y2VmdtbG5XOU1yVStiK3hvZW44MFF1dUxrSWs1ZXBIM2l1ZDV4a1IxcVVXQU1aTUZTQW4yUHJDdjQrZFFMRDd2OG83d3BrPSJdLCJhbGciOiJQUzI1NiJ9.eyJhY3NFcGhlbVB1YktleSI6eyJrdHkiOiJFQyIsImNydiI6IlAtMjU2IiwieCI6IlRoRj NJY3BIMVVLanliQW5lNWhHcy1BNnpyYXo2aUxiYVk0WmVEOU1oSU0iLCJ5Ijoid0VuVXNvNlRLZDlfbjZSc2NjUXRCeFc2Q1gzLXFSTGk0UWJBU3pNbm4tTSJ9LCJzZGtFcGhlbVB1YktleSI6eyJrdHkiOiJFQyIsImNydiI6IlAtMjU2IiwieCI6Ikp6R2tGM2w3WGxnclJ6NU 1PTl9ncDg3WUxfd0NkVVJpVUlxOXJmNnVyR2MiLCJ5IjoiTnI4UmllTE9vVzJXUkhiX2RFazFmdHRoWEZXTHdYaWZFUzNZZkFnMkhvWSJ9LCJhY3NVUkwiOiJodHRwczovL2VhY3EtZHMtbW9jay1zZXJ2aWNlLXRlc3QudGNzYmFuay5ydS9jaGFsbGVuZ2UvZDkzZjdj NjYtM2VjZi00ZDEwLWJhNjItNDYwNDZlN2I3NTk2In0.hQLVTT5YMAY8TjISRdYX2IT04zH8Z8DgoB4kIAyVfkuJ0X6AGIKXSVcIVSNgC-A_SEkCZRqAyUeu0ZJtpoIVyOf1mumBGEK-uC6yVQlX5WSPidQUj4nuBvpYsfdrGPeoHWvNsrBpMMxvvW4559jtbAUY00NcW3rwDShAi4gVKgJcssMPAM1zOOR5vi0_ClUsCW1k9a201Hv6cYcEBuO2JQ8NPLampEkZ55nOmwcPPTEziXeZsq9VjROXNfBewbA4wLuQmh8aSrcOcwFtJo0CPpdrsKiY77KPT0c8XMmZZK_FiAxzrWocfHraqC7cRJNQ5glEBakXvSfrwGg_xXA"
        },
        "AcsReferenceNumber": {
          "type": "string",
          "format": "uri",
          "description": "Уникальный идентификатор, назначенный EMVCo.",
          "example": "12345"
        },
        "SdkTransID": {
          "type": "string",
          "format": "uri",
          "description": "Уникальный идентификатор транзакции, назначенный 3DS SDK для идентификации одной транзакции, который вернулся в ответе **FinishAuthorize**.",
          "example": "d5a44dfe-673b-4666-82f9-96346107e424"
        }
      }
    }
  ]
}
```

### #/components/schemas/With3DSv2BRW

```ts
{
  "allOf": [
    {
      "$ref": "#/components/schemas/FinishAuthorize"
    },
    {
      "type": "object"
    },
    {
      "required": [
        "TdsServerTransId",
        "AcsTransId"
      ],
      "properties": {
        "TdsServerTransId": {
          "type": "string",
          "description": "Уникальный идентификатор транзакции, генерируемый 3DS-Server. Обязательный параметр для 3DS второй версии.\n",
          "example": "d7171a06-7159-4bdd-891a-a560fe9938d2"
        },
        "AcsTransId": {
          "type": "string",
          "description": "Идентификатор транзакции, присвоенный ACS, который вернулся в ответе **FinishAuthorize**.\n",
          "example": "e176d5d3-2f19-40f5-8234-46d3464e0b08"
        },
        "ACSUrl": {
          "type": "string",
          "format": "uri",
          "description": "Если в ответе метода **FinishAuthorize** возвращается статус `3DS_CHECKING`, \nмерчанту нужно сформировать запрос на URL ACS банка, \nкоторый выпустил карту — параметр `ACSUrl` в ответе, и вместе с этим перенаправить клиента на эту же страницу\nACSUrl для прохождения 3DS.\n",
          "example": "https://acs.vendorcert.mirconnect.ru/mdpayacs/creq"
        }
      }
    }
  ]
}
```

### #/components/schemas/AddCard_FULL

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  // 
  CustomerKey: string
  // Подпись запроса.
  // 
  Token: string
  // Если `CheckType` не передается, автоматически проставляется значение `NO`.
  //  Возможные значения:
  //  * `NO` — сохранить карту без проверок. `RebillID` для рекуррентных платежей не возвращается.
  //  * `HOLD` — при сохранении сделать списание на 0 руб. `RebillID` возвращается для терминалов без
  //  поддержки 3DS.
  //  * `3DS` — при сохранении карты выполнить проверку 3DS и выполнить списание на 0 р. В этом случае `RebillID` будет только для
  //  3DS карт. Карты, не поддерживающие 3DS, привязаны не будут.
  //  * `3DSHOLD` – при привязке карты выполнить проверку, поддерживает карта 3DS или нет. Если карта не поддерживает 3DS, выполняется
  //  списание на 0 руб.
  // 
  CheckType?: enum[NO, HOLD, 3DS, 3DSHOLD]
  // IP-адрес запроса.
  // 
  IP?: string
  // Признак резидентности добавляемой карты:
  // Возможные значения:
  // * `true` — карта РФ,
  // * `false` — карта не РФ,
  // * `null` — не специфицируется, универсальная карта.
  // 
  ResidentState?: boolean
}
```

### #/components/schemas/AddCardResponse_FULL

```ts
{
  // Идентификатор платежа в системе Т‑Бизнес.
  // 
  PaymentId: number
  // Идентификатор терминала. Выдается мерчанту Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  // 
  CustomerKey: string
  // Идентификатор запроса на привязку карты.
  // 
  RequestKey: string
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
  // UUID, используется для работы без PCI DSS.
  // 
  PaymentURL: string
}
```

### #/components/schemas/AttachCard

```ts
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Идентификатор запроса на привязку карты.
  // 
  RequestKey: string
  // Зашифрованные данные карты в формате: 
  //   ```
  //   PAN=****************;ExpDate=0519;CardHolder=IVAN PETROV;CVV=111
  //   ```
  // 
  CardData: string
  // В объекте передаются дополнительные параметры в формате `ключ:значение`.
  // Например, меняем на JSON-объект, который содержит дополнительные параметры в виде `ключ:значение`.
  // 
  // Если ключи или значения содержат в себе специальные символы, получившееся значение должно быть закодировано
  // функцией `urlencode`. Максимальная длина для каждого передаваемого параметра:
  // * ключ — 20 знаков,
  // * значение — 100 знаков. 
  // 
  // Максимальное количество пар `ключ:значение` — не больше 20.
  // 
  // >**Важно** Для 3DS второй версии в `DATA` передаются параметры, описанные в объекте
  // `3DSv2`. В `HttpHeaders` запроса обязательны заголовки `User-Agent` и `Accept`.
  // 
  DATA?: {
   } | #/components/schemas/3DSv2
  // Подпись запроса.
  // 
  Token: string
}
```

### #/components/schemas/AttachCardResponse

```ts
{
  // Платежный ключ, выдается мерчанту при заведении
  // терминала.
  // 
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  // 
  CustomerKey: string
  // Идентификатор запроса на привязку карты.
  // 
  RequestKey: string
  // Идентификатор карты в системе Т‑Бизнес. <br>
  // При сценарии 3-D Secure Authentication Challenge — `CardId` можно получить после успешного прохождения 3DS.
  // 
  CardId?: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Статус привязки карты:
  // * `NEW` — новая сессия привязки карты;
  // * `FORM_SHOWED` — показ формы привязки карты;
  // * `THREE_DS_CHECKING` — отправка клиента на проверку 3DS;
  // * `THREE_DS_CHECKED` — клиент успешно прошел проверку 3DS;
  // * `AUTHORIZING` — отправка платежа на 0 руб;
  // * `AUTHORIZED` — платеж на 0 руб прошел успешно;
  // * `COMPLETED` — карта успешно привязана;
  // * `REJECTED` — привязать карту не удалось.
  // 
  Status?: enum[NEW, FORM_SHOWED, THREE_DS_CHECKING, THREE_DS_CHECKED, AUTHORIZING, AUTHORIZED, COMPLETED, REJECTED]
  // Идентификатор рекуррентного платежа.
  // 
  RebillId?: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
  // Адрес сервера управления доступом для проверки 3DS —
  // возвращается в ответе на статус `3DS_CHECKING`.
  // 
  ACSUrl?: string
  // Уникальный идентификатор транзакции в системе
  // Т‑Бизнес — возвращается в ответе на статус `3DS_CHECKING`.
  // 
  MD?: string
  // Результат аутентификации 3-D Secure — возвращается в ответе на статус `3DS_CHECKING`.
  // 
  PaReq?: string
}
```

### #/components/schemas/ACSUrl_V1

```ts
{
  // Уникальный идентификатор транзакции в системе Банка (возвращается в ответе на FinishAuthorize)
  MD: string
  // Результат аутентификации 3-D Secure (возвращается в ответе на FinishAuthorize)
  PaReq: string
  // Адрес перенаправления после аутентификации 3-D Secure (URL обработчик на стороне Мерчанта, принимающий результаты прохождения 3-D Secure)
  TermUrl: string
}
```

### #/components/schemas/ACSUrl_V2

```ts
{
  // JSON с параметрами закодированный в форматe base-64
  creq: {
    // Идентификатор транзакции из ответа метода Check3DSVersion
    threeDSServerTransID: string
    // Идентификатор транзакции, присвоенный ACS, полученный в ответе на FinishAuthorize
    acsTransID: string
    // Размер экрана, на котором открыта страница ACS. <br>Допустимые значения <br>• 01 = 250 x 400, <br>• 02 = 390 x 400, <br>• 03 = 500 x 600, <br>• 04 = 600 x 400, <br>• 05 = Fullscreen. <br>
    challengeWindowSize: string
    // Передается фиксированное значение «CReq»
    messageType: string
    // Версия 3DS, полученная из ответа метода Check3dsVersion
    messageVersion: string
  }
}
```

### #/components/schemas/ACSUrlResponseV1

```ts
{
  // Уникальный идентификатор транзакции в системе Банка (возвращается в ответе на FinishAuthorize)
  MD: string
  // Шифрованная строка, содержащая результаты 3-D Secure аутентификации (возвращается в ответе от ACS)
  PaRes: string
  // В случае невозможности прохождения аутентификации по 3DS v2.1, делается принудительный Fallback на 3DS v1.0 и данный атрибут выставляется в true, в противном случае не передается в ответе
  FallbackOnTdsV1?: string
}
```

### #/components/schemas/ACSUrlResponseV2

```ts
{
  // JSON/JWE object с параметрами закодированный в формат base-64. Ответ отправляется на URL, который был указан в методе FinishAuthorize. После получения на NotificationUrl Мерчанта ответа ACS(CRes) с результатами прохождения 3-D Secure v2 необходимо сформировать запрос к методу Submit3DSAuthorizationV2.
  cres: {
    // Идентификатор транзакции из ответа метода Check3DSVersion
    threeDSServerTransID: string
    // Идентификатор транзакции, присвоенный ACS, полученный в ответе на FinishAuthorize
    acsTransID: string
    // Результат выполнения Challenge flow, возможны 2 значения — Y/N. <br> Y — аутентификация выполнена успешна,  <br> N — аутентификация не пройдена, клиент отказался или ввел неверные данные.  <br>
    transStatus: string
    // Передается фиксированное значение «CRes»
    messageType: string
    // Версия 3DS
    messageVersion: string
  }
}
```

### #/components/schemas/Confirm

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор платежа в системе Т‑Бизнес.
  PaymentId: string
  // Подпись запроса — хэш `SHA-256`.
  Token: string
  // IP-адрес клиента.
  IP?: string
  // Сумма в копейках. Если не передан, используется `Amount`, переданный в методе **Init**.
  Amount?: number
  // JSON-объект с данными чека. Обязателен, если подключена онлайн-касса.
  Receipt: {
  }
  // JSON-объект с данными маркетплейса. Обязательный для маркетплейсов.
  Shops: {
    // Код магазина
    // 
    ShopCode: string
    // Cумма в копейках, которая относится к
    // указанному `ShopCode`.
    // 
    Amount: number
    // Наименование товара.
    // 
    Name?: string
    // Сумма комиссии в копейках, удерживаемая из
    // возмещения партнера в пользу маркетплейса.
    // Если не передано, используется комиссия,
    // указанная при регистрации.
    // 
    Fee?: string
  }[]
  // Способ платежа.
  // 
  Route?: enum[TCB, BNPL]
  // Источник платежа.
  // 
  Source?: enum[installment, BNPL]
}
```

### #/components/schemas/Items_Params

```ts
// Информация по способу оплаты или деталям для платежей в рассрочку.
{
  // Возможные значения:
  // * `Route` — способ оплаты.
  // * `Source` — источник платежа.
  // * `CreditAmount` — сумма выданного кредита в копейках. Возвращается только для платежей в рассрочку.
  // 
  Key?: enum[Route, Source, CreditAmount]
  // Возможные значения:
  // * `ACQ`, `BNPL`, `TCB`, `SBER` — для Route.
  // * `BNPL`, `cards`, `Installment`, `MirPay`, `qrsbp`, `SberPay`, `TinkoffPay`, `YandexPay` — для Source.
  // * Сумма в копейках — для CreditAmount.
  // 
  Value?: enum[ACQ, BNPL, TCB, SBER, BNPL, cards, Installment, MirPay, qrsbp, SberPay, TinkoffPay, YandexPay]
}
```

### #/components/schemas/Confirm-2

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор заказа в системе мерчанта.
  OrderId: string
  // Успешность прохождения запроса — `true`/`false`.
  Success: boolean
  // Статус транзакции.
  Status: enum[NEW, AUTHORIZING, AUTHORIZED, AUTH_FAIL, CANCELED, CHECKING, CHECKED, COMPLETING, COMPLETED, CONFIRMING, CONFIRMED, DEADLINE_EXPIRED, FORM_SHOWED, PARTIAL_REFUNDED, PREAUTHORIZING, PROCESSING, 3DS_CHECKING, 3DS_CHECKED, REVERSING, REVERSED, REFUNDING, REFUNDED, REJECTED, UNKNOWN]
  // Идентификатор транзакции в системе Т‑Бизнес.
  PaymentId: string
  // Код ошибки. `0` в случае успеха.
  ErrorCode: string
  // Краткое описание ошибки.
  Message?: string
  // Подробное описание ошибки.
  Details?: string
  // Информация по способу оплаты или деталям для платежей в рассрочку.
  Params: {
    // Возможные значения:
    // * `Route` — способ оплаты.
    // * `Source` — источник платежа.
    // * `CreditAmount` — сумма выданного кредита в копейках. Возвращается только для платежей в рассрочку.
    // 
    Key?: enum[Route, Source, CreditAmount]
    // Возможные значения:
    // * `ACQ`, `BNPL`, `TCB`, `SBER` — для Route.
    // * `BNPL`, `cards`, `Installment`, `MirPay`, `qrsbp`, `SberPay`, `TinkoffPay`, `YandexPay` — для Source.
    // * Сумма в копейках — для CreditAmount.
    // 
    Value?: enum[ACQ, BNPL, TCB, SBER, BNPL, cards, Installment, MirPay, qrsbp, SberPay, TinkoffPay, YandexPay]
  }[]
}
```

### #/components/schemas/ShopsCancel

```ts
// JSON-объект с данными маркетплейса. Обязательный для маркетплейсов.
{
  // Код магазина.
  // 
  ShopCode: string
  // Cумма в копейках, которая относится к
  // указанному `ShopCode`.
  // 
  Amount: number
  // Наименование товара.
  // 
  Name?: string
}
```

### #/components/schemas/Cancel

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор платежа в системе Т‑Бизнес.
  PaymentId: string
  // Подпись запроса — хэш `SHA-256`.
  Token: string
  // IP-адрес клиента.
  IP?: string
  // Сумма в копейках. Если не передан, используется `Amount`, переданный в методе **Init**.
  // 
  // 
  // При отмене статуса `NEW` поле `Amount` игнорируется, даже если оно заполнено. Отмена производится на полную сумму.
  // 
  Amount?: number
  // JSON-объект с данными чека. Обязателен, если подключена онлайн-касса.
  // 
  // Если отмена делается только по части товаров, данные, переданные в этом запросе, могут отличаться данных, переданных в **Init**.
  // При полной отмене структура чека не передается, при частичной — передаются товары, которые нужно отменить.
  Receipt: {
  }
  // JSON-объект с данными маркетплейса. Обязательный для маркетплейсов.
  Shops: {
    // Код магазина.
    // 
    ShopCode: string
    // Cумма в копейках, которая относится к
    // указанному `ShopCode`.
    // 
    Amount: number
    // Наименование товара.
    // 
    Name?: string
  }[]
  // Код банка в классификации СБП, в который нужно выполнить возврат. Смотрите параметр `MemberId` методе [**QrMembersList**](#tag/Oplata-cherez-SBP/paths/~1QrMembersList/post).
  QrMemberId?: string
  // Способ платежа.
  // 
  Route?: enum[TCB, BNPL]
  // Источник платежа.
  // 
  Source?: enum[installment, BNPL]
  // Идентификатор операции на стороне мерчанта. Параметр не работает для операций по СБП. Обязателен для операций «Долями» и в рассрочку.
  // 
  // * Если поле не передано или пустое (""), запрос будет обработан без проверки ранее созданных возвратов.
  // * Если поле заполнено, перед проведением возврата проверяется запрос на отмену с таким `ExternalRequestId`.
  // * Если такой запрос уже есть, в ответе вернется текущее состояние платежной операции, если нет — платеж отменится.
  // * Для операций «Долями» при заполнении параметра нужно генерировать значение в формате `UUID v4`.
  // * Для операций в рассрочку при заполнении параметра нужно генерировать значение с типом `string` — ограничение 100 символов.
  // 
  ExternalRequestId?: string
}
```

### #/components/schemas/Cancel-2

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор заказа в системе мерчанта.
  OrderId: string
  // Успешность прохождения запроса — `true`/`false`.
  Success: boolean
  // Статус транзакции.
  Status: string
  // Сумма в копейках до операции отмены.
  OriginalAmount: number
  // Сумма в копейках после операции отмены.
  NewAmount: number
  // Уникальный идентификатор транзакции в системе Т‑Бизнес.
  PaymentId: number
  // Код ошибки. `0` в случае успеха.
  ErrorCode: string
  // Краткое описание ошибки.
  Message?: string
  // Подробное описание ошибки.
  Details?: string
  // Идентификатор операции на стороне мерчанта.
  ExternalRequestId?: string
}
```

### #/components/schemas/Charge_FULL

```ts
{
  // Идентификатор терминала. <br>
  // Выдается мерчанту в Т‑Бизнес при заведении терминала.
  // 
  TerminalKey: string
  // Уникальный идентификатор транзакции в
  // системе Т‑Бизнес.
  // 
  PaymentId: string
  // Идентификатор рекуррентного платежа. Значение зависит от атрибутов:
  //   * `OperationInitiatorType` в методе **init**,
  //   * `Recurrent` в методе **Init**.
  // 
  // Подробнее — в описаниях [Рекуррентный платёж](#tag/Rekurrentnyj-platyozh) и [Инициализация платежа](#tag/Standartnyj-platyozh/paths/~1Init/post)
  // 
  RebillId: string
  // Подпись запроса.
  // 
  Token: string
  // IP-адрес клиента.
  // 
  IP?: string
  // `true` — если клиент хочет получать
  // уведомления на почту.
  // 
  SendEmail?: boolean
  // Адрес почты клиента.
  // Обязателен при передаче `SendEmail`.
  // 
  InfoEmail?: string
}
```

### #/components/schemas/GetState_FULL

```ts
{
  // Идентификатор терминала. <br>
  // Выдается мерчанту в Т‑Бизнес при заведении терминала.
  // 
  TerminalKey: string
  // Идентификатор платежа в системе Т‑Бизнес.
  // 
  PaymentId: string
  // Подпись запроса.
  // 
  Token: string
  // IP-адрес клиента.
  // 
  IP?: string
}
```

### #/components/schemas/AddCustomer

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  CustomerKey: string
  // Подпись запроса.
  Token: string
  // IP-адрес запроса.
  IP?: string
  // Электронная почта клиента.
  Email?: string
  // Телефон клиента в формате `+{Ц}`.
  // 
  Phone?: string
}
```

### #/components/schemas/AddCustomerResponse

```ts
{
  // Идентификатор терминала, выдается продавцу Т‑Бизнес.
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  CustomerKey: string
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
}
```

### #/components/schemas/GetOrRemoveCustomer

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  CustomerKey: string
  // Подпись запроса.
  Token: string
  // IP-адрес запроса.
  IP?: string
}
```

### #/components/schemas/GetCustomerResponse

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  CustomerKey: string
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
  // Электронная почта клиента.
  Email?: string
  // Телефон клиента в формате `+{Ц}`.
  // 
  Phone?: string
}
```

### #/components/schemas/RemoveCustomerResponse

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  CustomerKey: string
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
}
```

### #/components/schemas/GetAddCardState

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Идентификатор запроса на привязку карты.
  // 
  RequestKey: string
  // Подпись запроса.
  // 
  Token: string
}
```

### #/components/schemas/GetAddCardStateResponse

```ts
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Идентификатор запроса на привязку карты.
  // 
  RequestKey: string
  // Статус привязки карты:
  // * `NEW` — новая сессия привязки карты,
  // * `FORM_SHOWED` — показ формы привязки карты,
  // * `THREE_DS_CHECKING` — отправка клиента на проверку 3DS;
  // * `THREE_DS_CHECKED` — клиент успешно прошел проверку 3DS;
  // * `AUTHORIZING` — отправка платежа на 0 руб;
  // * `AUTHORIZED` — платеж на 0 руб прошел успешно;
  // * `COMPLETED` — карта успешно привязана,
  // * `REJECTED` — привязать карту не удалось.
  // 
  Status: enum[NEW, FORM_SHOWED, THREE_DS_CHECKING, THREE_DS_CHECKED, AUTHORIZING, AUTHORIZED, COMPLETED, REJECTED]
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Идентификатор карты в системе Т‑Бизнес.
  // 
  CardId?: string
  // Идентификатор рекуррентного платежа.
  // 
  RebillId?: string
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode?: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
  // Идентификатор клиента в системе мерчанта.
  // 
  CustomerKey?: string
}
```

### #/components/schemas/GetCardList_FULL

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  // 
  CustomerKey: string
  // Признак сохранения карты для оплаты в 1 клик.
  // 
  SavedCard?: boolean
  // Подпись запроса
  // 
  Token: string
  // IP-адрес запроса
  // 
  IP?: string
}
```

### #/components/schemas/RemoveCard

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Идентификатор клиента в системе мерчанта.
  // 
  CustomerKey: string
  // Идентификатор карты в системе Т‑Бизнес.
  // 
  CardId: string
  // Подпись запроса.
  // 
  Token: string
  // IP-адрес запроса.
  // 
  IP?: string
}
```

### #/components/schemas/RemoveCardResponse

```ts
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Статус карты. `D` — удалена.
  // 
  Status: string
  // Идентификатор клиента в системе мерчанта.
  // 
  CustomerKey: string
  // Идентификатор карты в системе Т‑Бизнес.
  // 
  CardId: string
  // Тип карты:
  // * `0` — карта списания,
  // * `1` — карта пополнения,
  // * `2` — карта пополнения и списания.
  // 
  CardType: enum[0, 1, 2]
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
}
```

### #/components/schemas/GetQr

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Уникальный идентификатор транзакции в системе Т‑Бизнес. Запрос будет работать, даже если указать значение в формате `string`.
  // 
  PaymentId: number
  // Тип возвращаемых данных:
  // * `PAYLOAD` — в ответе возвращается только Payload — по умолчанию;
  // * `IMAGE` — в ответе возвращается SVG изображение QR.
  // 
  DataType?: enum[PAYLOAD, IMAGE] //default: PAYLOAD
  // Подпись запроса.
  Token: string
}
```

### #/components/schemas/QrResponse_FULL

```ts
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Номер заказа в системе мерчанта.
  // 
  OrderId: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // В зависимости от параметра `DataType` в запросе:
  //   * `Payload` — информация, которая должна быть закодирована в QR;
  //   * `SVG` — изображение QR, в котором уже закодирован Payload.
  // 
  Data: string
  // Уникальный идентификатор транзакции в системе Т‑Бизнес.
  // 
  PaymentId: number
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
  // Идентификатор запроса на привязку счета. Передается в случае привязки и одновременной оплаты по CБП.
  // 
  RequestKey: string
}
```

### #/components/schemas/Member

```ts
{
  // Идентификатор участника.
  // 
  MemberId: string
  // Наименование участника.
  // 
  MemberName: string
  // * `true` — если данный участник был получателем
  // указанного платежа, 
  // * `false` — если нет.
  // 
  IsPayee: boolean
}
```

### #/components/schemas/AddAccountQr

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Подробное описание деталей заказа.
  Description: string
  // Тип возвращаемых данных:
  // * `PAYLOAD` — в ответе возвращается только Payload. Значение по умолчанию.
  // * `IMAGE` — в ответе возвращается SVG-изображение QR.
  // 
  DataType?: enum[PAYLOAD, IMAGE] //default: PAYLOAD
  // JSON-объект, содержащий
  // дополнительные параметры в виде `ключ`:`значение`. Эти параметры будут
  // переданы на страницу оплаты, если она
  // кастомизирована. Максимальная длина для
  // каждого передаваемого параметра:
  //   * ключ — 20 знаков,
  //   * значение — 100 знаков.
  // 
  // 
  // Максимальное количество пар `ключ`:`значение` — не больше 20.
  // 
  Data: {
  }
  // Cрок жизни ссылки или динамического QR-кода СБП, если выбран этот способ
  // оплаты. Если параметр `RedirectDueDate` не был передан, проверяется настроечный параметр
  // платежного терминала `REDIRECT_TIMEOUT`, который может содержать значение срока жизни ссылки в 
  // часах. Если его значение больше нуля, оно будет установлено в качестве срока жизни ссылки или
  // динамического QR-кода, если нет — устанавливается значение по умолчанию: 1440 мин (1 сутки).
  // 
  // Если текущая дата превышает дату, переданную в этом параметре, ссылка для оплаты или возможность
  // платежа по QR-коду становятся недоступными и платёж выполнить нельзя.
  // - Максимальное значение — 90 дней от текущей даты.
  // - Минимальное значение — 1 минута от текущей даты.
  // - Формат даты — `YYYY-MM-DDTHH24:MI:SS+GMT`.
  // 
  RedirectDueDate?: string
  // Подпись запроса.
  Token: string
}
```

### #/components/schemas/AddAccountQrResponse

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // В зависимости от параметра `DataType` в запросе:
  //   * `Payload` — информация, которая должна быть закодирована в QR;
  //   * `SVG` — изображение QR, в котором уже закодирован Payload.
  // 
  Data: string
  // Идентификатор запроса на привязку счета.
  // 
  RequestKey: string
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Краткое описание ошибки.
  // 
  Message?: string
}
```

### #/components/schemas/GetAddAccountQrState

```ts
{
  // Идентификатор запроса на привязку счета.
  // 
  RequestKey: string
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Подпись запроса.
  // 
  Token: string
}
```

### #/components/schemas/GetAddAccountQrStateResponse

```ts
{
  // Платежный ключ, выдается мерчанту при заведении
  // терминала.
  // 
  TerminalKey: string
  // Идентификатор запроса на привязку счета.
  // 
  RequestKey: number
  // Идентификатор банка клиента, который будет
  // совершать оплату по привязанному счету —
  // заполнен, если статус `ACTIVE` или `INACTIVE`.
  // 
  BankMemberId?: string
  // Наименование банка-эмитента — заполнен если передан `BankMemberId`.
  // 
  BankMemberName?: string
  // Идентификатор привязки счета, назначаемый банком плательщика.
  // 
  AccountToken?: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Статус привязки карты:
  // * `NEW` — получен запрос на привязку счета;
  // * `PROCESSING` — запрос в обработке,
  // * `ACTIVE` — привязка счета успешна,
  // * `INACTIVE` — привязка счета неуспешна или деактивирована.
  // 
  Status: enum[NEW, PROCESSING, ACTIVE, INACTIVE]
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
}
```

### #/components/schemas/GetAccountQrList

```ts
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Подпись запроса.
  // 
  Token: string
}
```

### #/components/schemas/GetAccountQrListResponse

```ts
{
  // Платежный ключ, выдается мерчанту при заведении
  // терминала.
  // 
  TerminalKey: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
  AccountTokens: {
    // Идентификатор запроса на привязку карты.
    // 
    RequestKey: string
    // Статус привязки карты:
    // * `NEW` — получен запрос на привязку счета;
    // * `PROCESSING` — запрос в обработке,
    // * `ACTIVE` — привязка счета успешна,
    // * `INACTIVE` — привязка счета неуспешна или деактивирована.
    // 
    Status: enum[NEW, PROCESSING, ACTIVE, INACTIVE]
    // Идентификатор привязки счета, назначаемый банком плательщика.
    // 
    AccountToken: {
    }
    // Идентификатор банка клиента (эмитент), который будет
    // совершать оплату по привязанному счету —
    // заполнен, если статус `ACTIVE` или `INACTIVE`.
    // 
    BankMemberId?: string
    // Наименование банка-эмитента — заполнен если передан `BankMemberId`.
    // 
    BankMemberName?: string
  }[]
}
```

### #/components/schemas/ChargeQr

```ts
{
  // Идентификатор терминала. <br>
  // Выдается мерчанту в Т‑Бизнес при заведении терминала.
  // 
  TerminalKey: string
  // Уникальный идентификатор транзакции в
  // системе Т‑Бизнес.
  // 
  PaymentId: string
  // Идентификатор привязки счета,
  // назначаемый банком-эмитентом.
  // 
  AccountToken: string
  // Подпись запроса.
  // 
  Token: string
  // IP-адрес клиента.
  // 
  IP?: string
  // `true`, если клиент хочет получать
  // уведомления на почту.
  // 
  SendEmail?: boolean
  // Адрес почты клиента. Обязательно, если передан параметр `SendEmail` = `true`.
  // 
  InfoEmail?: string
}
```

### #/components/schemas/ChargeQrResponse

```ts
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey: string
  // Сумма в копейках.
  // 
  Amount: number
  // Идентификатор заказа в системе мерчанта.
  // 
  OrderId: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Статус платежа. Возвращается один из трех статусов:
  // * `CONFIRMED` — если платеж выполнен;
  // * `REJECTED` — если платеж не выполнен;
  // * `FORM SHOWED` — если форма оплаты пока что только отображается, и клиент еще не успел провести оплату.
  // 
  Status?: enum[CONFIRMED, REJECTED]
  // Идентификатор платежа в системе Т‑Бизнес.
  // 
  PaymentId: string
  // Код ошибки:
  // * `0` — Успешная операция;
  // * `3013` — Рекуррентные платежи недоступны;
  // * `3015` — Неверный статус AccountToken;
  // * `3040` — Техническая ошибка;
  // * `3037` — Повторный вызов метода недоступен;
  // * `3041` — Слишком много неудачных попыток за сутки. Попробуйте еще раз завтра;
  // * `3042` — Слишком много неудачных попыток за час. Попробуйте снова через час;
  // * `9999` — Повторите попытку позже.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
  // Код валюты по `ISO 4217`.
  Currency?: number
}
```

### #/components/schemas/SbpPayTest

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Идентификатор платежа в системе Т‑Бизнес.
  // 
  PaymentId: string
  // Подпись запроса.
  // 
  Token: string
  // Признак эмуляции отказа проведения платежа банком по таймауту. По умолчанию не используется.
  // * `false` — эмуляция не требуется,
  // * `true` — требуется эмуляция. Не может быть использован вместе с `IsRejected` = `true`.
  // 
  IsDeadlineExpired?: boolean
  // Признак эмуляции отказа банка в проведении платежа. По умолчанию не используется.
  // * `false` — эмуляция не требуется,
  // * `true` — требуется эмуляция. Не может быть использован вместе с `IsDeadlineExpired` = `true`.
  // 
  IsRejected?: boolean
}
```

### #/components/schemas/SbpPayTestResponse

```ts
{
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки
  // 
  Message: string
  // Подробное описание ошибки
  // 
  Details: string
}
```

### #/components/schemas/GetQRStateResponse_FULL

```ts
{
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: enum[true, false]
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Статус платежа. <br>
  // Обязателен, если не произошло ошибки при получении статуса.
  // 
  Status?: string
  // Код ошибки возврата, полученный от СБП.
  // 
  QrCancelCode?: string
  // Дополнительное описание ошибки, произошедшей при возврате по QR.
  // 
  QrCancelMessage?: string
  // Номер заказа в системе мерчанта.
  // 
  OrderId?: string
  // Сумма отмены в копейках.
  // 
  Amount?: number
  // Краткое описание ошибки, произошедшей при запросе статуса.
  // 
  Message?: string
}
```

### #/components/schemas/CheckOrder

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Номер заказа в системе мерчанта. 
  // 
  // Не является уникальным идентификатором.
  // 
  OrderId: string
  // Подпись запроса
  Token: string
}
```

### #/components/schemas/PaymentsCheckOrder

```ts
// Детали
{
  // Уникальный идентификатор транзакции в системе Т‑Бизнес.
  // 
  PaymentId: string
  // Сумма операции в копейках.
  // 
  Amount?: number
  // Статус операции.
  // 
  Status: string
  // RRN операции.
  // 
  RRN?: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: string
  // Код ошибки.
  // 
  ErrorCode?: number
  // Краткое описание ошибки.
  // 
  Message?: string
  // Идентификатор платежа в СБП.
  // 
  SbpPaymentId?: string
  // Хэшированный номер телефона покупателя.
  // 
  SbpCustomerId?: string
}
```

### #/components/schemas/CheckOrder-2

```ts
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор заказа в системе мерчанта.
  OrderId: string
  // Успешность прохождения запроса — `true`/`false`.
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  ErrorCode: string
  // Краткое описание ошибки.
  Message?: string
  // Подробное описание ошибки.
  Details?: string
  // Детали
  Payments: {
    // Уникальный идентификатор транзакции в системе Т‑Бизнес.
    // 
    PaymentId: string
    // Сумма операции в копейках.
    // 
    Amount?: number
    // Статус операции.
    // 
    Status: string
    // RRN операции.
    // 
    RRN?: string
    // Успешность прохождения запроса — `true`/`false`.
    // 
    Success: string
    // Код ошибки.
    // 
    ErrorCode?: number
    // Краткое описание ошибки.
    // 
    Message?: string
    // Идентификатор платежа в СБП.
    // 
    SbpPaymentId?: string
    // Хэшированный номер телефона покупателя.
    // 
    SbpCustomerId?: string
  }[]
}
```

### #/components/schemas/SendClosingReceipt

```ts
{
  // Идентификатор терминала выдается мерчанту в Т‑Бизнес.
  TerminalKey: string
  // Идентификатор платежа в системе Т‑Бизнес.
  PaymentId: string
  // Объект с данными чека.
  Receipt: {
  }
  // Подпись запроса
  Token: string
}
```

### #/components/schemas/SendClosingReceipt-2

```ts
{
  // Успешность прохождения запроса — `true`/`false`.
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  ErrorCode: string
  // Краткое описание ошибки
  Message?: string
}
```

### #/components/schemas/DataNotification

```ts
// Дополнительные параметры платежа, переданные при создании заказа. Являются обязательными для платежей в рассрочку. В ответе параметр приходит в формате <code>Data</code> — не полностью в верхнем регистре.
{
  // Способ платежа.
  // 
  Route?: enum[TCB]
  // Источник платежа.
  // 
  Source?: enum[Installment]
  // Сумма выданного кредита в копейках.
  // 
  CreditAmount?: number
}
```

### #/components/schemas/NotificationPayment

```ts
// **Уведомление о платеже**
// 
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey?: string
  // Сумма в копейках.
  // 
  Amount?: number
  // Идентификатор заказа в системе мерчанта.
  // 
  OrderId?: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success?: boolean
  // Статус платежа.
  // 
  Status?: string
  // Уникальный идентификатор транзакции в системе Т‑Бизнес.
  // 
  PaymentId?: number
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode?: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подробное описание ошибки.
  // 
  Details?: string
  // Идентификатор автоплатежа.
  RebillId?: number
  // Идентификатор карты в системе Т‑Бизнес.
  // 
  CardId?: number
  // Замаскированный номер карты или телефона.
  Pan?: string
  // Срок действия карты
  // в формате `MMYY`, где `YY` — две последние цифры года.
  // 
  ExpDate?: string
  // Подпись запроса. Формируется по такому же принципу, как и в случае
  // запросов в Т‑Бизнес.
  // 
  Token?: string
  // Дополнительные параметры платежа, переданные при создании заказа. Являются обязательными для платежей в рассрочку. В ответе параметр приходит в формате <code>Data</code> — не полностью в верхнем регистре.
  DATA: {
    // Способ платежа.
    // 
    Route?: enum[TCB]
    // Источник платежа.
    // 
    Source?: enum[Installment]
    // Сумма выданного кредита в копейках.
    // 
    CreditAmount?: number
  }
}
```

### #/components/schemas/NotificationAddCard

```ts
// **Уведомления о привязке**
// 
// Уведомления магазину о статусе выполнения метода привязки карты — **AttachCard**.
// После успешного выполнения метода **AttachCard** Т‑Бизнес отправляет POST-запрос с информацией о привязке карты.
// Уведомление отправляется на ресурс мерчанта на адрес `Notification URL` синхронно и ожидает ответа в течение 10 секунд. 
// После получения ответа или не получения его за заданное время сервис переадресует клиента на `Success AddCard URL` 
// или `Fail AddCard URL` — в зависимости от результата привязки карты.
// В случае успешной обработки нотификации мерчант должен вернуть ответ с телом сообщения `OK` — без тегов, заглавными английскими буквами.
// 
// Если тело сообщения отлично от `OK`, любая нотификация считается неуспешной, и сервис будет повторно отправлять
// нотификацию раз в час в течение 24 часов. Если за это время нотификация так и не доставлена, она складывается в дамп.
// 
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес 
  // при заведении терминала.
  // 
  TerminalKey?: string
  // Идентификатор клиента в системе мерчанта.
  // 
  CustomerKey?: string
  // Идентификатор запроса на привязку карты.
  // 
  RequestKey?: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success?: boolean
  // Статус привязки карты.
  // Возвращается один из двух статусов:
  //   * `COMPLETED` — при одностадийной оплате;
  //   * `REJECTED` — при двухстадийной оплате.
  // 
  Status?: enum[COMPLETED, REJECTED]
  // Идентификатор платежа в системе Т‑Бизнес.
  // 
  PaymentId?: number
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode?: string
  // Идентификатор автоплатежа.
  RebillId?: string
  // Идентификатор карты в системе Т‑Бизнес.
  // 
  CardId?: string
  // Замаскированный номер карты.
  Pan?: string
  // Срок действия карты
  // в формате `MMYY`, где `YY` — две последние цифры года.
  // 
  ExpDate?: string
  // Подпись запроса. Формируется по такому же принципу, как и в случае
  // запросов в Т‑Бизнес.
  // 
  Token?: string
}
```

### #/components/schemas/Receipt_FFD_12-2

```ts
// Объект с информацией о видах суммы платежа
{
  // Версия ФФД.
  // Возможные значения:
  // * FfdVersion: `1.2`,
  // * FfdVersion: `1.05`.
  // 
  FfdVersion?: string
  // Информация по клиенту.
  // 
  ClientInfo: {
    // `Тег ФФД: 1243`
    // 
    // 
    // Дата рождения клиента в формате `ДД.ММ.ГГГГ`.
    // 
    Birthdate?: string
    // `Тег ФФД: 1244`
    // 
    // 
    // Числовой код страны, гражданином которой является
    // клиент. Код страны указывается в соответствии с
    // Общероссийским классификатором стран мира [ОКСМ](https://classifikators.ru/oksm).
    // 
    Citizenship?: string
    // `Тег ФФД: 1245`
    // 
    // 
    // Числовой код вида документа, удостоверяющего
    // личность.
    // 
    // Может принимать только следующие значения:
    // 
    // |Код|Описание|
    // |---|---|
    // | 21 | Паспорт гражданина Российской Федерации|
    // | 22 | Паспорт гражданина Российской Федерации, дипломатический паспорт, служебный паспорт, удостоверяющие личность гражданина Российской Федерации за пределами Российской Федерации|
    // | 26 | Временное удостоверение личности гражданина Российской Федерации, выдаваемое на период оформления паспорта гражданина Российской Федерации|
    // | 27 | Свидетельство о рождении гражданина Российской Федерации — для граждан Российской Федерации в возрасте до 14 лет|
    // | 28 | Иные документы, признаваемые документами, удостоверяющими личность гражданина Российской Федерации в соответствии с законодательством Российской Федерации|
    // | 31 | Паспорт иностранного гражданина|
    // | 32 | Иные документы, признаваемые документами, удостоверяющими личность иностранного гражданина в соответствии с законодательством Российской Федерации и международным договором Российской Федерации|
    // | 33 | Документ, выданный иностранным государством и признаваемый в соответствии с международным договором Российской Федерации в качестве документа, удостоверяющего личность лица безгражданства.|
    // | 34 | Вид на жительство — для лиц без гражданства|
    // | 35 | Разрешение на временное проживание — для лиц без гражданства|
    // | 36 | Свидетельство о рассмотрении ходатайства о признании лица без гражданства беженцем на территории Российской Федерации по существу|
    // | 37 | Удостоверение беженца|
    // | 38 | Иные документы, признаваемые документами, удостоверяющими личность лиц без гражданства в соответствии с законодательством Российской Федерации и международным договором Российской Федерации|
    // | 40 | Документ, удостоверяющий личность лица, не имеющего действительного документа, удостоверяющего личность, на период рассмотрения заявления о признании гражданином Российской Федерации или о приеме в гражданство Российской Федерации|
    // 
    DocumentСode?: string
    // `Тег ФФД: 1246`
    // 
    // 
    // Реквизиты документа, удостоверяющего личность — например, серия и номер паспорта.
    // 
    DocumentData?: string
    // `Тег ФФД: 1254`
    // 
    // 
    // Адрес клиента-грузополучателя.
    // 
    Address?: string
  }
  // Система налогообложения. Возможные значения:
  // * `osn` — общая СН;
  // * `usn_income` — упрощенная СН (доходы);
  // * `usn_income_outcome` — упрощенная СН (доходы минус расходы);
  // * `envd` — единый налог на вмененный доход;
  // * `esn` — единый сельскохозяйственный налог;
  // * `patent` — патентная СН.
  // 
  Taxation?: enum[osn, usn_income, usn_income_outcome, envd, esn, patent]
  // Электронная почта клиента.
  // 
  Email?: string
  // Телефон клиента в формате `+{Ц}`.
  // 
  Phone?: string
  // Идентификатор/имя клиента.
  Customer?: string
  // ИНН клиента.
  CustomerInn?: string
  Items: {
    // Данные агента. Обязателен, если используется агентская схема.
    // 
    AgentData: {
      // `Тег ФФД: 1222`
      // 
      // 
      // Признак агента. Возможные значения:
      // * `bank_paying_agent` — банковский платежный агент,
      // * `bank_paying_subagent` — банковский платежный субагент,
      // * `paying_agent` — платежный агент,
      // * `paying_subagent` — платежный субагент,
      // * `attorney` — поверенный,
      // * `commission_agent` — комиссионер,
      // * `another` — другой тип агента.
      // 
      AgentSign?: string
      // `Тег ФФД: 1044`
      // 
      // 
      // Наименование операции.
      // Параметр обязательный, если `AgentSign` передан в значениях:
      // * `bank_paying_agent`,
      // * `bank_paying_subagent`.
      // 
      OperationName?: string
      Phones?: string[]
      // `Тег ФФД: 1026`
      // 
      // 
      // Наименование оператора перевода.
      // Параметр обязательный, если в `AgentSign` передан в значениях:
      // * `bank_paying_agent`,
      // * `bank_paying_subagent`.
      // 
      OperatorName?: string
      // `Тег ФФД: 1005`
      // 
      // 
      // Адрес оператора перевода.
      // Параметр обязательный, если в `AgentSign` передан в значениях:
      // * `bank_paying_agent`,
      // * `bank_paying_subagent`.
      // 
      OperatorAddress?: string
      // `Тег ФФД: 1016`
      // 
      // 
      // ИНН оператора перевода.
      // Параметр обязательный, если в `AgentSign` передан в значениях:
      // * `bank_paying_agent`,
      // * `bank_paying_subagent`.
      // 
      OperatorInn?: string
    }
    // Данные поставщика платежного агента. 
    // Обязателен, если передается значение `AgentSign` в объекте `AgentData`.
    // 
    SupplierInfo: {
      Phones?: string[]
      // `Тег ФФД: 1225`
      // 
      // 
      // Наименование поставщика.
      // Атрибут обязателен, если передается значение `AgentSign` 
      // в объекте `AgentData`.
      // Внимание: в эти 239 символов включаются телефоны поставщика 
      // + 4 символа на каждый телефон.
      // 
      // 
      // Например, если передано два телефона поставщика длиной 12 и 14 символов, 
      // максимальная длина наименования поставщика будет 
      // 239 – (12 + 4) – (14 + 4) = 205 символов.
      // 
      Name?: string
      // `Тег ФФД: 1226`
      // 
      // 
      // ИНН поставщика в формате `ЦЦЦЦЦЦЦЦЦЦ`.
      // Атрибут обязателен, если передается значение `AgentSign` 
      // в объекте `AgentData`.
      // 
      Inn?: string
    }
    // `Тег ФФД: 1030`
    // 
    // 
    // Наименование товара.
    // 
    Name: string
    // `Тег ФФД: 1079`
    // 
    // 
    // Цена в копейках.
    // 
    Price: number
    // `Тег ФФД: 1023`
    // 
    // 
    // Количество или вес товара.
    // * Максимальное количество символов — 8, где целая часть — не больше 5 знаков, дробная — не больше 3 знаков для Атол и
    // 2 знаков для CloudPayments.
    // * Значение `1`, если передан объект `MarkCode`.
    // 
    Quantity: number
    // `Тег ФФД: 1043`
    // 
    // 
    // Стоимость товара в копейках.
    // Произведение `Quantity` и `Price`.
    // 
    Amount: number
    // `Тег ФФД: 1199`
    // 
    // 
    // Ставка НДС.
    // Возможные значения:
    // * `none` — без НДС,
    // * `vat0` — НДС по ставке 0%;
    // * `vat5` — НДС по ставке 5%;
    // * `vat7` — НДС по ставке 7%;
    // * `vat10` — НДС по ставке 10%;
    // * `vat20` — НДС по ставке 20%;
    // * `vat105` — НДС чека по расчетной ставке 5/105;
    // * `vat107` — НДС чека по расчетной ставке 7/107;
    // * `vat110` — НДС чека по расчетной ставке 10/110;
    // * `vat120` — НДС чека по расчетной ставке 20/120.
    // 
    Tax: enum[none, vat0, vat5, vat7, vat10, vat20, vat105, vat107, vat110, vat120]
    // `Тег ФФД: 1214`
    // 
    //  
    // Возможные значения:
    //  * `full_prepayment` — предоплата 100%,
    //  * `prepayment` — предоплата,
    //  * `advance` — аванс,
    //  * `full_payment` — полный расчет,
    //  * `partial_payment` — частичный расчет и кредит,
    //  * `credit` — передача в кредит,
    //  * `credit_payment` — оплата кредита. 
    // 
    // 
    // Если значение не передано, по умолчанию в онлайн-кассу
    // передается признак способа расчёта
    // `full_payment`.
    // 
    PaymentMethod: enum[full_prepayment, prepayment, advance, full_payment, partial_payment, credit, credit_payment]
    // `Тег ФФД: 1212`
    // 
    // 
    // Значения реквизита «признак предмета расчета» — тег 1212, таблица 101.
    // Возможные значения:
    // * `commodity` — товар,
    // * `excise` — подакцизный товар,
    // * `job` — работа,
    // * `service` — услуга,
    // * `gambling_bet` — ставка азартной игры,
    // * `gambling_prize` — выигрыш азартной игры,
    // * `lottery` — лотерейный билет,
    // * `lottery_prize` — выигрыш лотереи,
    // * `intellectual_activity` — предоставление,
    //   результатов интеллектуальной деятельности,
    // * `payment` — платеж,
    // * `agent_commission` — агентское
    //   вознаграждение,
    // * `contribution` — выплата,
    // * `property_rights` — имущественное право,
    // * `unrealization` — внереализационный доход,
    // * `tax_reduction` — иные платежи и взносы,
    // * `trade_fee` — торговый сбор,
    // * `resort_tax` — курортный сбор,
    // * `pledge` — залог,
    // * `income_decrease` — расход,
    // * `ie_pension_insurance_without_payments` — взносы на ОПС ИП,
    // * `ie_pension_insurance_with_payments` — взносы на ОПС,
    // * `ie_medical_insurance_without_payments` — взносы на ОМС ИП,
    // * `ie_medical_insurance_with_payments` — взносы на ОМС,
    // * `social_insurance` — взносы на ОСС,
    // * `casino_chips` — платеж казино,
    // * `agent_payment` — выдача ДС,
    // * `excisable_goods_without_marking_code` — АТНМ,
    // * `excisable_goods_with_marking_code` — АТМ,
    // * `goods_without_marking_code` — ТНМ,
    // * `goods_with_marking_code` — ТМ,
    // * `another` — иной предмет расчета.
    // 
    PaymentObject: enum[commodity, excise, job, service, gambling_bet, gambling_prize, lottery, lottery_prize, intellectual_activity, payment, agent_commission, contribution, property_rights, unrealization, tax_reduction, trade_fee, resort_tax, pledge, income_decrease, ie_pension_insurance_without_payments, ie_pension_insurance_with_payments, ie_medical_insurance_without_payments, ie_medical_insurance_with_payments, social_insurance, casino_chips, agent_payment, excisable_goods_without_marking_code, excisable_goods_with_marking_code, goods_without_marking_code, goods_with_marking_code, another]
    // `Тег ФФД: 1191`
    // 
    // 
    // Дополнительный реквизит предмета расчета.
    // 
    UserData?: string
    // `Тег ФФД: 1229`
    // 
    // 
    // Сумма акциза в рублях с учетом копеек,
    // включенная в стоимость предмета расчета:
    // * целая часть — не больше 8 знаков;
    // * дробная часть — не больше 2 знаков;
    // * значение не может быть отрицательным.
    // 
    Excise?: string
    // `Тег ФФД: 1230`
    // 
    // 
    // Цифровой код страны происхождения товара в
    // соответствии с Общероссийским
    // классификатором стран мира — 3 цифры.
    // 
    CountryCode?: string
    // `Тег ФФД: 1231`
    // 
    // 
    // Номер таможенной декларации.
    // 
    DeclarationNumber?: string
    // `Тег ФФД: 2108`
    // 
    // 
    // Единицы измерения.
    // 
    // 
    // Возможные варианты описаны в разделе<a href="https://www.tbank.ru/kassa/dev/payments/#tag/Opisanie-dopolnitelnyh-obuektov" target="_blank"> дополнительных объектов</a>. Также возможна передача произвольных значений.
    // 
    // `MeasurementUnit` обязателен, если версия ФД онлайн-кассы — 1.2.
    // 
    MeasurementUnit: string
    // `Тег ФФД: 2102`
    // 
    // 
    // Режим обработки кода маркировки.
    // Должен принимать значение, равное `0`.
    // Включается в чек , если предметом расчета 
    // является товар, подлежащий обязательной
    // маркировке средством идентификации — соответствующий код в поле `paymentObject`.
    // 
    MarkProcessingMode?: string
    // Код маркировки в машиночитаемой форме,
    // представленный в виде одного из видов кодов,
    // формируемых в соответствии с требованиями,
    // предусмотренными правилами, для нанесения
    // на потребительскую упаковку, или на товары,
    // или на товарный ярлык
    // 
    // 
    // Включается в чек, если предметом расчета является товар, подлежащий обязательной маркировке средством идентификации — соответствующий 
    // код в поле `paymentObject`.
    // 
    MarkCode: {
      // Тип штрих кода.
      // Возможные значения:
      // * `UNKNOWN` — код товара, формат которого не
      // идентифицирован, как один из реквизитов;
      // * `EAN8` — код товара в формате EAN-8;
      // * `EAN13` — код товара в формате EAN-13;
      // * `ITF14` — код товара в формате ITF-14;
      // * `GS10` — код товара в формате GS1,
      // нанесенный на товар, не подлежащий
      // маркировке;
      // * `GS1M` — код товара в формате GS1,
      // нанесенный на товар, подлежащий
      // маркировке;
      // * `SHORT` — код товара в формате короткого кода
      // маркировки, нанесенный на товар;
      // * `FUR` — контрольно-идентификационный знак
      // мехового изделия;
      // * `EGAIS20` — код товара в формате ЕГАИС-2.0;
      // * `EGAIS30` — код товара в формате ЕГАИС-3.0;
      // * `RAWCODE` — код маркировки, как он был прочитан сканером.
      // 
      MarkCodeType: string
      // Код маркировки.
      // 
      Value: string
    }
    // Реквизит «дробное количество маркированного товара».
    // Передается, только если расчет осуществляется 
    // за маркированный товар — соответствующий код в поле 
    // `paymentObject`, и значение в поле `measurementUnit` 
    // равно `0`.
    // 
    // `MarkQuantity` не является обязательным объектом, в том числе для товаров с маркировкой. Этот объект можно передавать, 
    //  если товар с маркировкой. То есть даже при ФФД 1.2 этот объект не является обязательным.
    //  
    // 
    // Пример: 
    // ```
    //       {
    //       "numenator": "1"
    //       "denominator" "2"  
    //       }
    // ```
    // 
    MarkQuantity: {
      // `Тег ФФД: 1293`
      // 
      // 
      // Числитель дробной части предмета расчета. 
      // Значение должно быть строго меньше
      // значения реквизита «знаменатель».
      // 
      Numerator?: number
      // `Тег ФФД: 1294`
      // 
      // 
      // Знаменатель дробной части предмета расчета. 
      // Значение равно количеству товара в партии (упаковке), 
      // имеющей общий код маркировки товара.
      // 
      Denominator?: number
    }
    // Отраслевой реквизит предмета расчета. Указывается только для товаров подлежащих обязательной маркировке средством
    // идентификации. Включение этого реквизита предусмотрено НПА отраслевого регулирования для
    // соответствующей товарной группы.
    // 
    SectoralItemProps: {
      // `Тег ФФД: 1262`
      // 
      // 
      // Идентификатор ФОИВ — федеральный орган
      // исполнительной власти.
      // 
      FederalId: string
      // `Тег ФФД: 1263`
      // 
      // 
      // Дата нормативного акта ФОИВ.
      // 
      Date: string
      // `Тег ФФД: 1264`
      // 
      // 
      // Номер нормативного акта ФОИВ.
      // 
      Number: string
      // `Тег ФФД: 1265`
      // 
      // 
      // Состав значений, определенных нормативным актом ФОИВ.
      // 
      Value: string
    }[]
  }[]
  // Детали платежа.
  // 
  // 
  // Если объект не передан, автоматически указывается итоговая сумма чека с видом оплаты «Безналичный». 
  //  
  // Если передан объект `receipt.Payments`, значение в `Electronic` должно быть равно итоговому значению `Amount` в методе **Init**.
  // При этом сумма введенных значений по всем видам оплат, включая `Electronic`, должна быть равна сумме (**Amount**) всех товаров,
  // переданных в объекте `receipt.Items`.
  // 
  Payments: {
    // `Тег ФФД: 1031.`<br>
    // 
    // Вид оплаты «Наличные».
    // Сумма к оплате в копейках.
    // 
    Cash?: number
    // `Тег ФФД: 1081.`<br>
    // 
    // Вид оплаты «Безналичный».
    // 
    Electronic: number
    // `Тег ФФД: 1215.`<br>
    // 
    // Вид оплаты «Предварительная оплата (Аванс)».
    // 
    AdvancePayment?: number
    // `Тег ФФД: 1216.`<br>
    // 
    // Вид оплаты «Постоплата (Кредит)».
    // 
    Credit?: number
    // `Тег ФФД: 1217.`<br>
    // 
    // Вид оплаты «Иная форма оплаты».
    // 
    Provision?: number
  }[]
}
```

### #/components/schemas/Receipt_FFD_105-2

```ts
// Объект с информацией о видах суммы платежа.
{
  Items: {
    // `Тег ФФД: 1030`
    // 
    // 
    // Наименование товара.
    // 
    Name: string
    // `Тег ФФД: 1078`
    // 
    // 
    // Цена в копейках.
    // 
    Price: number
    // `Тег ФФД: 1023`
    // 
    // 
    // Количество или вес товара.
    // Максимальное количество символов — 8, где целая часть — не больше 5 знаков, дробная — не больше 3 знаков для АТОЛ, 
    // и 2 знаков для CloudPayments.
    // 
    Quantity: number
    // `Тег ФФД: 1043`
    // 
    // 
    // Стоимость товара в копейках.
    // Произведение `Quantity` и `Price`.
    // 
    Amount: number
    // `Тег ФФД: 1214`
    // 
    // 
    // Возможные значения:
    // * `full_prepayment` — предоплата 100%,
    // * `prepayment` — предоплата,
    // * `advance` — аванс,
    // * `full_payment` — полный расчет,
    // * `partial_payment` — частичный расчет и кредит,
    // * `credit` — передача в кредит,
    // * `credit_payment` — оплата кредита.
    // 
    // 
    // Если значение не
    // передано, по умолчанию в онлайн-кассу
    // передается признак способа расчёта
    // `full_payment`.
    // 
    PaymentMethod?: enum[full_prepayment, prepayment, advance, full_payment, partial_payment, credit, credit_payment] //default: full_payment
    // `Тег ФФД: 1212`
    // 
    // 
    // Признак предмета расчета.
    // Возможные значения:
    // * `commodity` — товар,
    // * `excise` — подакцизный товар,
    // * `job` — работа,
    // * `service` — услуга,
    // * `gambling_bet` — ставка азартной игры,
    // * `gambling_prize` — выигрыш азартной игры,
    // * `lottery` — лотерейный билет,
    // * `lottery_prize` — выигрыш лотереи,
    // * `intellectual_activity` — предоставление результатов интеллектуальной деятельности,
    // * `payment` — платеж,
    // * `agent_commission` — агентское вознаграждение,
    // * `composite` — составной предмет расчета,
    // * `another` — иной предмет расчета,
    // 
    // 
    // Если значение не передано, по умолчанию в онлайн-кассу
    // отправляется признак предмета расчёта `commodity`.
    // 
    PaymentObject?: enum[commodity, excise, job, service, gambling_bet, gambling_prize, lottery, lottery_prize, intellectual_activity, payment, agent_commission, composite, another] //default: commodity
    // `Тег ФФД: 1199`
    // 
    // 
    // Ставка НДС.
    // Перечисление со значениями:
    // * `none` — без НДС,
    // * `vat0` — НДС по ставке 0%,
    // * `vat5` — НДС по ставке 5%,
    // * `vat7` — НДС по ставке 7%,
    // * `vat10` — НДС по ставке 10%,
    // * `vat20` — НДС по ставке 20%,
    // * `vat105` — НДС чека по расчетной ставке 5/105,
    // * `vat107` — НДС чека по расчетной ставке 7/107,
    // * `vat110` — НДС чека по расчетной ставке 10/110,
    // * `vat120` — НДС чека по расчетной ставке 20/120.
    // 
    Tax: enum[none, vat0, vat5, vat7, vat10, vat20, vat105, vat107, vat110, vat120]
    // `Тег ФФД: 1162`
    // 
    // 
    // Штрих-код в требуемом формате. В зависимости от
    // типа кассы требования могут отличаться:
    // * АТОЛ Онлайн — шестнадцатеричное
    // представление с пробелами. Максимальная
    // длина – 32 байта (^[a-fA-F0-9]{2}$)|(^([afA-F0-9]{2}\\s){1,31}[a-fA-F0-9]{2}$).
    // 
    // Пример:
    // `00 00 00 01 00 21 FA 41 00 23 05 41 00
    // 00 00 00 00 00 00 00 00 00 00 00 00 00
    // 00 00 12 00 AB 00`
    // 
    // * CloudKassir — длина строки: четная, от 8 до
    // 150 байт. То есть от 16 до 300 ASCII символов
    // ['0' - '9' , 'A' - 'F' ] шестнадцатеричного
    // представления кода маркировки товара.
    // 
    // Пример:
    // `303130323930303030630333435`
    // 
    // 
    // * OrangeData — строка, содержащая `base64`-
    // кодированный массив от 8 до 32 байт.
    // 
    // Пример:
    // `igQVAAADMTIzNDU2Nzg5MDEyMwAAAAAAAQ==`
    // 
    // Если в запросе передается параметр Ean13, не
    // прошедший валидацию, возвращается неуспешный
    // ответ с текстом ошибки в параметре `message` =
    // `Неверный параметр Ean13`.
    // 
    Ean13?: string
    // Код магазина. Для параметра `ShopСode`
    // нужно использовать значение параметра
    // `Submerchant_ID`, который возвращается в ответн при
    // регистрации магазинов через XML. Если XML не
    // используется, передавать поле не нужно.
    // 
    ShopCode?: string
    // Данные агента. Обязателен, если используется агентская схема.
    // 
    AgentData: {
      // `Тег ФФД: 1222`
      // 
      // 
      // Признак агента. Возможные значения:
      // * `bank_paying_agent` — банковский платежный агент,
      // * `bank_paying_subagent` — банковский платежный субагент,
      // * `paying_agent` — платежный агент,
      // * `paying_subagent` — платежный субагент,
      // * `attorney` — поверенный,
      // * `commission_agent` — комиссионер,
      // * `another` — другой тип агента.
      // 
      AgentSign?: string
      // `Тег ФФД: 1044`
      // 
      // 
      // Наименование операции.
      // Параметр обязательный, если `AgentSign` передан в значениях:
      // * `bank_paying_agent`,
      // * `bank_paying_subagent`.
      // 
      OperationName?: string
      Phones?: string[]
      // `Тег ФФД: 1026`
      // 
      // 
      // Наименование оператора перевода.
      // Параметр обязательный, если в `AgentSign` передан в значениях:
      // * `bank_paying_agent`,
      // * `bank_paying_subagent`.
      // 
      OperatorName?: string
      // `Тег ФФД: 1005`
      // 
      // 
      // Адрес оператора перевода.
      // Параметр обязательный, если в `AgentSign` передан в значениях:
      // * `bank_paying_agent`,
      // * `bank_paying_subagent`.
      // 
      OperatorAddress?: string
      // `Тег ФФД: 1016`
      // 
      // 
      // ИНН оператора перевода.
      // Параметр обязательный, если в `AgentSign` передан в значениях:
      // * `bank_paying_agent`,
      // * `bank_paying_subagent`.
      // 
      OperatorInn?: string
    }
    // Данные поставщика платежного агента. 
    // Обязателен, если передается значение `AgentSign` в объекте `AgentData`.
    // 
    SupplierInfo: {
      Phones?: string[]
      // `Тег ФФД: 1225`
      // 
      // 
      // Наименование поставщика.
      // Атрибут обязателен, если передается значение `AgentSign` 
      // в объекте `AgentData`.
      // Внимание: в эти 239 символов включаются телефоны поставщика 
      // + 4 символа на каждый телефон.
      // 
      // 
      // Например, если передано два телефона поставщика длиной 12 и 14 символов, 
      // максимальная длина наименования поставщика будет 
      // 239 – (12 + 4) – (14 + 4) = 205 символов.
      // 
      Name?: string
      // `Тег ФФД: 1226`
      // 
      // 
      // ИНН поставщика в формате `ЦЦЦЦЦЦЦЦЦЦ`.
      // Атрибут обязателен, если передается значение `AgentSign` 
      // в объекте `AgentData`.
      // 
      Inn?: string
    }
  }[]
  // Версия ФФД.
  // Возможные значения:
  // * FfdVersion: `1.2`,
  // * FfdVersion: `1.05`.
  // 
  // Версия ФФД по умолчанию — `1.05`.
  // 
  FfdVersion?: string //default: 1.05
  // Электронная почта клиента.
  // Должен быть заполнен, если не передано значение 
  // в параметре `Phone`.
  // 
  Email?: string
  // Телефон клиента в формате `+{Ц}`.
  // 
  Phone?: string
  // Система налогообложения. Возможные значения:
  // * `osn` — общая СН;
  // * `usn_income` — упрощенная СН (доходы);
  // * `usn_income_outcome` — упрощенная СН (доходы минус расходы);
  // * `envd` — единый налог на вмененный доход;
  // * `esn` — единый сельскохозяйственный налог;
  // * `patent` — патентная СН.
  // 
  Taxation?: enum[osn, usn_income, usn_income_outcome, envd, esn, patent]
  // Детали платежа.
  // 
  // 
  // Если объект не передан, автоматически указывается итоговая сумма чека с видом оплаты «Безналичный». 
  //  
  // Если передан объект `receipt.Payments`, значение в `Electronic` должно быть равно итоговому значению `Amount` в методе **Init**.
  // При этом сумма введенных значений по всем видам оплат, включая `Electronic`, должна быть равна сумме (**Amount**) всех товаров,
  // переданных в объекте `receipt.Items`.
  // 
  Payments: {
    // `Тег ФФД: 1031.`<br>
    // 
    // Вид оплаты «Наличные».
    // Сумма к оплате в копейках.
    // 
    Cash?: number
    // `Тег ФФД: 1081.`<br>
    // 
    // Вид оплаты «Безналичный».
    // 
    Electronic: number
    // `Тег ФФД: 1215.`<br>
    // 
    // Вид оплаты «Предварительная оплата (Аванс)».
    // 
    AdvancePayment?: number
    // `Тег ФФД: 1216.`<br>
    // 
    // Вид оплаты «Постоплата (Кредит)».
    // 
    Credit?: number
    // `Тег ФФД: 1217.`<br>
    // 
    // Вид оплаты «Иная форма оплаты».
    // 
    Provision?: number
  }[]
}
```

### #/components/schemas/NotificationFiscalization

```ts
// **Уведомление о фискализации**
// 
// Если используется подключенная онлайн касса, по результату фискализации будет
// отправлено уведомление с фискальными данными.
// 
{
  // Идентификатор терминала. Выдается мерчанту в Т‑Бизнес
  // при заведении терминала.
  // 
  TerminalKey?: string
  // Идентификатор заказа в системе мерчанта.
  // 
  OrderId?: string
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success?: boolean
  // Для уведомлений о фискализации — всегда `RECEIPT`.
  // 
  Status?: string //default: RECEIPT
  // Идентификатор платежа в системе Т‑Бизнес.
  // 
  PaymentId?: number
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode?: string
  // Описание ошибки, если она произошла.
  ErrorMessage?: string
  // Сумма в копейках.
  // 
  Amount?: number
  // Номер чека в смене.
  FiscalNumber?: integer
  // Номер смены.
  ShiftNumber?: integer
  // Дата и время документа из ФН.
  ReceiptDatetime?: string
  // Номер ФН.
  FnNumber?: string
  // Регистрационный номер ККТ.
  EcrRegNumber?: string
  // Фискальный номер документа.
  FiscalDocumentNumber?: integer
  // Фискальный признак документа.
  FiscalDocumentAttribute?: integer
  // Состав чека
  Receipt: {
  }
  // Признак расчета.
  Type?: string
  // Подпись запроса. Формируется по такому же принципу, как и в случае
  // запросов в Т‑Бизнес.
  // 
  Token?: string
  // Наименование оператора фискальных данных.
  Ofd?: string
  // URL-адрес с копией чека.
  Url?: string
  // URL-адрес с QR-кодом для проверки чека в ФНС.
  QrCodeUrl?: string
  // Место осуществления расчетов.
  CalculationPlace?: string
  // Имя кассира.
  CashierName?: string
  // Место нахождения (установки) ККМ.
  SettlePlace?: string
}
```

### #/components/schemas/NotificationQr

```ts
// **Уведомление о статусе привязки счета по QR**
// 
// После привязки счета по QR магазину отправляется статус привязки и токен.
// Уведомление будет приходить по статусам `ACTIVE` и `INACTIVE`.
// 
{
  // Идентификатор терминала, выдается мерчанту в Т‑Бизнес.
  // 
  TerminalKey: string
  // Идентификатор запроса на привязку счета.
  // 
  RequestKey: string
  // Идентификатор привязки счета,
  // назначаемый банком-эмитентом.
  // 
  AccountToken?: string
  // Идентификатор банка-эмитента клиента, который будет
  // совершать оплату по привязанному счету —
  // заполнен, если статус `ACTIVE`.
  // 
  BankMemberId?: string
  // Наименование банка-эмитента. Заполнен, если передан `BankMemberId`.
  // 
  BankMemberName?: string
  // Тип уведомления, всегда — `LINKACCOUNT`.
  NotificationType: string //default: LINKACCOUNT
  // Успешность прохождения запроса — `true`/`false`.
  // 
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  // 
  ErrorCode: string
  // Краткое описание ошибки.
  // 
  Message?: string
  // Подпись запроса. Формируется по такому же
  // принципу, как и в случае запросов в Т‑Бизнес.
  // 
  Token: string
  // Статус привязки.
  Status: string
}
```

### #/components/schemas/GetDeepLink

```ts
{
  // Идентификатор терминала. <br> Выдается мерчанту в Т‑Бизнес при заведении терминала.
  TerminalKey: string
  // Уникальный идентификатор транзакции в системе Банка.
  PaymentId: string
  // Подпись запроса.
  Token: string
}
```

### #/components/schemas/GetDeepLinkResponse

```ts
{
  // Успешность прохождения запроса — `true`/`false`.
  Success: enum[true, false]
  // Код ошибки. `0` в случае успеха.
  ErrorCode: string
  // Сформированный и подписанный JWT-токеном deeplink.
  Deeplink?: string
  // Краткое описание ошибки.
  Message?: string
  // Подробное описание ошибки.
  Details?: string
}
```

### #/components/schemas/GetTerminalPayMethods

```ts
{
  // Идентификатор терминала. <br> Выдается мерчанту в Т‑Бизнес при заведении терминала.
  TerminalKey: string
  // Тип подключения:   
  // - API;   
  // - SDK.
  // 
  Paysource: string
}
```

### #/components/schemas/Paymethod

```ts
// Перечень доступных методов оплаты
{
  // Доступные методы оплаты:
  // * TinkoffPay,
  // * YandexPay,
  // * ApplePay,
  // * GooglePay,
  // * SBP,
  // * MirPay.
  // 
  PayMethod: string
  // Перечень параметров подключения в формате ключзначение
  Params: {
  }
}
```

### #/components/schemas/GetTerminalPayMethodsResponse

```ts
{
  // Успешность прохождения запроса — `true`/`false`.
  Success: enum[true, false]
  // Код ошибки. `0` в случае успеха.
  ErrorCode: string
  // Краткое описание ошибки.
  Message?: string
  // Подробное описание ошибки.
  Details?: string
  // Характеристики терминала.
  TerminalInfo: {
  }
  // Перечень доступных методов оплаты
  TerminalInfo.Paymethods: {
    // Доступные методы оплаты:
    // * TinkoffPay,
    // * YandexPay,
    // * ApplePay,
    // * GooglePay,
    // * SBP,
    // * MirPay.
    // 
    PayMethod: string
    // Перечень параметров подключения в формате ключзначение
    Params: {
    }
  }[]
  // Признак возможности сохранения карт.
  TerminalInfo.AddCardScheme: enum[true, false]
  // Признак необходимости подписания токеном.
  TerminalInfo.TokenRequired: enum[true, false]
  // Признак необходимости подписания токеном запроса **Init**.
  TerminalInfo.InitTokenRequired: enum[true, false]
}
```

### #/components/schemas/by_url

```ts
{
  "title": "URL",
  "required": [
    "TerminalKey",
    "CallbackUrl",
    "PaymentIdList",
    "Token"
  ],
  "properties": {
    "TerminalKey": {
      "type": "string",
      "description": "Идентификатор терминала, выдается мерчанту в Т‑Бизнес.",
      "example": "TinkoffBankTest"
    },
    "CallbackUrl": {
      "type": "string",
      "description": "URL сервиса получения справок.",
      "example": "https://www.tinkoff.ru"
    },
    "PaymentIdList": {
      "type": "array",
      "description": "JSON-массив, содержащий в себе перечень `paymentID` (уникальных идентификаторов в системе Т‑Бизнес) c типом `Number`.",
      "example": [
        **********,
        **********
      ],
      "items": {
        "type": "number"
      }
    },
    "Token": {
      "type": "string",
      "description": "Подпись запроса.",
      "example": "f2fdd7fec8225872590e1558b7ea258c75df8f300d808006c41ab540dd7514d9"
    }
  }
}
```

### #/components/schemas/by_email

```ts
{
  "title": "Email",
  "required": [
    "TerminalKey",
    "PaymentIdList",
    "EmailList",
    "Token"
  ],
  "properties": {
    "TerminalKey": {
      "type": "string",
      "description": "Идентификатор терминала, выдается мерчанту в Т‑Бизнес.",
      "example": "TinkoffBankTest"
    },
    "PaymentIdList": {
      "type": "array",
      "description": "JSON-массив, содержащий в себе перечень `paymentID` (уникальных идентификаторов в системе Т‑Бизнес) c типом `Number`.",
      "example": [
        **********,
        **********
      ],
      "items": {
        "type": "number"
      }
    },
    "EmailList": {
      "type": "array",
      "description": "JSON-массив, содержащий перечень `Email` с типом `String`.",
      "example": [
        "<EMAIL>",
        "<EMAIL>"
      ],
      "items": {
        "type": "string",
        "properties": {
          "Email": {
            "type": "string"
          }
        }
      }
    },
    "Token": {
      "type": "string",
      "description": "Подпись запроса.",
      "example": "f2fdd7fec8225872590e1558b7ea258c75df8f300d808006c41ab540dd7514d9"
    }
  }
}
```

### #/components/schemas/PaymentIdListForGCO

```ts
// JSON-массив с объектами, содержащими информацию по запросу.
{
  // Успешность прохождения запроса — `true`/`false`.
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  ErrorCode: string
  // Сервисное сообщение.
  Message: string
  // Идентификатор операции.
  PaymentId: number
}
```

### #/components/schemas/response_by_url

```ts
{
  // Успешность прохождения запроса — `true`/`false`.
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  ErrorCode: string
  // Краткое описание ошибки.
  Message?: string
  // JSON-массив с объектами, содержащими информацию по запросу.
  PaymentIdList: {
    // Успешность прохождения запроса — `true`/`false`.
    Success: boolean
    // Код ошибки. `0` в случае успеха.
    ErrorCode: string
    // Сервисное сообщение.
    Message: string
    // Идентификатор операции.
    PaymentId: number
  }[]
}
```

### #/components/schemas/response_by_email

```ts
{
  // Успешность прохождения запроса — `true`/`false`.
  Success: boolean
  // Код ошибки. `0` в случае успеха.
  ErrorCode: string
  // Краткое описание ошибки.
  Message?: string
  // JSON-массив с объектами, содержащими информацию по запросу.
  PaymentIdList: {
    // Успешность прохождения запроса — `true`/`false`.
    Success: boolean
    // Код ошибки. `0` в случае успеха.
    ErrorCode: string
    // Сервисное сообщение.
    Message: string
    // Идентификатор операции.
    PaymentId: number
  }[]
}
```
