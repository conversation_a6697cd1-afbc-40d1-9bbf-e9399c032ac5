---
type: "manual"
---

# Endpoint: Inbounds

## Description

This route is used to retrieve a comprehensive list of all inbounds along with their associated client options and statistics. This includes information about each inbound, its settings, and the clients connected to it.
If no inbounds are available, the response will contain an empty array `[]`.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Handle any potential errors or failure messages returned in the response.

### Method: GET

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/api/inbounds/list
> ```

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Response: 200 (with data)

```json
{
    "success": true,
    "msg": "",
    "obj": [
        {
            "id": 3,
            "up": 0,
            "down": 0,
            "total": 0,
            "remark": "",
            "enable": true,
            "expiryTime": 0,
            "clientStats": [
                {
                    "id": 3,
                    "inboundId": 3,
                    "enable": true,
                    "email": "hyvcs325",
                    "up": 0,
                    "down": 0,
                    "expiryTime": 0,
                    "total": 0,
                    "reset": 0
                },
                {
                    "id": 5,
                    "inboundId": 3,
                    "enable": true,
                    "email": "27225ost",
                    "up": 0,
                    "down": 0,
                    "expiryTime": 0,
                    "total": 0,
                    "reset": 0
                }
            ],
            "listen": "",
            "port": 37155,
            "protocol": "vless",
            "settings": "{\n  \"clients\": [\n    {\n      \"email\": \"hyvcs325\",\n      \"enable\": true,\n      \"expiryTime\": 0,\n      \"flow\": \"\",\n      \"id\": \"819920c0-22c8-4c83-8713-9c3da4980396\",\n      \"limitIp\": 0,\n      \"reset\": 0,\n      \"subId\": \"jmrwimzhicxm7hrm\",\n      \"tgId\": \"\",\n      \"totalGB\": 0\n    },\n    {\n      \"email\": \"27225ost\",\n      \"enable\": true,\n      \"expiryTime\": 0,\n      \"flow\": \"\",\n      \"id\": \"bf036995-a81d-41b3-8e06-8e233418c96a\",\n      \"limitIp\": 0,\n      \"reset\": 0,\n      \"subId\": \"jw45dtw6rhvefikz\",\n      \"tgId\": \"\",\n      \"totalGB\": 0\n    }\n  ],\n  \"decryption\": \"none\",\n  \"fallbacks\": []\n}",
            "streamSettings": "{\n  \"network\": \"tcp\",\n  \"security\": \"reality\",\n  \"externalProxy\": [],\n  \"realitySettings\": {\n    \"show\": false,\n    \"xver\": 0,\n    \"dest\": \"yahoo.com:443\",\n    \"serverNames\": [\n      \"yahoo.com\",\n      \"www.yahoo.com\"\n    ],\n    \"privateKey\": \"QJS9AerMmDU-DrTe_SAL7vX6_2wg19OxCuthZLLs40g\",\n    \"minClient\": \"\",\n    \"maxClient\": \"\",\n    \"maxTimediff\": 0,\n    \"shortIds\": [\n      \"97de\",\n      \"5f7b4df7d0605151\",\n      \"cc1a7d15c439\",\n      \"f196851a\",\n      \"e291c2\",\n      \"b10c0deeceec08\",\n      \"19\",\n      \"7db6c63a5d\"\n    ],\n    \"settings\": {\n      \"publicKey\": \"UNXIILQ_LpbZdXGbhNCMele1gaPVIfCJ9N0AoLYdRUE\",\n      \"fingerprint\": \"random\",\n      \"serverName\": \"\",\n      \"spiderX\": \"/\"\n    }\n  },\n  \"tcpSettings\": {\n    \"acceptProxyProtocol\": false,\n    \"header\": {\n      \"type\": \"none\"\n    }\n  }\n}",
            "tag": "inbound-37155",
            "sniffing": "{\n  \"enabled\": false,\n  \"destOverride\": [\n    \"http\",\n    \"tls\",\n    \"quic\",\n    \"fakedns\"\n  ],\n  \"metadataOnly\": false,\n  \"routeOnly\": false\n}",
            "allocate": "{\n  \"strategy\": \"always\",\n  \"refresh\": 5,\n  \"concurrency\": 3\n}"
        },
        {
            "id": 4,
            "up": 0,
            "down": 0,
            "total": 0,
            "remark": "",
            "enable": true,
            "expiryTime": 0,
            "clientStats": [
                {
                    "id": 4,
                    "inboundId": 4,
                    "enable": true,
                    "email": "s729v2km",
                    "up": 0,
                    "down": 0,
                    "expiryTime": 0,
                    "total": 0,
                    "reset": 0
                }
            ],
            "listen": "",
            "port": 44360,
            "protocol": "vless",
            "settings": "{\n  \"clients\": [\n    {\n      \"id\": \"a39c9655-bcbb-43c4-9b3b-ebd8b7ae9e1e\",\n      \"flow\": \"\",\n      \"email\": \"s729v2km\",\n      \"limitIp\": 0,\n      \"totalGB\": 0,\n      \"expiryTime\": 0,\n      \"enable\": true,\n      \"tgId\": \"\",\n      \"subId\": \"n2b9ubaioe06cak8\",\n      \"reset\": 0\n    }\n  ],\n  \"decryption\": \"none\",\n  \"fallbacks\": []\n}",
            "streamSettings": "{\n  \"network\": \"tcp\",\n  \"security\": \"none\",\n  \"externalProxy\": [],\n  \"tcpSettings\": {\n    \"acceptProxyProtocol\": false,\n    \"header\": {\n      \"type\": \"none\"\n    }\n  }\n}",
            "tag": "inbound-44360",
            "sniffing": "{\n  \"enabled\": false,\n  \"destOverride\": [\n    \"http\",\n    \"tls\",\n    \"quic\",\n    \"fakedns\"\n  ],\n  \"metadataOnly\": false,\n  \"routeOnly\": false\n}",
            "allocate": "{\n  \"strategy\": \"always\",\n  \"refresh\": 5,\n  \"concurrency\": 3\n}"
        }
    ]
}
```

### Response: 200 (without data)

```json
{
    "success": true,
    "msg": "",
    "obj": []
}
```

## Inbound `settings` field decoded example

```json
{
   "clients":[
      {
         "email":"hyvcs325",
         "enable":true,
         "expiryTime":0,
         "flow":"",
         "id":"819920c0-22c8-4c83-8713-9c3da4980396",
         "limitIp":0,
         "reset":0,
         "subId":"jmrwimzhicxm7hrm",
         "tgId":"",
         "totalGB":0
      },
      {
         "email":"27225ost",
         "enable":true,
         "expiryTime":0,
         "flow":"",
         "id":"bf036995-a81d-41b3-8e06-8e233418c96a",
         "limitIp":0,
         "reset":0,
         "subId":"jw45dtw6rhvefikz",
         "tgId":"",
         "totalGB":0
      }
   ],
   "decryption":"none",
   "fallbacks":{
      
   }
}
```

## Inbound `streamSettings` field decoded example

```json
{
  "network": "tcp",
  "security": "reality",
  "externalProxy": [],
  "realitySettings": {
    "show": false,
    "xver": 0,
    "dest": "yahoo.com:443",
    "serverNames": [
      "yahoo.com",
      "www.yahoo.com"
    ],
    "privateKey": "QJS9AerMmDU-DrTe_SAL7vX6_2wg19OxCuthZLLs40g",
    "minClient": "",
    "maxClient": "",
    "maxTimediff": 0,
    "shortIds": [
      "97de",
      "5f7b4df7d0605151",
      "cc1a7d15c439",
      "f196851a",
      "e291c2",
      "b10c0deeceec08",
      "19",
      "7db6c63a5d"
    ],
    "settings": {
      "publicKey": "UNXIILQ_LpbZdXGbhNCMele1gaPVIfCJ9N0AoLYdRUE",
      "fingerprint": "random",
      "serverName": "",
      "spiderX": "/"
    }
  },
  "tcpSettings": {
    "acceptProxyProtocol": false,
    "header": {
      "type": "none"
    }
  }
}
```

## Inbound `sniffing` field decoded example

```json
{
  "enabled": false,
  "destOverride": [
    "http",
    "tls",
    "quic",
    "fakedns"
  ],
  "metadataOnly": false,
  "routeOnly": false
}
```

## Inbound `allocate` field decoded example

```json
{
  "strategy": "always",
  "refresh": 5,
  "concurrency": 3
}
```
