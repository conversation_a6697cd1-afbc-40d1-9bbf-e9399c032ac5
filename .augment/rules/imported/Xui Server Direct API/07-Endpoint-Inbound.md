---
type: "manual"
---

# Endpoint: Inbound

## Description

This route is used to retrieve statistics and details for a specific inbound connection identified by `{inboundId}`. This includes information about the inbound itself, its statistics, and the clients connected to it.

## **Path Parameter**

- `{inboundId}`: Identifier of the specific inbound for which information is requested.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Ensure that the provided `{inboundId}` corresponds to an existing inbound within the system.
- Handle any potential errors or failure messages returned in the response.

### Method: GET

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/api/inbounds/get/{inboundId}
> ```

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Response: 200 (if successful, inbound exists)

```json
{
    "success": true,
    "msg": "",
    "obj": {
        "id": 2,
        "up": 0,
        "down": 0,
        "total": 0,
        "remark": "",
        "enable": true,
        "expiryTime": 0,
        "clientStats": null,
        "listen": "",
        "port": 38476,
        "protocol": "vless",
        "settings": "{\n  \"clients\": [\n    {\n      \"id\": \"7da4dd82-66e6-4dfa-a66b-bf423f5407ea\",\n      \"flow\": \"\",\n      \"email\": \"t6l5ljc9\",\n      \"limitIp\": 0,\n      \"totalGB\": 0,\n      \"expiryTime\": 0,\n      \"enable\": true,\n      \"tgId\": \"\",\n      \"subId\": \"ile0ixxgdmjeuz5m\",\n      \"reset\": 0\n    }\n  ],\n  \"decryption\": \"none\",\n  \"fallbacks\": []\n}",
        "streamSettings": "{\n  \"network\": \"tcp\",\n  \"security\": \"reality\",\n  \"externalProxy\": [],\n  \"realitySettings\": {\n    \"show\": false,\n    \"xver\": 0,\n    \"dest\": \"yahoo.com:443\",\n    \"serverNames\": [\n      \"yahoo.com\",\n      \"www.yahoo.com\"\n    ],\n    \"privateKey\": \"yKUjT7SgfQH8fOTqsKLhwkOWiqRi5oC0Y4lFZXb0CTE\",\n    \"minClient\": \"\",\n    \"maxClient\": \"\",\n    \"maxTimediff\": 0,\n    \"shortIds\": [\n      \"8714e8f78bd9\",\n      \"8692\",\n      \"4e\",\n      \"9c46e1\",\n      \"52c0f48e\",\n      \"2d439ce7fd35bd\",\n      \"a64d2fc4a1\",\n      \"2520ce66461ba14d\"\n    ],\n    \"settings\": {\n      \"publicKey\": \"HBOoWQWTTFlN1CyPL-wzf-0S28Ae7D4E23f6GL9FaXw\",\n      \"fingerprint\": \"random\",\n      \"serverName\": \"\",\n      \"spiderX\": \"/\"\n    }\n  },\n  \"tcpSettings\": {\n    \"acceptProxyProtocol\": false,\n    \"header\": {\n      \"type\": \"none\"\n    }\n  }\n}",
        "tag": "inbound-38476",
        "sniffing": "{\n  \"enabled\": false,\n  \"destOverride\": [\n    \"http\",\n    \"tls\",\n    \"quic\",\n    \"fakedns\"\n  ],\n  \"metadataOnly\": false,\n  \"routeOnly\": false\n}",
        "allocate": "{\n  \"strategy\": \"always\",\n  \"refresh\": 5,\n  \"concurrency\": 3\n}"
    }
}
```

### Response: 200 (if inbound does not exist)

```json
{
    "success": false,
    "msg": "Obtain Failed: record not found",
    "obj": null
}
```
