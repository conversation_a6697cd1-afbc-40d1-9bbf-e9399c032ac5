---
type: "manual"
---

# Endpoint: Client Traffics by email

## Description

This route is used to retrieve information about a specific client based on their email. This endpoint provides details such as traffic statistics and other relevant information related to the client.

## **Path Parameter**

- `{email}`: Email address of the client for whom information is requested.

## **Note**

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Ensure that the provided `{email}` corresponds to a valid client in the system to retrieve accurate information.
- Handle any potential errors or failure messages returned in the response.

### Method: GET

> ```Text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/api/inbounds/getClientTraffics/{email}
> ```

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Response: 200 (if successful, client exists)

```json
{
    "success": true,
    "msg": "",
    "obj": {
        "id": 4,
        "inboundId": 4,
        "enable": true,
        "email": "s729v2km",
        "up": 0,
        "down": 0,
        "expiryTime": 0,
        "total": 0,
        "reset": 0
    }
}
```

### Response: 200 (if client does not exist or couldn't retrieve information)

```json
{
    "success": true,
    "msg": "",
    "obj": null
}
```
