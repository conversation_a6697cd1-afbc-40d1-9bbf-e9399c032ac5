---
type: "manual"
---

# Endpoint: Reset traffics of all clients in an inbound

## Description

This route is used to reset the traffic statistics for all clients associated with a specific inbound identified by its ID (`{inboundId}`).

## Path Parameter

- `{inboundId}` : Identifier of the specific inbound for which client traffics are being reset.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Resetting the client traffics through this endpoint affects all clients associated with the specified inbound.
- Verify that the provided `{inboundId}` corresponds to an existing inbound within the system.
- Handle any potential errors or failure messages returned in the response.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/api/inbounds/resetAllClientTraffics/{inboundId}
> ```

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Response: 200

```json
{
    "success": true,
    "msg": "All traffic from the client has been reset. Successfully",
    "obj": null
}
```
