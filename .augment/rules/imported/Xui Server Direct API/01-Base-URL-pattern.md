---
type: "manual"
---

# Основа URL-шаблон

```text
https://{xui_server_address}:{xui_server_port}/{xui_server_web_base_path}/{endpoint}
```

## HTTPS, SSL certificate

* На серверах используются самоподписанные сертификаты
* Все запросы должны быть отправлены через HTTPS
* Стоит игнорировать ошибки сертификата для доступа к API

## 🔸 Переменные в URL

| Переменная                 | Описание                                                                       |
| -------------------------- | ------------------------------------------------------------------------------ |
| `xui_server_address`       | Домен, IP адрес                                                                |
| `xui_server_port`          | Номер порта, если не используется порт по умолчанию (`57775` для HTTPS)        |
| `xui_server_web_base_path` | Путь к веб-интерфейсу сервера XUI, обычно рандомная строка `SkjSUk8ihfQwWoI`   |
| `endpoint`                 | Конечная точка API, например `/server/status`, `/panel/inbound/list`           |

## 📌 Пример полного URL

```text
https://************:57775/SkjSUk8ihfQwWoI/panel/inbound/list
```
