---
type: "manual"
---

# Endpoint: Delete Depleted Clients

## Description

This route is used to delete all depleted clients associated with a specific inbound identified by its ID (`{inboundId}`). If no `{inboundId}` is specified, depleted clients will be deleted from all inbounds. Depleted clients are those clients who have reached their traffic limit or expired and are no longer able to access the internet.

## Path Parameter

- `{inboundId}` : Identifier of the specific inbound from which the depleted clients will be deleted. If not specified, depleted clients will be deleted from all inbounds.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- If `{inboundId}` is provided, ensure it corresponds to an existing inbound within the system. If not provided, depleted clients will be deleted from all inbounds.
- Deleting depleted clients through this endpoint permanently removes all depleted clients from the specified inbound(s).
- Handle any potential errors or failure messages returned in the response.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/api/inbounds/delDepletedClients/{inboundId}
> ```

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Response: 200

```json
{
    "success": true,
    "msg": "All depleted clients are deleted Successfully",
    "obj": null
}
```
