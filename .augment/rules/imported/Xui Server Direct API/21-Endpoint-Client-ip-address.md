---
type: "manual"
---

# Endpoint: Client Ip address

## Description

This route is used to retrieve the IP records associated with a specific client identified by their email. Does IP limit work? How do I use it in tunneling?
Yes; from version 1.7.0+, you can use it by installing fail2ban in the x-ui menu and specifying the limit for each client. If a user connects with an unauthorized number of IPs, they will be blocked for the time you specified. From version 1.7.9+, you need to enable access log in xray settings.

## Path Parameter

- **`{email}`** : Email address of the client for whom IP records are requested.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Ensure that the provided `{email}` corresponds to a valid client in the system to retrieve accurate IP records.
- Handle any potential errors or failure messages returned in the response.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/api/inbounds/clientIps/{email}
> ```

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Response: 200

```json
{
    "success": true,
    "msg": "",
    "obj": "No IP Record"
}
```
