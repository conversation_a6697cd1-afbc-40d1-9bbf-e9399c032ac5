---
type: "manual"
---

# Endpoint: Get All Server Settings

## Description

This route is used to retrieve all server settings. This includes settings that are directly related to the panel's operation and are essential for the server's functionality.

> `sessionMaxAge` is the Session Duration. The duration for which you can stay logged in. (unit: minute). Can be 0 (zero) but not recommended (because does not work properly).
> Checking the `sessionMaxAge` value can help prevent session expiration and 401 errors (Unauthorized).
> To avoid unexpected logouts or 401 errors, consider checking `sessionMaxAge` before it expires.
> Check `sessionMaxAge` together with the last login time (`/login`) to proactively re-authenticate and refresh the token (session cookie) before the session expires. This helps prevent 401 errors.
> `sessionMaxAge` can be 0 (zero) or a positive integer.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Handle any potential errors or failure messages returned in the response.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/setting/all
> ```

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Response: 200

```json
{
    "success": true,
    "msg": "",
    "obj": {
        "webListen": "",
        "webDomain": "",
        "webPort": 57775,
        "webCertFile": "/root/certs/certificate.crt",
        "webKeyFile": "/root/certs/private.key",
        "webBasePath": "/SsBPspeDarVpK9y/",
        "sessionMaxAge": 99999,
        "pageSize": 60,
        "expireDiff": 0,
        "trafficDiff": 0,
        "remarkModel": "-ieo",
        "tgBotEnable": false,
        "tgBotToken": "",
        "tgBotProxy": "",
        "tgBotAPIServer": "",
        "tgBotChatId": "",
        "tgRunTime": "@daily",
        "tgBotBackup": false,
        "tgBotLoginNotify": true,
        "tgCpu": 80,
        "tgLang": "en-US",
        "timeLocation": "Local",
        "twoFactorEnable": false,
        "twoFactorToken": "",
        "subEnable": false,
        "subTitle": "",
        "subListen": "",
        "subPort": 2096,
        "subPath": "/sub/",
        "subDomain": "",
        "subCertFile": "",
        "subKeyFile": "",
        "subUpdates": 12,
        "externalTrafficInformEnable": false,
        "externalTrafficInformURI": "",
        "subEncrypt": true,
        "subShowInfo": true,
        "subURI": "",
        "subJsonPath": "/json/",
        "subJsonURI": "",
        "subJsonFragment": "",
        "subJsonNoises": "",
        "subJsonMux": "",
        "subJsonRules": "",
        "datepicker": "gregorian"
    }
}
```
