---
type: "manual"
---

# Endpoint: Online Clients

## Description

This route is used to retrieve a list of online clients from all inbounds.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Handle any potential errors or failure messages returned in the response.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/api/inbounds/onlines
> ```

### Body formdata

|Param|value|Type|
|---|---|---|

### Response: 200

```json
{
    "success": true,
    "msg": "",
    "obj": [
        "88vzckui"
    ]
}
```

Returns an array of emails of online clients.
