---
type: "manual"
---

# Responses

## 📤 Ответы API

Все ответы поступают в виде JSON-объекта с полями `success`, `msg` и `obj`.

### 📤 Пример ответа

В json ответе:

> `success` - boolean, успешно - true, неуспешно - false
> `msg` - string, сообщение об ошибке или успехе
> `obj` - object, данные ответа, может быть null

#### Ошибка или проблема

```json
{
  "success": false,
  "msg": "Username is required",
  "obj": null
}
```

#### Успех

```json
{
    "success": true,
    "msg":" You have successfully logged into your account.",
    "obj":null
}
```

## Если слетает авторизация

### 📤 `401` Unauthorized

Сессия истекла

```json
{
    "success":false,
    "msg":"Your session has expired, please log in again",
    "obj":null
}
```
