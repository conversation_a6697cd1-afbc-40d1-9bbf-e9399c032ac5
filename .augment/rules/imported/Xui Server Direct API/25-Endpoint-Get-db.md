---
type: "manual"
---

# Endpoint: Get Db

## Description

This route is used to export the database of the server. The database contains all the configuration and statistics of the server.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Handle any potential errors or failure messages returned in the response.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/server/getDb
> ```

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Response: 200

The response will be a binary file containing the database. The file name will be `x-ui.db`. The file format will be `sqlite`.

### Response headers

```http
Content-disposition: attachment; filename=x-ui.db
Content-encoding: gzip
Content-type: application/octet-stream
```
