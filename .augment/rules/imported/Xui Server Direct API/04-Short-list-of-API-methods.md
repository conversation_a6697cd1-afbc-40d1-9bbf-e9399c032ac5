---
type: "manual"
---

# 📌 Краткий список API-методов

* [Документация API (Postman)](https://www.postman.com/hsanaei/3x-ui/collection/q1l5l0u/3x-ui)
* `/login` с методом `POST` — передаются данные пользователя: `{username: '', password: ''}` для входа.
* Базовый путь (path): `/panel/api/inbounds` используется для следующих действий:

| Метод  | Путь                               | Описание действия                                                            |
| ------ | ---------------------------------- | ---------------------------------------------------------------------------- |
| `GET`  | `"/list"`                          | Получить все соединения (inbounds)                                           |
| `GET`  | `"/get/:id"`                       | Получить соединение (inbound) по `inbound.id`                                |
| `GET`  | `"/getClientTraffics/:email"`      | Получить трафик клиента по email                                             |
| `GET`  | `"/getClientTrafficsById/:id"`     | Получить трафик клиента по ID                                                |
| `GET`  | `"/createbackup"`                  | Телеграм-бот отправляет резервную копию администраторам                      |
| `POST` | `"/add"`                           | Добавить соединение (inbound)                                                |
| `POST` | `"/del/:id"`                       | Удалить соединение (inbound) по `id`                                         |
| `POST` | `"/update/:id"`                    | Обновить соединение (inbound) по `id`                                        |
| `POST` | `"/clientIps/:email"`              | Получить IP-адреса клиента по email                                          |
| `POST` | `"/clearClientIps/:email"`         | Очистить IP-адреса клиента по email                                          |
| `POST` | `"/addClient"`                     | Добавить клиента в соединение (inbound)                                      |
| `POST` | `"/:id/delClient/:clientId"`       | Удалить клиента по `clientId`                                                |
| `POST` | `"/updateClient/:clientId"`        | Обновить данные клиента по `clientId`                                        |
| `POST` | `"/:id/resetClientTraffic/:email"` | Сбросить трафик клиента по email                                             |
| `POST` | `"/resetAllTraffics"`              | Сбросить трафик у всех соединений (inbounds)                                 |
| `POST` | `"/resetAllClientTraffics/:id"`    | Сбросить трафик всех клиентов в соединении (inbound)                         |
| `POST` | `"/delDepletedClients/:id"`        | Удалить клиентов с исчерпанным трафиком из соединения (inbound) (`-1` — все) |
| `POST` | `"/onlines"`                       | Получить список онлайн-пользователей (email'ы)                               |

* Базовый путь (path): совсем не используется для следующих действий:

| Метод   | Путь                                  | Описание действия                                                            |
| ------- | ------------------------------------- | ---------------------------------------------------------------------------- |
| `POST`  | `"/login"`                            | Аутентификация пользователя для получения cookie `3x-ui`.                    |
| `POST`  | `"/server/status"`                    | Получить статус сервера                                                      |
| `POST`  | `"/server/getDb"`                     | Экспорт базы данных. Формат файла: sqlite. Имя файла: x-ui.db                |
| `POST`  | `"/server/logs/:rowsCount"`           | Получить последние `rowsCount` записей из лог-файла сервера                  |
| `POST`  | `"/server/updateGeofile/{:filename}"` | Обновить Geofile файл `filename`. Например `geosite.dat`, `geoip_RU.dat` и другие |
| `POST`  | `"/server/restartXrayService"`        | Перезапустить сервис Xray                                                    |
| `POST`  | `"/panel/setting/restartPanel"`       | Перезапуск самой панели 3X-UI                                                |
| `POST`  | `"/panel/setting/defaultSettings"`    | Получить минимальный набор настроек панели 3X-UI                             |
| `POST`  | `"/panel/setting/all"`                | Получить все настройки панели 3X-UI                                          |

---

🔸 **Примечание по `clientId`:**

Поле `clientId` должно заполняться следующим образом в зависимости от типа клиента:

* `client.id` — для **VMESS** и **VLESS**
* `client.password` — для **TROJAN**
* `client.email` — для **Shadowsocks**

---

🔸 **Примечание по `email`:**

Поле email должно быть уникальным для каждого сервера.
Даже если на сервере несколько соединений (inbounds), email должен быть уникальным для каждого клиента.
В отличии от `clientId`, который может быть неуникальным для каждого соединения (inbound).
