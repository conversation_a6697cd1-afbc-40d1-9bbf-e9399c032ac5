---
type: "manual"
---

# Endpoint: Server Update Geofile

## Description

The route is used to update the geofile of the server. The geofile contains information about the domains and their associated tags. The geofile is used to define rules for routing network traffic based on domain names.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.

> geosite.dat is a file used in network routing, particularly within tools like V2Ray and its variants. It contains a database of domain names and their associated tags, enabling users to define rules for routing network traffic based on domain names. These tags can be used to categorize domains (e.g., "category-ads-all" for advertising domains) or to specify which domains should be routed through a proxy or directly, depending on the user's needs.
> The geosite.dat file is an essential component for implementing domain-based routing policies in network configurations. Files with names like `geosite.dat`, `geoip.dat`, `geosite_IR.dat`, etc. are **geo- and network rules databases** used in proxying and traffic routing systems such as **Xray**, **V2Ray**, **3x-ui** and others.

### 📁 `geosite.dat`

- **Что это**: База данных доменных групп (по странам, категориям, службам).
- **Используется для**: Маршрутизации трафика по доменам. Например, можно настроить, чтобы трафик к `google.com` или `*.youtube.com` шёл через определённый выход (outbound).
- **Источник**: Создаётся и обновляется проектом [Loyalsoldier/v2ray-rules-dat](https://github.com/Loyalsoldier/v2ray-rules-dat) или напрямую из [v2fly/domain-list-community](https://github.com/v2fly/domain-list-community).

---

### 📁 `geoip.dat`

- **Что это**: База данных IP-сетей по странам.
- **Используется для**: Определения страны назначения по IP. Например, чтобы направить весь трафик в Россию через один прокси, а остальные — через другой.
- **Источник**: Обычно основан на данных MaxMind или аналогичных проектов.

---

### 📁 `geosite_IR.dat`

- **Что это**: Та же структура, что и `geosite.dat`, но содержит только домены, относящиеся к **Ирану (IR)**.
- **Применение**: Используется для настройки маршрутизации или блокировки иранского контента. Можно, например, указать, чтобы все сайты из этой группы шли напрямую или через определённый туннель.

---

### 📁 `geoip_IR.dat`

- **Что это**: Подмножество `geoip.dat`, содержащее только **иранские IP-сети**.
- **Применение**: Аналогично — применяется для фильтрации или маршрутизации трафика, связанного с Ираном.

---

### 📁 `geosite_RU.dat`

- **Что это**: Домены, связанные с **Россией (RU)**.
- **Применение**: Например, можно задать правило, чтобы весь трафик к российским доменам (`.ru`, `yandex.ru`, `vk.com` и др.) шёл напрямую или через определённый выход.

---

### 📁 `geoip_RU.dat`

- **Что это**: IP-сети, зарегистрированные в **России**.
- **Применение**: Полезно, когда нужно определить, что IP-пакет адресован российскому серверу, и обработать его соответствующим образом (например, не маршрутизировать через прокси).

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/server/updateGeofile/{filename}
> ```

`filename` is the name of the geofile to update. It can be one of the following values:

- `geosite.dat`
- `geoip.dat`
- `geosite_IR.dat`
- `geoip_IR.dat`
- `geosite_RU.dat`
- `geoip_RU.dat`

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Response: 200

```json
{
    "success": true,
    "msg": "Geofile updated successfully",
    "obj": null
}
```
