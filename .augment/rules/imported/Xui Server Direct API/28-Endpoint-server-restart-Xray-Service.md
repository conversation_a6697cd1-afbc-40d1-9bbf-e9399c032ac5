---
type: "manual"
---

# Endpoint: Server Restart Xray Service

## Description

This route is used to restart the Xray service.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Restarting the Xray service through this endpoint affects the operation of the Xray service.
- Handle any potential errors or failure messages returned in the response.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/server/restartXrayService
> ```

### Response: 200

```json
{
    "success": true,
    "msg": "Xray has been successfully relaunched.",
    "obj": null
}
```
