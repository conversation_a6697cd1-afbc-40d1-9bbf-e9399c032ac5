---
type: "manual"
---

# Project: 3X-UI

The **Postman collection** for the MHSanaei/3x-ui offers a comprehensive range of API endpoints, allowing users to manage various operations efficiently. These include authentication, inbound management (listing, retrieving details, updating, and deleting), and client operations (retrieving, updating, deleting, and resetting traffic). Additionally, the collection includes specialized tasks such as resetting traffic statistics, removing depleted clients, exporting the database, and generating backups. This collection is designed to simplify interaction with the MHSanaei/3x-ui API , making it easier to manage inbounds, clients, and other key functionalities.
