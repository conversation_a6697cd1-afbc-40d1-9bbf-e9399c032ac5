---
type: "manual"
---

# Endpoint: Update Client

## Description

This route is used to update an existing client identified by its UUID (`{uuid}`) within a specific inbound.

## **Path Parameter**

- `{uuid}` : Unique identifier (UUID) of the specific client for whom information is being updated.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Verify that the provided `{uuid}` corresponds to an existing client within the system associated with the specified inbound.
- Format the client information in the `settings` parameter as a stringified JSON format within the request body.
- Handle any potential errors or failure messages returned in the response.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/api/inbounds/updateClient/{uuid}
> ```

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Body (**raw**)

```json
{
    "id": 20,
    "settings": "{\"clients\": [{\"id\": \"6046007d-f4e5-4384-a545-2848165001da\",\"flow\": \"\",\"email\": \"sbhmrsmz\",\"limitIp\": 0,\"totalGB\": 10737418240,\"expiryTime\": 1729073736270,\"enable\": true,\"tgId\": \"\",\"subId\": \"z70791vpexfxw57h\",\"reset\": 0}]}"
}
```

### Response: 200 (If successful)

```json
{
    "success": true,
    "msg": "Client updated Successfully",
    "obj": null
}
```

### Response: 200 (If failed)

```json
{
    "success": false,
    "msg": "Something went wrong! Failed: empty client ID\n",
    "obj": null
}
```
