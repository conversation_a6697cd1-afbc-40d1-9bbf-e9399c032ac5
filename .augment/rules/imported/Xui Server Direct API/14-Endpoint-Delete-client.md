---
type: "manual"
---

# Endpoint: Delete Client

## Description

This route is used to delete a client identified by its UUID (`{uuid}`) within a specific inbound identified by its ID (`{inboundId}`).

## Path Parameters

- `{inboundId}` : Identifier of the specific inbound from which the client will be deleted.
- `{uuid}` : Unique identifier (UUID) of the specific client to be deleted.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Ensure that the provided `{inboundId}` corresponds to an existing inbound and `{uuid}` corresponds to an existing client within the system.
- Deleting the client through this endpoint permanently removes the specified client from the specified inbound.
- Handle any potential errors or failure messages returned in the response.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/api/inbounds/{inboundId}/delClient/{uuid}
> ```

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Response: 200 (If successful)

```json
{
    "success": true,
    "msg": "Client deleted Successfully",
    "obj": null
}
```

### Response: 200 (If failed)

```json
{
    "success": false,
    "msg": "Something went wrong! Failed: record not found",
    "obj": null
}
```
