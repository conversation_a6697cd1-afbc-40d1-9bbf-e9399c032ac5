---
type: "manual"
---

# Endpoint: Delete Inbound

## Description

This route is used to delete an inbound identified by its ID (`{inboundId}`).

## Path Parameter

- `{inboundId}` : Identifier of the specific inbound to be deleted.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Ensure that the provided `{inboundId}` corresponds to an existing inbound within the system.
- Deleting the inbound through this endpoint permanently removes the specified inbound.
- Handle any potential errors or failure messages returned in the response.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/api/inbounds/del/{inboundId}
> ```

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Response: 200 (If successful)

```json
{
    "success": true,
    "msg": "Delete Successfully",
    "obj": 3
}
```

### Response: 200 (If failed)

```json
{
    "success": false,
    "msg": "Delete Failed: record not found",
    "obj": 3
}
```
