---
type: "manual"
---

# Endpoint: Clear Client Ip address

## Description

This route is used to reset or clear the IP records associated with a specific client identified by their email address (`{email}`). This is useful when you want to remove any IP restrictions or reset the IP usage for a particular client.

## Path Parameter

- `{email}` : Email address of the client for whom IP records need to be reset.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Verify that the provided `{email}` corresponds to an existing client within the system for whom IP records need to be cleared.
- Handle any potential errors or failure messages returned in the response.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/api/inbounds/clearClientIps/{email}
> ```

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Response: 200

```json
{
    "success": true,
    "msg": "Log Cleared Successfully",
    "obj": null
}
```
