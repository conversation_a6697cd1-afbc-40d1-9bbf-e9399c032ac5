---
type: "manual"
---

# Endpoint: tgbot - sends backup to admins

## Description

This endpoint triggers the creation of a system backup and initiates the delivery of the backup file to designated administrators via a configured Telegram bot. The server verifies the Telegram bot's activation status within the system settings and checks for the presence of admin IDs specified in the settings before sending the backup.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Upon implementation, the backup file might be sent through the Telegram bot registered in the panel settings.
- Handle any potential errors or failure messages returned in the response.

### Method: GET

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/api/inbounds/createbackup
> ```

### Headers

|Content-Type|Value|
|---|---|
|||
