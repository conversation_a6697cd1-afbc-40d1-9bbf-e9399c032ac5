---
type: "manual"
---

# Endpoint: Reset traffics of all inbounds

## Description

This route is used to reset the traffic statistics for all inbounds within the system.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Resetting the traffics through this endpoint affects the statistics for all inbounds within the system.
- Handle any potential errors or failure messages returned in the response.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/api/inbounds/resetAllTraffics
> ```

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Response: 200

```json
{
    "success": true,
    "msg": "all traffic has been reset Successfully",
    "obj": null
}
```
