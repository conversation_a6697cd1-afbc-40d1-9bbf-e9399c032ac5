---
type: "manual"
---

# Аутентификация, авторизация

## 🔹 POST `/{xui_server_web_base_path}/login`

Авторизация по логину и паролю для дальнейшего взаимодействия с API.
При успешной авторизации сервер вернет cookie под названием `3x-ui`.
Этот `3x-ui` будет использоваться для авторизации в дальнейшем.
Пример: `3x-ui=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXCJ9.eyJ1c2VybmFtZSI6ImpvaG5zbWl0aCIsInVzZXJfaWQiOjEsImV4cCI6MTY5NjUxNjIyNn12s`
Далее во всех запросах нужно отправлять этот `3x-ui` в заголовке `Cookie`.
Пример: `Cookie: 3x-ui=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXCJ9.eyJ1c2VybmFtZSI6ImpvaG5zbWl0aCIsInVzZXJfaWQiOjEsImV4cCI6MTY5NjUxNjIyNn12s`

### 🛡️ Заголовки

| Заголовок                                                          | Обязательный  | Описание                              |
| ------------------------------------------------------------------ | ------------- | ------------------------------------- |
| `Content-type: application/x-www-form-urlencoded; charset=UTF-8`   | Да            | Отправка данных как форма `asForm`    |
| `Cookie: lang=en-US`                                               | Нет           | Язык интерфейса (Желательно передать) |

> ⚠️ Важно!
> После авторизации сервер вернет переменную cookie `3x-ui`, эта cookie использовать для дальнейших запросов
> Самое важное: **НЕ ПЕРЕДАВАТЬ** переменную cookie `3x-ui` при авторизации, даже пустым, т.к. сервер будет пытаться использовать эту переменную и будет выдана ошибка 404

#### 📦 Тело запроса (форма)

Передаётся в теле HTTP-запроса, закодировано в форме:

```text
username=JohnSmith&password=Secret123&twoFactorCode=
```

| Поле            | Обязательный | Тип    | Описание                        |
| --------------- | ------------ | ------ | ------------------------------- |
| `username`      | Да           | string | Имя пользователя                |
| `password`      | Да           | string | Email-адрес                     |
| `twoFactorCode` | Нет          | string |                                 |

### 📤 Пример запроса

```http
POST /SkjSUk8ihfQwWoI/login HTTP/1.1
Content-Type: application/x-www-form-urlencoded; charset=UTF-8
Cookie: lang=en-US
Host: ***********:57775

username=JohnSmith&password=Secret123
```

### ✅ Ответ 200 (OK)

```json
{
    "success": true,
    "msg":" You have successfully logged into your account.",
    "obj":null
}
```

### ⚠️ Возможные ошибки

> Ошибка - если `success: false`

| Код | Сообщение `msg`                                    | Описание                                                         |
| --- | -------------------------------------------------- | ---------------------------------------------------------------- |
| 200 | `Invalid username or password or two-factor code.` | Неверный логин или пароль или код двухфакторной аутентификации   |
| 200 | `Username is required`                             | Не передан логин и пароль или только логин                       |
| 200 | `Password is required`                             | Не передан пароль                                                |
| 200 | `The Input data format is invalid.`                | Неверный формат данных. Возможно не переданы требуемые заголовки |
