---
type: "manual"
---

# Endpoint: Add Client to inbound

## Description

This route is used to add a new client to a specific inbound identified by its ID.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Verify that the provided inbound ID (`id`) corresponds to an existing inbound within the system.
- Format the client information in the `settings` parameter as a stringified JSON format within the request body.
- Handle any potential errors or failure messages returned in the response.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/api/inbounds/addClient
> ```

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Body (**raw**)

```json
{
    "id": 5,
    "settings": "{\"clients\": [{\"id\": \"bbfad557-28f2-47e5-9f3d-e3c7f532fbda\",\"flow\": \"\",\"email\": \"dp1plmlt8\",\"limitIp\": 0,\"totalGB\": 0,\"expiryTime\": 0,\"enable\": true,\"tgId\": \"\",\"subId\": \"2rv0gb458kbfl532\",\"reset\": 0}]}"
}
```

### Response: 200 (If successful)

```json
{
    "success": true,
    "msg": "Client(s) added Successfully",
    "obj": null
}
```

### Response: 200 (If failed)

```json
{
    "success": false,
    "msg": "Something went wrong! Failed: Duplicate email: dp1plmlt8\n",
    "obj": null
}
```
