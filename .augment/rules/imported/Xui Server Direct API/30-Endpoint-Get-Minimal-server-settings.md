---
type: "manual"
---

# Endpoint: Get Minimal Server Settings

## Description

This route is used to retrieve a minimal set of server settings. This includes settings that are not directly related to the panel's operation but are essential for the server's functionality.

- Can be efficient to fast check IP limit functionality on server, by checking `ipLimitEnable` value.
- Can be efficient to fast check Telegram bot functionality on server, by checking `tgBotEnable` value.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Handle any potential errors or failure messages returned in the response.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/setting/defaultSettings
> ```

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Response: 200

```json
{
    "success": true,
    "msg": "",
    "obj": {
        "datepicker": "gregorian",
        "defaultCert": "/root/certs/certificate.crt",
        "defaultKey": "/root/certs/private.key",
        "expireDiff": 0,
        "ipLimitEnable": false,
        "pageSize": 60,
        "remarkModel": "-ieo",
        "subEnable": false,
        "subJsonURI": "",
        "subTitle": "",
        "subURI": "",
        "tgBotEnable": false,
        "trafficDiff": 0
    }
}
```
