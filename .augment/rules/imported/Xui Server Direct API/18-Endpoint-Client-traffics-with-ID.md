---
type: "manual"
---

# Endpoint: Client Traffics by ID

## Description

This route is used to retrieve information about a specific client based on their clientId. This endpoint provides details such as traffic statistics and other relevant information related to the client.

## **Path Parameter**

- `{uuid}`: This is the clientId, most often represented as a unique identifier (UUID) of the specific client for which information is being requested.

## **Note**

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Ensure that the provided `{uuid}` (UUID) corresponds to a valid client (clientId) in the system to retrieve accurate information.
- Handle any potential errors or failure messages returned in the response.

### Method: GET

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/api/inbounds/getClientTrafficsById/{uuid}
> ```

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Response: 200 (successful and client exists in single inbound)

```json
{
    "success": true,
    "msg": "",
    "obj": [
        {
            "id": 4,
            "inboundId": 4,
            "enable": true,
            "email": "s729v2km",
            "up": 0,
            "down": 0,
            "expiryTime": 0,
            "total": 0,
            "reset": 0
        }
    ]
}
```

`obj` is an array of clients

### Response: 200 (successful and client with same `{clientId}` exists in multiple inbounds)

```json
{
    "success": true,
    "msg": "",
    "obj": [
        {
            "id": 4,
            "inboundId": 4,
            "enable": true,
            "email": "dfhk33hkh_2",
            "up": 0,
            "down": 0,
            "expiryTime": 107374182400,
            "total": 0,
            "reset": 0
        },
        {
            "id": 8,
            "inboundId": 2,
            "enable": true,
            "email": "dfhk33hkh_8",
            "up": 0,
            "down": 0,
            "expiryTime": 0,
            "total": 0,
            "reset": 0
        }
    ]
}
```

`obj` is an array of clients

### Response: 200 (if client does not exist or not found)

```json
{
    "success": true,
    "msg": "",
    "obj": []
}
```

`obj` is an empty array
