---
type: "manual"
---

# Endpoint: Server Logs

## Description

This route is used to retrieve the logs of the server. The logs contain information about the server's activity, including errors, warnings, and other relevant information. The logs are useful for debugging and monitoring the server's activity. The logs are also useful for troubleshooting issues with the server.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Handle any potential errors or failure messages returned in the response.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/server/logs/{rowsCount}
> ```

### Body formdata

|Param|value|Type|
|---|---|---|
|level|debug|enum string|
|syslog|true|boolean|

`level` is the log level. It can be one of the following values:

- `debug`
- `info`
- `notice`
- `warning`
- `err`

`syslog` is a boolean value that indicates whether to include syslog logs in the response. If set to `true`, the response will include both x-ui logs and syslog logs. If set to `false`, the response will only include x-ui logs.

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Response: 200 (level: debug, syslog: false)

```json
{
    "success": true,
    "msg": "",
    "obj": [
        "2025/07/11 10:56:56 INFO - Remove Inbound User tg100014@leadteh_2 due to expiration or traffic limit",
        "2025/07/11 10:56:56 INFO - Remove Inbound User tg100013@leadteh_2 due to expiration or traffic limit",
        "2025/07/11 10:56:56 INFO - Remove Inbound User tg100012@leadteh_2 due to expiration or traffic limit",
        "2025/07/11 10:56:56 INFO - Remove Inbound User tg100011@leadteh_2 due to expiration or traffic limit",
        "2025/07/11 10:56:12 INFO - Remove Inbound User tg100014@leadteh_2 due to expiration or traffic limit",
        "2025/07/11 10:56:12 INFO - Remove Inbound User tg100013@leadteh_2 due to expiration or traffic limit",
        "2025/07/11 10:56:12 INFO - Remove Inbound User tg100012@leadteh_2 due to expiration or traffic limit",
        "2025/07/11 10:56:12 INFO - Remove Inbound User tg100011@leadteh_2 due to expiration or traffic limit",
        "2025/07/11 10:48:48 DEBUG - XRAY: REALITY remoteAddr: *************:13098\tlen(postHandshakeRecord): 43",
        "2025/07/11 10:48:48 DEBUG - XRAY: REALITY remoteAddr: *************:13098\tlen(postHandshakeRecord): 287",
        "2025/07/11 10:48:48 DEBUG - XRAY: REALITY remoteAddr: *************:13098\tlen(postHandshakeRecord): 271"
    ]
}
```

### Response: 200 (level: notice, syslog: true)

```json
{
    "success": true,
    "msg": "",
    "obj": [
        "Jul 10 20:07:14 xui-sbp-01 /usr/local/x-ui/x-ui[1482]: WARNING - wrong username: \"admin\", password: \"pass111\", IP: \"*************\"",
        "Jul 10 20:16:08 xui-sbp-01 /usr/local/x-ui/x-ui[1482]: WARNING - wrong username: \"admin\", password: \"pass112\", IP: \"*************\"",
        "Jul 10 20:16:14 xui-sbp-01 /usr/local/x-ui/x-ui[1482]: WARNING - wrong username: \"admin\", password: \"pass123\", IP: \"*************\"",
        "Jul 10 20:16:20 xui-sbp-01 /usr/local/x-ui/x-ui[1482]: WARNING - wrong username: \"admin\", password: \"pass222\", IP: \"*************\"",
        "Jul 10 20:30:13 xui-sbp-01 /usr/local/x-ui/x-ui[1482]: WARNING - wrong username: \"asdfasdf\", password: \"adsfasdf\", IP: \"**************\"",
        "Jul 10 20:30:47 xui-sbp-01 /usr/local/x-ui/x-ui[1482]: WARNING - wrong username: \"admin\", password: \"23eddf\", IP: \"***************\"",
        "Jul 10 21:04:06 xui-sbp-01 /usr/local/x-ui/x-ui[1482]: WARNING - wrong username: \"admin\", password: \"admin\", IP: \"*************\"",
        "Jul 10 21:11:45 xui-sbp-01 /usr/local/x-ui/x-ui[1482]: WARNING - wrong username: \"dsfsdf\", password: \"sdf\", IP: \"***************\"",
        "Jul 10 22:27:04 xui-sbp-01 /usr/local/x-ui/x-ui[1482]: WARNING - Something went wrong Failed:  Duplicate email: tg100013@leadteh_2",
        "Jul 10 22:28:21 xui-sbp-01 /usr/local/x-ui/x-ui[1482]: WARNING - Something went wrong Failed:  Duplicate email: tg100013@leadteh_2",
        ""
    ]
}
```
