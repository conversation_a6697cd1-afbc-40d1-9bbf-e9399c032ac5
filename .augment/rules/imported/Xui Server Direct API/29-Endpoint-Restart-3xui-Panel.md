---
type: "manual"
---

# Endpoint: Restart 3X-UI Panel

## Description

This route is used to restart the 3X-UI panel itself.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Restarting the 3X-UI panel through this endpoint affects the operation of the panel.
- Handle any potential errors or failure messages returned in the response.
- It is recommended to introduce a short delay before sending subsequent API requests to avoid potential connection issues.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/setting/restartPanel
> ```

### Response: 200

```json
{
  "success": true,
  "msg": "The panel was successfully restarted.",
  "obj": null
}
```
