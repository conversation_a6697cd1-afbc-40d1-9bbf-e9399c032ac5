---
type: "manual"
---

# Endpoint: Update Inbound

## Description

This route is used to update an existing inbound identified by its ID (`{inboundId}`).

## **Path Parameter**

- `{inboundId}`: Identifier of the specific inbound to be updated.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Verify that the provided `{inboundId}` corresponds to an existing inbound within the system.
- Ensure that sub-arrays or objects within the JSON body are stringified in JSON format for correct parsing by the panel.
- Handle any potential errors or failure messages returned in the response.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/api/inbounds/update/{inboundId}
> ```

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Body (**raw**)

```json
{
  "up": 0,
  "down": 0,
  "total": 0,
  "remark": "",
  "enable": true,
  "expiryTime": 0,
  "listen": "",
  "port": 44360,
  "protocol": "vless",
  "settings": "{\n  \"clients\": [\n    {\n      \"id\": \"a39c9655-bcbb-43c4-9b3b-ebd8b7ae9e1e\",\n      \"flow\": \"\",\n      \"email\": \"s729v2km\",\n      \"limitIp\": 0,\n      \"totalGB\": 0,\n      \"expiryTime\": 0,\n      \"enable\": true,\n      \"tgId\": \"\",\n      \"subId\": \"n2b9ubaioe06cak8\",\n      \"reset\": 0\n    }\n  ],\n  \"decryption\": \"none\",\n  \"fallbacks\": []\n}",
  "streamSettings": "{\n  \"network\": \"tcp\",\n  \"security\": \"none\",\n  \"externalProxy\": [],\n  \"tcpSettings\": {\n    \"acceptProxyProtocol\": false,\n    \"header\": {\n      \"type\": \"none\"\n    }\n  }\n}",
  "sniffing": "{\n  \"enabled\": false,\n  \"destOverride\": [\n    \"http\",\n    \"tls\",\n    \"quic\",\n    \"fakedns\"\n  ],\n  \"metadataOnly\": false,\n  \"routeOnly\": false\n}",
  "allocate": "{\n  \"strategy\": \"always\",\n  \"refresh\": 5,\n  \"concurrency\": 3\n}"
}
```

### Response: 200 (If successful)

```json
{
  "success": true,
  "msg": "Update Successfully",
  "obj": {
    "id": 4,
    "up": 0,
    "down": 0,
    "total": 0,
    "remark": "",
    "enable": true,
    "expiryTime": 0,
    "clientStats": null,
    "listen": "",
    "port": 44360,
    "protocol": "vless",
    "settings": "{\n  \"clients\": [\n    {\n      \"id\": \"a39c9655-bcbb-43c4-9b3b-ebd8b7ae9e1e\",\n      \"flow\": \"\",\n      \"email\": \"s729v2km\",\n      \"limitIp\": 0,\n      \"totalGB\": 0,\n      \"expiryTime\": 0,\n      \"enable\": true,\n      \"tgId\": \"\",\n      \"subId\": \"n2b9ubaioe06cak8\",\n      \"reset\": 0\n    }\n  ],\n  \"decryption\": \"none\",\n  \"fallbacks\": []\n}",
    "streamSettings": "{\n  \"network\": \"tcp\",\n  \"security\": \"none\",\n  \"externalProxy\": [],\n  \"tcpSettings\": {\n    \"acceptProxyProtocol\": false,\n    \"header\": {\n      \"type\": \"none\"\n    }\n  }\n}",
    "tag": "",
    "sniffing": "{\n  \"enabled\": false,\n  \"destOverride\": [\n    \"http\",\n    \"tls\",\n    \"quic\",\n    \"fakedns\"\n  ],\n  \"metadataOnly\": false,\n  \"routeOnly\": false\n}",
    "allocate": "{\n  \"strategy\": \"always\",\n  \"refresh\": 5,\n  \"concurrency\": 3\n}"
  }
}
```

### Response: 200 (If failed)

```json
{
  "success": false,
  "msg": "Update Failed: record not found",
  "obj": {
    "id": 6,
    "up": 0,
    "down": 0,
    "total": 0,
    "remark": "",
    "enable": true,
    "expiryTime": 0,
    "clientStats": null,
    "listen": "",
    "port": 44322,
    "protocol": "vless",
    "settings": "{\n  \"clients\": [\n    {\n      \"id\": \"a39c9655-bcbb-43c4-9b3b-ebd8b7ae9e1e\",\n      \"flow\": \"\",\n      \"email\": \"s729v2km\",\n      \"limitIp\": 0,\n      \"totalGB\": 0,\n      \"expiryTime\": 0,\n      \"enable\": true,\n      \"tgId\": \"\",\n      \"subId\": \"n2b9ubaioe06cak8\",\n      \"reset\": 0\n    }\n  ],\n  \"decryption\": \"none\",\n  \"fallbacks\": []\n}",
    "streamSettings": "{\n  \"network\": \"tcp\",\n  \"security\": \"none\",\n  \"externalProxy\": [],\n  \"tcpSettings\": {\n    \"acceptProxyProtocol\": false,\n    \"header\": {\n      \"type\": \"none\"\n    }\n  }\n}",
    "tag": "",
    "sniffing": "{\n  \"enabled\": false,\n  \"destOverride\": [\n    \"http\",\n    \"tls\",\n    \"quic\",\n    \"fakedns\"\n  ],\n  \"metadataOnly\": false,\n  \"routeOnly\": false\n}",
    "allocate": "{\n  \"strategy\": \"always\",\n  \"refresh\": 5,\n  \"concurrency\": 3\n}"
  }
}
```
