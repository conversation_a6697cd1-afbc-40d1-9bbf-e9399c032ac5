---
type: "manual"
---

# Endpoint: Reset Client Traffic

## Description

This route is used to reset the traffic statistics for a specific client identified by their email address (`{email}`) within a particular inbound identified by its ID (`{inboundId}`).

## Path Parameters

- `{inboundId}` : Identifier of the specific inbound where the client belongs.
- `{email}` : Email address of the client for whom traffic statistics are being reset.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Resetting the client traffic through this endpoint affects the statistics for the specified client within the specified inbound.
- Verify that the provided {inboundId} corresponds to an existing inbound and `{email}` corresponds to an existing client within the system.
- Handle any potential errors or failure messages returned in the response.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/panel/api/inbounds/{inboundId}/resetClientTraffic/{email}
> ```

### Response: 200 (If successful)

```json
{
  "success": true,
  "msg": "Traffic has been reset Successfully",
  "obj": null
}
```

### Response: 200 (If successful, alternative)

```json
{
  "success": true,
  "msg": "Traffic has been reset.",
  "obj": null
}
```

### Response: 500 Internal Server Error

Error 500 can be if the client does not exist.
