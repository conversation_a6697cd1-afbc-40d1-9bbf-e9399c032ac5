---
type: "manual"
---

# Endpoint: Server Status

## Description

This route is used to retrieve the status of the server, including information about the server's load, memory usage, and other relevant metrics.

## Note

- A valid session ID is required (obtained from the login endpoint). Include the session ID in a cookie named `3x-ui` for authorization. Example: `Cookie: 3x-ui=session_hash`.
- Handle any potential errors or failure messages returned in the response.

### Method: POST

> ```text
> https://{{HOST}}:{{PORT}}{{WEBBASEPATH}}/server/status
> ```

### Headers

|Content-Type|Value|
|---|---|
|Accept|application/json|

### Response: 200

```json
{
    "success": true,
    "msg": "",
    "obj": {
        "cpu": 4.545454546657183,
        "cpuCores": 1,
        "logicalPro": 1,
        "cpuSpeedMhz": 2399.998,
        "mem": {
            "current": 157421568,
            "total": 476143616
        },
        "swap": {
            "current": 0,
            "total": 0
        },
        "disk": {
            "current": 4486017024,
            "total": 21027491840
        },
        "xray": {
            "state": "running",
            "errorMsg": "",
            "version": "25.6.8"
        },
        "uptime": 421801,
        "loads": [
            0,
            0.02,
            0
        ],
        "tcpCount": 48,
        "udpCount": 6,
        "netIO": {
            "up": 37448,
            "down": 5127
        },
        "netTraffic": {
            "sent": 5627899872,
            "recv": 6039460618
        },
        "publicIP": {
            "ipv4": "*************",
            "ipv6": "N/A"
        },
        "appStats": {
            "threads": 16,
            "mem": 67130632,
            "uptime": 49881
        }
    }
}
```

Connection Stats: `tcpCount` and `udpCount` are the number of active TCP and UDP connections on the server.
Overall Speed: `netIO` is the number of bytes sent and received per second.
Total Data: `netTraffic` is the total number of bytes sent and received since the server started.
