# Payment System Documentation

This document describes the clean, practical payment system implementation for the SmartVPN project.

## Overview

The payment system supports multiple payment gateways with a unified interface:
- **Free Gateway**: For promotional/free subscriptions
- **Manual Gateway**: For admin-processed payments
- **Cash Gateway**: For offline cash payments  
- **T-Bank Gateway**: For online card payments via T-Bank (ex. Tinkoff)

## Architecture

### Core Components

1. **PaymentGatewayInterface**: Unified interface for all payment gateways
2. **AbstractPaymentGateway**: Base class with common functionality
3. **PaymentResult**: Standardized result object for all operations
4. **OrderService**: Handles order creation and management
5. **PaymentService**: Orchestrates payment processing and gateway management

### Gateway Implementations

- `FreeGateway`: Instant approval for 0-amount payments
- `ManualGateway`: Admin-confirmed payments with configurable auto-approval
- `CashGateway`: Offline payments with receipt requirements
- `TBankGateway`: Full T-Bank integration using `paveldanilin/php-tinkoff-payment`

## Configuration

### Environment Variables

```env
# Payment Gateway Configuration
PAYMENT_DEFAULT_GATEWAY=free
PAYMENT_FREE_ENABLED=true
PAYMENT_MANUAL_ENABLED=true
PAYMENT_CASH_ENABLED=true
PAYMENT_TBANK_ENABLED=false

# T-Bank Payment Gateway
TBANK_TERMINAL_KEY=your_terminal_key
TBANK_PASSWORD=your_password
TBANK_API_URL=https://securepay.tinkoff.ru/v2
TBANK_SUCCESS_URL={app_url}/access/{uuid}/plan/success/{order_id}
TBANK_FAIL_URL={app_url}/access/{uuid}/plan/failure/{order_id}
TBANK_NOTIFICATION_URL={app_url}/webhook/tbank
```

### Gateway Configuration

Each gateway is configured in `config/payments.php`:

```php
'gateways' => [
    'tbank' => [
        'driver' => 'tbank',
        'name' => 'T-Bank',
        'enabled' => env('PAYMENT_TBANK_ENABLED', false),
        'supports_recurring' => true,
        'supports_webhooks' => true,
        'supports_refunds' => true,
        'config' => [
            'terminal_key' => env('TBANK_TERMINAL_KEY'),
            'password' => env('TBANK_PASSWORD'),
            // ... other config
        ],
    ],
]
```

## Usage Examples

### Creating an Order and Payment

```php
use App\Services\OrderService;
use App\Services\PaymentService;

// Create order
$order = app(OrderService::class)->createVpnOrder($user, $plan, $quantity);

// Create payment
$result = app(PaymentService::class)->createPayment($order, 'tbank');

if ($result->isSuccess()) {
    // Redirect to payment URL for T-Bank
    return redirect($result->paymentUrl);
} else {
    // Handle error
    return back()->withErrors(['error' => $result->message]);
}
```

### Handling Webhooks

Webhooks are automatically handled by the `WebhookController`:

```php
// T-Bank webhook endpoint: POST /webhook/tbank
// Generic webhook endpoint: POST /webhook/{gateway}
```

All webhook data is logged to the `payment_webhooks` table for debugging and audit purposes.

### Testing the System

Use the built-in test command:

```bash
# Test free gateway
php artisan payment:test free

# Test T-Bank gateway
php artisan payment:test tbank --amount=1000

# Test manual gateway
php artisan payment:test manual --amount=500
```

## T-Bank Integration

### Features

- One-step and two-step payments
- Automatic currency conversion (kopecks to rubles)
- Webhook notifications
- Payment status tracking
- Refund support
- Recurring payments support

### URL Placeholders

T-Bank URLs support placeholders:
- `{app_url}`: Application base URL
- `{uuid}`: User UUID
- `{order_id}`: Order public ID

### Webhook Processing

T-Bank webhooks are processed automatically:
1. Webhook data logged to `payment_webhooks` table
2. Payment status updated based on webhook status
3. Order status updated when payment is confirmed

## Database Schema

### Tables Used

- `orders`: Order information
- `order_items`: Order line items
- `payments`: Payment records
- `payment_methods`: Available payment methods
- `payment_webhooks`: Webhook logs

### Payment Statuses

- `pending`: Payment created, awaiting completion
- `paid`: Payment successfully completed
- `failed`: Payment failed
- `cancelled`: Payment cancelled
- `refunded`: Payment refunded

### Order Statuses

- `pending`: Order created, awaiting payment
- `paid`: Payment received
- `completed`: Order fulfilled
- `cancelled`: Order cancelled

## Security Features

### Webhook Security

- CSRF protection disabled for webhook endpoints
- All webhook data logged for audit
- IP address and user agent tracking
- Signature verification (where supported)

### Payment Security

- External payment IDs used for gateway communication
- Internal UUIDs for database references
- Comprehensive logging of all operations
- Transaction-safe order and payment creation

## Error Handling

### Payment Failures

All payment operations return `PaymentResult` objects:

```php
if ($result->isFailure()) {
    $errorMessage = $result->message;
    $errorCode = $result->errorCode;
    // Handle error appropriately
}
```

### Exception Handling

- All exceptions logged with context
- Graceful degradation for gateway failures
- Transaction rollback on errors

## Monitoring and Logging

### Log Levels

- `info`: Successful operations
- `warning`: Non-critical issues
- `error`: Failed operations
- `debug`: Detailed debugging information

### Webhook Logging

All webhooks are logged to `payment_webhooks` table with:
- Raw payload data
- HTTP headers
- Processing results
- Retry information

## Extending the System

### Adding New Gateways

1. Create gateway class implementing `PaymentGatewayInterface`
2. Extend `AbstractPaymentGateway` for common functionality
3. Add configuration to `config/payments.php`
4. Register in `PaymentService::createGateway()`

### Example Gateway Implementation

```php
class NewGateway extends AbstractPaymentGateway
{
    public function getName(): string
    {
        return 'new_gateway';
    }

    public function createPayment(array $data): PaymentResult
    {
        // Implementation
    }

    // ... other required methods
}
```

## Best Practices

1. **Always use transactions** for order/payment creation
2. **Log all operations** with appropriate context
3. **Handle failures gracefully** with user-friendly messages
4. **Test thoroughly** with the test command
5. **Monitor webhook logs** for integration issues
6. **Use environment-specific configuration** for test/production

## Troubleshooting

### Common Issues

1. **T-Bank payments failing**: Check terminal credentials and API URL
2. **Webhooks not working**: Verify CSRF exclusion and URL accessibility
3. **Database errors**: Check migration status and table structure
4. **Gateway not found**: Verify gateway is enabled in configuration

### Debug Commands

```bash
# Test specific gateway
php artisan payment:test {gateway}

# Check payment method configuration
php artisan tinker
>>> App\Models\PaymentMethod::all()

# View recent webhook logs
>>> App\Models\PaymentWebhook::latest()->take(10)->get()
```
