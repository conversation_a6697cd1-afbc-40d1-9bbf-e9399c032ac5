# Proxy Configuration Guide

This document explains how to configure <PERSON><PERSON>'s trusted proxies and trusted hosts settings for the SmartVPN service application.

## Overview

The application is configured to properly handle client IP addresses and host headers when running behind reverse proxies, load balancers, or CDNs. This is crucial for:

- Accurate client IP detection for security features
- Proper URL generation
- Webhook security
- Rate limiting
- Audit logging

## Configuration Files

### 1. `config/proxy.php`

Main configuration file containing:
- Trusted proxy IP addresses/ranges
- Trusted host names
- Environment-specific settings
- Webhook-specific configuration
- Security and debug settings

### 2. `bootstrap/app.php`

Contains the middleware configuration for:
- Trusted proxy configuration
- Trusted host configuration
- Dynamic configuration based on environment

### 3. `app/Support/ProxyHelper.php`

Helper class containing static methods for:
- Proxy configuration management
- IP range validation
- Host trust validation
- Debug information collection

### 4. `app/Support/helpers.php`

Global helper functions that provide backward compatibility and convenience access to proxy configuration methods. Automatically loaded via Composer.

### 3. `.env` Configuration

Environment variables for proxy configuration:

```env
# Trusted Proxies and Hosts Configuration
TRUSTED_PROXIES="10.0.0.0/8,**********/12,***********/16"
TRUSTED_PROXY_HEADERS=HEADER_X_FORWARDED_ALL
TRUSTED_HOSTS="localhost,127.0.0.1,svs.local,svs.devet.ru"
TRUSTED_HOSTS_ALLOW_SUBDOMAINS=true

# Security settings
LOG_SUSPICIOUS_REQUESTS=true
PROXY_STRICT_MODE=false
RATE_LIMIT_BY_REAL_IP=true

# Debug settings (development only)
DEBUG_PROXY_HEADERS=false
DEBUG_SHOW_IP_INFO=false
```

## Environment-Specific Configuration

### Local Development
- **Hosts**: `localhost`, `127.0.0.1`, `svs.local`, `*.test`, `*.local`
- **Proxies**: Common private network ranges
- **Trust all proxies**: Optional (set `LOCAL_TRUST_ALL_PROXIES=true`)

### Remote Test Environment
- **Hosts**: `svs.devet.ru`
- **Proxies**: Specific proxy IPs or ranges
- **Enhanced logging**: Enabled for debugging

### Production
- **Hosts**: Production domain names only
- **Proxies**: Specific trusted proxy IPs/ranges
- **Strict mode**: Recommended
- **Debug features**: Disabled

## Webhook Security

### T-Bank Webhook IPs
The following IP ranges are configured for T-Bank webhooks:
- `************/23`
- `***********/27`
- `***********/27`
- `***********/25`
- `************`
- `************`

### Webhook Endpoints
- `/api/webhooks/tbank` - T-Bank specific webhook
- `/api/webhooks/{gateway}` - Generic webhook handler

## Testing and Validation

### Console Commands

#### Test Proxy Configuration
```bash
php artisan proxy:test --show-config
```

#### Test Specific IP/Host
```bash
php artisan proxy:test --test-ip=*********** --test-host=example.com
```

#### Simulate Webhook
```bash
php artisan proxy:test --simulate-webhook
```

### Test Endpoints

#### Local Development Only
- `GET /proxy-test` - Web route for proxy information
- `GET /api/proxy-test/info` - API route with detailed proxy info
- `POST /api/proxy-test/webhook/{gateway}` - Simulate webhook

#### Health Checks
- `GET /api/health` - Basic health check
- `GET /api/health/detailed` - Detailed health check with IP info

## Security Features

### IP-Based Security
- Automatic detection of suspicious requests
- Logging of untrusted proxy attempts
- Rate limiting based on real client IP
- Webhook IP validation

### Host Header Protection
- Validation of Host headers against trusted list
- Prevention of Host header injection attacks
- Subdomain support configuration

### Rate Limiting
- **Webhooks**: 100/min for trusted sources, 10/min for others
- **API**: 60/min per user/IP
- **Sensitive endpoints**: 5/min per IP
- **Global**: 1000/min per IP

## Middleware

### `LogProxyInformation`
- Logs proxy headers for debugging
- Detects and logs suspicious requests
- Adds debug headers in development
- Validates request sources

## Troubleshooting

### Common Issues

#### 1. Incorrect Client IP Detection
**Symptoms**: Wrong IP addresses in logs, rate limiting issues
**Solutions**:
- Check `TRUSTED_PROXIES` configuration
- Verify proxy headers are being sent correctly
- Enable debug logging: `DEBUG_PROXY_HEADERS=true`

#### 2. Host Header Rejection
**Symptoms**: 400 Bad Request errors
**Solutions**:
- Add domain to `TRUSTED_HOSTS`
- Check subdomain configuration
- Verify `APP_URL` is set correctly

#### 3. Webhook Failures
**Symptoms**: Webhooks returning 403/429 errors
**Solutions**:
- Verify webhook IP ranges in configuration
- Check rate limiting settings
- Review webhook logs for suspicious activity

### Debug Information

#### Enable Debug Mode (Development Only)
```env
DEBUG_PROXY_HEADERS=true
DEBUG_SHOW_IP_INFO=true
```

#### Check Proxy Information
Visit `/proxy-test` in local environment to see:
- Current proxy configuration
- Request headers
- Client IP detection
- Server information

## Best Practices

### Production Deployment
1. **Disable debug features**:
   ```env
   DEBUG_PROXY_HEADERS=false
   DEBUG_SHOW_IP_INFO=false
   ```

2. **Use specific proxy IPs** instead of wildcards:
   ```env
   TRUSTED_PROXIES="***********,***********"
   ```

3. **Enable strict mode**:
   ```env
   PROXY_STRICT_MODE=true
   ```

4. **Configure monitoring** for suspicious requests

### Security Considerations
- Regularly review and update trusted proxy IPs
- Monitor logs for suspicious activity
- Use HTTPS for all webhook endpoints
- Implement proper webhook signature verification
- Keep webhook IP ranges up to date

### Performance Optimization
- Use specific IP ranges instead of broad CIDR blocks
- Enable rate limiting by real IP
- Configure appropriate cache settings
- Monitor webhook processing performance

## Integration with Payment System

The proxy configuration is specifically designed to work with:
- **T-Bank payment gateway** webhooks
- **Payment webhook security** features
- **IP-based rate limiting** for payment endpoints
- **Audit logging** with accurate client IPs

All payment-related endpoints automatically benefit from the proxy configuration for accurate IP detection and security.
