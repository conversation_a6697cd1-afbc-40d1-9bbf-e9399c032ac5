# VPN Purchase System Documentation

This document describes the VPN subscription purchase system that allows users to select and purchase VPN plans using various payment methods.

## Overview

The VPN purchase system consists of two main controllers that handle the subscription purchase flow:

1. **PlanSelectionController** - Displays available plans and payment methods
2. **VpnPurchaseController** - Processes purchases and integrates with the payment system

## Architecture

### Controllers

#### PlanSelectionController
- **Purpose**: Display subscription plans and payment methods for plan selection
- **Route**: `GET /access/{uuid}/plan/select`
- **Security**: Uses user UUID for access (read-only, secure)

#### VpnPurchaseController  
- **Purpose**: Process plan purchases and create payment requests
- **Route**: `POST /access/{uuid}/plan/purchase`
- **Integration**: Uses existing PaymentService and PaymentGatewayManager

### Routes

```php
// Display available subscription plans + payment methods
Route::get('/access/{uuid}/plan/select', [PlanSelectionController::class, 'show'])
    ->whereUuid('uuid')
    ->name('vpn.plan.selection');

// Process plan selection and payment method → generate payment
Route::post('/access/{uuid}/plan/purchase', [VpnPurchaseController::class, 'purchase'])
    ->whereUuid('uuid')
    ->name('vpn.plan.purchase');

// Payment result pages
Route::get('/access/{uuid}/plan/success/{order}', ...)
    ->name('vpn.plan.success');
Route::get('/access/{uuid}/plan/failure/{order}', ...)
    ->name('vpn.plan.failure');
```

## Features

### Plan Selection Logic

#### Available Plans
- Loads all public and active subscription plans (`is_public = true`, `is_active = true`, `is_archived = false`)
- Orders plans by price for consistent display
- Includes current plan even if it's archived/inactive (for renewals)

#### Current Subscription Handling
- Displays current subscription status at the top of the page
- Shows subscription expiry date and remaining time
- Marks current plan as "Your Current Plan" in the plan list
- Supports expired, expiring soon, and active subscription states

#### Payment Methods
- Loads active payment methods from database
- Filters methods based on gateway availability
- Only shows payment methods with working gateways

### Purchase Processing

#### Subscription Analysis
The system analyzes the user's current subscription and determines the purchase type:

1. **New Subscription**: User has no active subscription
2. **Renewal**: User selects their current plan
3. **Plan Change**: User selects a different plan (upgrade/downgrade)

#### Pricing Logic
- **New/Renewal**: Full plan price
- **Plan Change**: Full price of new plan (prorating can be added later)

#### Order Creation
- Creates Order record with calculated amount
- Sets appropriate description based on purchase type
- Links order to user and includes metadata

#### Payment Integration
- Uses existing PaymentService for payment processing
- Creates PaymentRequest DTO with order details
- Handles payment gateway responses
- Supports both redirect and JSON responses

### Security Features

#### Input Validation
- UUID format validation for user identification
- Plan availability validation (public, active, not archived)
- Payment method availability validation
- User account status validation (active users only)

#### Access Control
- Users can only access their own subscription plans via UUID
- Current plan access allowed even if archived (for renewals)
- Inactive plans blocked for new purchases

#### Error Handling
- Comprehensive validation error handling
- Payment error categorization and logging
- User-friendly error messages
- Graceful degradation for system errors

## Usage Examples

### Basic Plan Selection
```php
// User visits plan selection page
GET /access/********-1234-1234-1234-************/plan/select

// System displays:
// - Current subscription status (if any)
// - Available subscription plans
// - Active payment methods
// - Interactive selection form
```

### Purchase Processing
```php
// User submits plan selection
POST /access/********-1234-1234-1234-************/plan/purchase
Content-Type: application/x-www-form-urlencoded

plan_id=5&payment_method=manual&_token=csrf_token

// System processes:
// 1. Validates user and input
// 2. Analyzes subscription type (new/renewal/change)
// 3. Creates order with calculated pricing
// 4. Initiates payment through PaymentService
// 5. Redirects to payment gateway or returns JSON
```

### AJAX Support
```javascript
// Frontend can use AJAX for seamless experience
fetch('/access/uuid/plan/purchase', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'X-CSRF-TOKEN': token
    },
    body: 'plan_id=5&payment_method=manual'
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        window.location.href = data.payment_url;
    } else {
        showError(data.message);
    }
});
```

## Database Integration

### Required Models
- **User**: Customer information and relationships
- **SubscriptionPlan**: Available subscription plans
- **PaymentMethod**: Available payment gateways
- **Subscription**: Current user subscriptions
- **Order**: Purchase orders and payment tracking

### Model Relationships
```php
// User model
public function currentSubscription()
{
    return $this->hasOne(Subscription::class)->where('status', 'active');
}

// Subscription model
public function plan()
{
    return $this->belongsTo(SubscriptionPlan::class);
}

// Order model
public function user()
{
    return $this->belongsTo(User::class);
}
```

## Error Handling

### Validation Errors
- Invalid UUID format
- Non-existent or inactive users
- Unavailable subscription plans
- Inactive payment methods

### Payment Errors
- Gateway configuration issues
- Payment processing failures
- Network connectivity problems
- Invalid payment parameters

### System Errors
- Database connection issues
- Service unavailability
- Unexpected exceptions

## Testing

### Unit Tests
- **VpnPurchaseControllerTest**: Tests purchase processing logic
- **PlanSelectionControllerTest**: Tests plan display logic
- Comprehensive test coverage for all scenarios

### Test Scenarios
- New subscription purchases
- Plan renewals
- Plan upgrades/downgrades
- Validation error handling
- Payment error handling
- AJAX request handling

## Integration Points

### PaymentService Integration
- Uses existing PaymentService for payment processing
- Creates PaymentRequest DTOs with proper metadata
- Handles PaymentResponse objects
- Supports all configured payment gateways

### Order Management
- Integrates with existing Order model
- Creates orders with proper status tracking
- Includes descriptive notes for purchase types
- Links orders to users and subscription plans

### Subscription Management
- Reads current subscription status
- Supports subscription lifecycle management
- Handles expired and expiring subscriptions
- Prepares for subscription activation after payment

## Future Enhancements

### Prorating Support
- Calculate prorated amounts for plan changes
- Handle partial refunds for downgrades
- Support mid-cycle plan changes

### Subscription Management
- Automatic subscription activation after payment
- Subscription renewal automation
- Grace period handling for expired subscriptions

### Enhanced UI
- Real-time plan comparison
- Subscription usage statistics
- Payment history integration
- Mobile-responsive design improvements

## Security Considerations

### Data Protection
- User UUIDs provide secure access without exposing internal IDs
- Payment data handled through secure PaymentService
- No sensitive payment information stored locally

### Access Control
- UUID-based access control for plan selection
- User account status validation
- Plan availability restrictions

### Audit Logging
- All purchase attempts logged
- Payment processing events tracked
- Error conditions recorded for debugging
