# Webhook Idempotency and Duplicate Handling

This document describes the comprehensive webhook idempotency system that prevents issues with repeated webhook deliveries and ensures data integrity.

## Overview

Payment gateways often send duplicate webhooks due to network issues, timeouts, or retry mechanisms. Our idempotency system ensures that processing the same webhook multiple times produces the same result without side effects or data corruption.

## Key Features

### 1. Duplicate Webhook Detection
- Detects when a webhook is received for a payment already in the same status
- Prevents unnecessary status updates and potential data corruption
- Maintains payment integrity across multiple webhook deliveries

### 2. Final State Protection
- Prevents transitions from final states (paid, failed, cancelled, refunded) to other states
- Allows only specific exceptional transitions (e.g., paid → refunded for legitimate refunds)
- Protects against malicious or erroneous webhook attempts

### 3. Complete Audit Trail
- All webhooks are logged to `payment_webhooks` table regardless of processing outcome
- Provides complete audit trail of all webhook deliveries from payment gateways
- Enables debugging and compliance reporting

### 4. Idempotent Processing
- Same webhook processed multiple times produces identical results
- No side effects from duplicate webhook processing
- Maintains system consistency and reliability

## Architecture Components

### PaymentStateHelper

Manages payment state transitions and validation:

```php
class PaymentStateHelper
{
    // Final states that should not be changed by webhooks
    public const FINAL_STATES = ['paid', 'failed', 'cancelled', 'refunded', 'disputed'];
    
    // Transitional states that can be updated by webhooks
    public const TRANSITIONAL_STATES = ['pending', 'processing', 'authorized'];
    
    public static function shouldProcessWebhook(string $currentStatus, string $webhookStatus): bool;
    public static function isTransitionAllowed(string $fromStatus, string $toStatus): bool;
}
```

### Enhanced WebhookResult

Supports duplicate detection and handling:

```php
// For duplicate webhooks
WebhookResult::duplicate(
    paymentId: $paymentId,
    currentStatus: $currentStatus,
    message: "Duplicate webhook: payment already has status 'paid'",
    additionalData: ['is_duplicate' => true, 'duplicate_reason' => 'same_status']
);
```

### Gateway-Level Duplicate Detection

Each gateway implements `checkForDuplicateWebhook()`:

```php
private function checkForDuplicateWebhook(string $paymentId, string $mappedStatus, array $webhookData): ?WebhookResult
{
    $payment = Payment::where('external_payment_id', $paymentId)->first();
    
    // Check for same status (duplicate)
    if ($currentStatus === $mappedStatus) {
        return WebhookResult::duplicate(/* ... */);
    }
    
    // Check for blocked transitions
    if (!PaymentStateHelper::shouldProcessWebhook($currentStatus, $mappedStatus)) {
        return WebhookResult::duplicate(/* ... */);
    }
    
    return null; // Allow normal processing
}
```

## Processing Flow

### 1. Webhook Reception
```
Webhook Received → Always Log to Database → Process Through Gateway
```

### 2. Gateway Processing
```
Validate Structure → Check for Duplicates → Map Status → Return Result
```

### 3. Service-Level Handling
```
if (hasPaymentUpdate()) {
    // First-time processing - update payment status
} elseif (isDuplicate()) {
    // Duplicate detected - log but don't update
} else {
    // Other scenarios (ignored events, etc.)
}
```

## Duplicate Detection Scenarios

### Scenario 1: Same Status Webhook
```
Current Status: paid
Webhook Status: CONFIRMED (maps to paid)
Result: Duplicate detected - "same_status"
Action: Log webhook, return success, no status change
```

### Scenario 2: Final State Transition Block
```
Current Status: paid
Webhook Status: REJECTED (maps to failed)
Result: Duplicate detected - "final_state_transition_blocked"
Action: Log webhook, return success, no status change
```

### Scenario 3: Valid Status Transition
```
Current Status: pending
Webhook Status: CONFIRMED (maps to paid)
Result: Normal processing
Action: Log webhook, update status to paid
```

## Logging and Monitoring

### Webhook Logging
All webhooks are logged with:
- Raw payload data
- Processing results
- Duplicate detection reasons
- Timestamp and source information

### Log Messages
- **First-time processing**: `"T-Bank webhook processed - first time processing"`
- **Duplicate same status**: `"Duplicate T-Bank webhook detected - same status"`
- **Final state blocked**: `"T-Bank webhook ignored - payment in final state"`

### Service-Level Logs
- **Status updated**: `"Webhook processed successfully - payment status updated"`
- **Duplicate ignored**: `"Webhook processed successfully - duplicate ignored"`
- **No action**: `"Webhook processed successfully - no action required"`

## Testing

### Test Command
```bash
php artisan webhook:test-idempotency {payment_id} --count=5
```

### Test Scenarios

#### 1. Multiple Identical Webhooks
```bash
# Send 5 identical CONFIRMED webhooks to a paid payment
php artisan webhook:test-idempotency ********** --count=5

Expected Results:
- All 5 webhooks processed successfully
- Payment status remains 'paid'
- All 5 webhooks logged to database
- 4 webhooks detected as duplicates
```

#### 2. Status Transition Test
```bash
# Create pending payment, send CONFIRMED webhook multiple times
Expected Results:
- First webhook: pending → paid
- Subsequent webhooks: detected as duplicates
```

#### 3. Invalid Transition Test
```bash
# Send REJECTED webhook to paid payment
Expected Results:
- Webhook processed successfully
- Payment status remains 'paid'
- Transition blocked with clear reason
```

## Configuration

### Final States
States that cannot be changed by webhooks (except exceptional cases):
- `paid` - Payment successfully completed
- `failed` - Payment permanently failed
- `cancelled` - Payment cancelled
- `refunded` - Payment refunded
- `disputed` - Payment disputed (chargebacks)

### Transitional States
States that can be updated by webhooks:
- `pending` - Payment created, awaiting completion
- `processing` - Payment being processed
- `authorized` - Payment authorized but not captured

### Exceptional Transitions
Allowed transitions from final states:
- `paid → refunded` - Legitimate refunds
- `paid → disputed` - Chargebacks and disputes
- `failed → paid` - Retry scenarios
- `cancelled → paid` - Manual approval after cancellation

## Benefits

### 1. Data Integrity
- Prevents payment status corruption from duplicate webhooks
- Maintains consistent state across multiple webhook deliveries
- Protects against race conditions and timing issues

### 2. Reliability
- Handles network issues and gateway retry mechanisms gracefully
- Provides predictable behavior for duplicate webhook scenarios
- Ensures system stability under high webhook volume

### 3. Compliance
- Complete audit trail of all webhook attempts
- Detailed logging for compliance and debugging
- Clear reasoning for all processing decisions

### 4. Debugging
- Easy identification of duplicate vs. legitimate webhooks
- Clear log messages for troubleshooting
- Comprehensive webhook history in database

## Best Practices

### 1. Always Log First
- Log all webhooks to database before processing
- Maintain complete audit trail regardless of processing outcome
- Include sufficient context for debugging

### 2. Check State Before Processing
- Validate current payment state before applying changes
- Use PaymentStateHelper for consistent state management
- Provide clear reasons for blocked transitions

### 3. Return Appropriate Results
- Use WebhookResult::duplicate() for duplicate scenarios
- Include detailed additional data for debugging
- Maintain consistent response format across gateways

### 4. Monitor and Alert
- Monitor duplicate webhook rates for anomalies
- Alert on unusual webhook patterns
- Track processing success rates by gateway

## Implementation for New Gateways

When implementing idempotency for new gateways:

1. **Add duplicate check method**:
```php
private function checkForDuplicateWebhook(string $paymentId, string $mappedStatus, array $webhookData): ?WebhookResult
```

2. **Use PaymentStateHelper**:
```php
if (!PaymentStateHelper::shouldProcessWebhook($currentStatus, $mappedStatus)) {
    return WebhookResult::duplicate(/* ... */);
}
```

3. **Return appropriate WebhookResult**:
```php
return WebhookResult::duplicate(
    paymentId: $paymentId,
    currentStatus: $currentStatus,
    message: "Clear reason for duplicate detection",
    additionalData: ['duplicate_reason' => 'specific_reason']
);
```

This idempotency system ensures robust webhook processing that can handle any volume of duplicate webhooks while maintaining data integrity and providing complete audit trails.
