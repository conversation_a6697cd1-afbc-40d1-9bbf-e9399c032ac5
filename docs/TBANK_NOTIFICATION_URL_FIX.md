# T-Bank Notification URL Fix Documentation

This document describes the fix for T-Bank payment gateway notification URL issue where environment variable placeholders were not being properly resolved.

## Problem Analysis

### Root Cause
The T-Bank payment gateway was sending notification URLs with unresolved placeholders to the T-Bank API, causing webhook notifications to fail.

### Issue Details
- **Problem**: NotificationURL was sent as literal string `"{APP_URL}/{TBANK_NOTIFICATION_ROUTE}"`
- **Expected**: NotificationURL should be resolved to actual URL like `"https://svs.devet.ru:6443/webhook/tbank"`
- **Impact**: T-Bank could not send payment status notifications back to the application
- **Other URLs**: SuccessURL and FailURL were correctly resolved with actual domains and UUIDs

### Log Analysis
Debug logs showed:
```json
{
  "NotificationURL": "{APP_URL}/{TBANK_NOTIFICATION_ROUTE}",
  "SuccessURL": "https://svs.devet.ru:6443/access/uuid/plan/success/order",
  "FailURL": "https://svs.devet.ru:6443/access/uuid/plan/failure/order"
}
```

## Solution Implementation

### 1. Enhanced Notification URL Processing
Modified the `buildPaymentData` method to detect and resolve placeholder URLs:

```php
if ($request->notificationUrl || $this->getConfigValue('notification_url')) {
    $notificationUrl = $request->notificationUrl ?? $this->getConfigValue('notification_url');
    
    // If notification URL contains placeholders, resolve them
    if ($notificationUrl && str_contains($notificationUrl, '{')) {
        $notificationUrl = $this->resolveNotificationUrl($notificationUrl);
    }
    
    $data['NotificationURL'] = $notificationUrl;
}
```

### 2. Notification URL Resolution Method
Added comprehensive URL resolution with multiple fallback strategies:

```php
/**
 * Resolve notification URL by replacing placeholders with actual values.
 */
private function resolveNotificationUrl(string $url): string
{
    // Replace {APP_URL} with actual application URL
    if (str_contains($url, '{APP_URL}')) {
        $url = str_replace('{APP_URL}', rtrim(config('app.url'), '/'), $url);
    }
    
    // Replace {TBANK_NOTIFICATION_ROUTE} with actual webhook route
    if (str_contains($url, '{TBANK_NOTIFICATION_ROUTE}')) {
        $url = str_replace('{TBANK_NOTIFICATION_ROUTE}', 'webhook/tbank', $url);
    }
    
    // If URL still contains placeholders or is invalid, build from scratch
    if (str_contains($url, '{') || !filter_var($url, FILTER_VALIDATE_URL)) {
        try {
            // Try to use named route first
            $url = route('webhook.tbank');
        } catch (\Exception $e) {
            // Fallback to URL helper
            $url = url('/webhook/tbank');
        }
    }
    
    return $url;
}
```

### 3. Webhook Route Registration
Added proper webhook routes to handle T-Bank notifications:

```php
// Payment Webhook Routes
Route::prefix('webhook')->group(function () {
    Route::post('/tbank', [App\Http\Controllers\Api\WebhookController::class, 'tbank'])
        ->name('webhook.tbank');
    
    Route::post('/{gateway}', [App\Http\Controllers\Api\WebhookController::class, 'handle'])
        ->name('webhook.handle');
});
```

### 4. Environment Configuration
Added proper environment variable examples:

```env
# T-Bank Payment Gateway Configuration
TBANK_TERMINAL_KEY=your_terminal_key
TBANK_PASSWORD=your_password
TBANK_NOTIFICATION_URL={APP_URL}/{TBANK_NOTIFICATION_ROUTE}
```

## Files Modified

### 1. `app/Services/Payment/Gateways/TBankGateway.php`
- **Enhanced**: `buildPaymentData()` method to detect and resolve placeholder URLs
- **Added**: `resolveNotificationUrl()` method with comprehensive URL resolution
- **Added**: Debug logging for URL resolution process

### 2. `routes/web.php`
- **Added**: Webhook routes for T-Bank and generic payment gateway notifications
- **Added**: Named routes for proper URL generation

### 3. `.env.example`
- **Added**: T-Bank configuration section with proper placeholder examples
- **Added**: Documentation for notification URL format

### 4. `tests/Unit/TBankNotificationUrlTest.php` (New)
- **Added**: Comprehensive tests for URL resolution scenarios
- **Tests**: Placeholder resolution, partial placeholders, direct URLs
- **Tests**: Configuration-based URLs and fallback mechanisms

## URL Resolution Strategies

### 1. Placeholder Replacement
- `{APP_URL}` → `config('app.url')` (e.g., `https://svs.devet.ru:6443`)
- `{TBANK_NOTIFICATION_ROUTE}` → `webhook/tbank`

### 2. Route-Based Generation
- Uses Laravel's `route('webhook.tbank')` when available
- Provides proper URL generation with domain and protocol

### 3. URL Helper Fallback
- Falls back to `url('/webhook/tbank')` if route generation fails
- Ensures a valid URL is always generated

### 4. Validation and Logging
- Validates generated URLs using `filter_var()`
- Logs resolution process for debugging

## Testing

### Unit Tests
The fix includes comprehensive unit tests covering:

1. **Placeholder Resolution**: Full placeholder strings resolve correctly
2. **Partial Placeholders**: Mixed placeholder and static content
3. **Direct URLs**: Non-placeholder URLs pass through unchanged
4. **Configuration URLs**: URLs from gateway configuration resolve properly
5. **Invalid Placeholders**: Fallback handling for unknown placeholders
6. **URL Validation**: Generated URLs are valid and accessible

### Test Examples
```php
// Test placeholder resolution
'{APP_URL}/{TBANK_NOTIFICATION_ROUTE}' → 'https://svs.devet.ru:6443/webhook/tbank'

// Test partial placeholders
'{APP_URL}/webhook/tbank' → 'https://svs.devet.ru:6443/webhook/tbank'

// Test direct URLs (no change)
'https://custom.com/webhook/tbank' → 'https://custom.com/webhook/tbank'

// Test invalid placeholders (fallback)
'{INVALID}/test' → 'https://svs.devet.ru:6443/webhook/tbank'
```

## Verification Steps

### 1. Test T-Bank Payment
```bash
php artisan payment:test tbank --debug
```

**Expected Result**: Debug log shows resolved notification URL:
```json
{
  "NotificationURL": "https://svs.devet.ru:6443/webhook/tbank"
}
```

### 2. Test VPN Purchase Flow
```bash
# Test VPN purchase with T-Bank
POST /access/{uuid}/plan/purchase
Data: plan_id=1&payment_method=tbank
```

**Expected Result**: T-Bank receives properly formatted notification URL

### 3. Verify Webhook Route
```bash
# Test webhook route accessibility
curl -X POST https://svs.devet.ru:6443/webhook/tbank
```

**Expected Result**: Route exists and is accessible (may return validation error, but route should be found)

## Configuration Options

### Environment Variables
```env
# Basic configuration
TBANK_TERMINAL_KEY=your_terminal_key
TBANK_PASSWORD=your_password

# Notification URL with placeholders (recommended)
TBANK_NOTIFICATION_URL={APP_URL}/{TBANK_NOTIFICATION_ROUTE}

# Or direct URL (alternative)
TBANK_NOTIFICATION_URL=https://yourdomain.com/webhook/tbank
```

### Configuration File
```php
// config/payments.php
'tbank' => [
    'notification_url' => env('TBANK_NOTIFICATION_URL', '{APP_URL}/{TBANK_NOTIFICATION_ROUTE}'),
    // ... other config
]
```

## Benefits

### 1. ✅ Fixes Notification URL Resolution
- Eliminates unresolved placeholder issues
- Ensures T-Bank receives valid webhook URLs

### 2. ✅ Flexible Configuration
- Supports placeholder-based configuration
- Allows direct URL specification
- Provides multiple fallback strategies

### 3. ✅ Robust Error Handling
- Validates generated URLs
- Provides fallback URL generation
- Logs resolution process for debugging

### 4. ✅ Proper Route Integration
- Uses Laravel's routing system
- Supports named routes for better maintainability
- Integrates with existing webhook infrastructure

### 5. ✅ Comprehensive Testing
- Tests all resolution scenarios
- Validates URL generation accuracy
- Ensures fallback mechanisms work

## Troubleshooting

### Common Issues

#### 1. Notification URL Still Contains Placeholders
**Cause**: Environment variables not properly set or cached
**Solution**:
```bash
php artisan config:clear
php artisan config:cache
```

#### 2. Invalid Notification URL Generated
**Cause**: APP_URL not properly configured
**Solution**: Verify `.env` file contains correct `APP_URL`

#### 3. Webhook Route Not Found
**Cause**: Routes not properly registered
**Solution**: Clear route cache and verify route registration
```bash
php artisan route:clear
php artisan route:list | grep webhook
```

### Debug Information
The gateway logs URL resolution process:
```php
$this->log('debug', 'Resolved T-Bank notification URL', [
    'original_url' => $originalUrl,
    'resolved_url' => $resolvedUrl,
]);
```

## Future Enhancements

### 1. Dynamic Route Detection
Could automatically detect available webhook routes:
```php
if (Route::has('webhook.tbank')) {
    $url = route('webhook.tbank');
}
```

### 2. Configuration Validation
Could validate notification URLs during gateway initialization:
```php
public function validateNotificationUrl(string $url): bool
{
    $resolved = $this->resolveNotificationUrl($url);
    return filter_var($resolved, FILTER_VALIDATE_URL) !== false;
}
```

### 3. URL Template System
Could implement a more flexible template system:
```php
private const URL_TEMPLATES = [
    'APP_URL' => 'config:app.url',
    'TBANK_NOTIFICATION_ROUTE' => 'route:webhook.tbank',
    'DOMAIN' => 'request:getHost',
];
```

## Conclusion

The T-Bank notification URL fix resolves the placeholder resolution issue by implementing comprehensive URL processing with multiple fallback strategies. The solution ensures that T-Bank always receives properly formatted webhook URLs, enabling successful payment status notifications and completing the payment flow integration.
