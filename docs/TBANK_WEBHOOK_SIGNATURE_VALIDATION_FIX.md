# T-Bank Webhook Signature Validation Fix Documentation

This document describes the fix for T-Bank webhook signature validation issues where webhooks were failing with "Webhook signature is missing" errors despite containing Token fields.

## Problem Analysis

### Issue from Logs
```
[2025-07-16] local.INFO: Payment created successfully {
  "payment_id": "**********",
  "order_id": "ORD-XZNVD9N",
  "amount": 14900
}

[2025-07-16] local.ERROR: Webhook processing failed {
  "error": "Webhook signature is missing",
  "gateway": "tbank",
  "ip": "127.0.0.1"
}
```

### Root Cause Analysis
The error "Webhook signature is missing" was occurring because:

1. **WebhookData Signature Extraction**: The `WebhookData::fromRequest()` method was only looking for signatures in HTTP headers (`signature` or `x-signature`)
2. **T-Bank Token Location**: T-Bank sends the signature as a `Token` field in the JSON payload, not in HTTP headers
3. **Validation Failure**: Without finding a signature, `WebhookData::validate()` was failing before reaching the T-Bank gateway's signature verification logic

### T-Bank Webhook Structure
```json
{
  "TerminalKey": "1726392591291DEMO",
  "OrderId": "ORD-XZNVD9N",
  "Success": true,
  "Status": "CONFIRMED",
  "PaymentId": **********,
  "ErrorCode": "0",
  "Amount": 14900,
  "CardId": *********,
  "Pan": "430000******0777",
  "ExpDate": "1230",
  "Token": "58113548931cc236448ebdd2766b4658e6b6139279124337144028e2ff5934ad"
}
```

## Solution Implementation

### 1. Enhanced WebhookData Signature Extraction
Modified `WebhookData::fromRequest()` to handle T-Bank's Token field:

```php
// BEFORE (Only checked headers)
$signature = $headers['signature'] ?? $headers['x-signature'] ?? '';

// AFTER (Checks headers first, then payload Token field)
$signature = $headers['signature'] ?? $headers['x-signature'] ?? '';

// If no signature in headers, check for T-Bank Token field in payload
if (empty($signature) && isset($data['Token'])) {
    $signature = $data['Token'];
}
```

**Benefits**:
- Maintains compatibility with standard webhook signatures in headers
- Adds support for T-Bank's Token field in payload
- Preserves header signature priority for other gateways

### 2. Improved T-Bank Token Generation Algorithm
Enhanced the `generateToken()` method to follow T-Bank documentation exactly:

```php
/**
 * Generate T-Bank API token according to T-Bank documentation.
 * 
 * Algorithm:
 * 1. Collect root-level parameters (excluding nested objects like DATA, Receipt)
 * 2. Remove Token field if present
 * 3. Add Password parameter
 * 4. Sort parameters alphabetically by key
 * 5. Concatenate values into a string
 * 6. Apply SHA-256 hash with UTF-8 support
 */
private function generateToken(array $data): string
{
    // Remove Token if present
    unset($data['Token']);

    // Remove nested objects according to T-Bank documentation
    $excludedFields = ['DATA', 'Receipt', 'Data', 'receipt'];
    foreach ($excludedFields as $field) {
        unset($data[$field]);
    }

    // Add password
    $data['Password'] = $this->password;

    // Sort by key alphabetically
    ksort($data);

    // Create concatenated string from values only
    $concatenated = '';
    foreach ($data as $key => $value) {
        // Convert arrays/objects to JSON, but this should not happen for root-level fields
        if (is_array($value) || is_object($value)) {
            $this->log('warning', 'T-Bank token generation: unexpected array/object value', [
                'key' => $key,
                'value' => $value,
            ]);
            $value = json_encode($value);
        }
        
        // Convert to string and concatenate
        $concatenated .= (string) $value;
    }

    $token = hash('sha256', $concatenated);

    $this->log('debug', 'T-Bank token generation', [
        'data_keys' => array_keys($data),
        'concatenated_length' => strlen($concatenated),
        'token' => $token,
    ]);

    return $token;
}
```

### 3. Enhanced Debugging and Logging
Added comprehensive logging to identify signature validation issues:

```php
$this->log('debug', 'T-Bank webhook signature verification', [
    'received_token' => $receivedToken,
    'expected_token' => $expectedToken,
    'data_keys' => array_keys($dataForToken),
    'terminal_key' => $this->terminalKey,
    'has_password' => !empty($this->password),
]);

if (!$isValid) {
    $this->log('warning', 'T-Bank webhook signature mismatch', [
        'received_token' => $receivedToken,
        'expected_token' => $expectedToken,
        'payload_data' => $dataForToken,
    ]);
}
```

## Files Modified

### 1. `app/Services/Payment/DTOs/WebhookData.php`
- **Enhanced**: `fromRequest()` method to extract T-Bank Token from payload
- **Added**: Fallback signature extraction for T-Bank webhooks
- **Maintained**: Compatibility with standard header-based signatures

### 2. `app/Services/Payment/Gateways/TBankGateway.php`
- **Improved**: `generateToken()` method with T-Bank documentation compliance
- **Added**: Nested object exclusion (DATA, Receipt fields)
- **Enhanced**: Debugging and logging for token generation and verification
- **Added**: Payload sample logging for missing Token field debugging

### 3. `tests/Feature/TBankWebhookSignatureValidationTest.php` (New)
- **Comprehensive Testing**: WebhookData Token extraction
- **Real Data Testing**: Using actual webhook data from logs
- **Token Generation Testing**: Algorithm verification
- **Validation Scenarios**: Various webhook signature scenarios

### 4. `test_tbank_webhook_signature.php` (Temporary)
- **Manual Testing**: Direct webhook signature validation testing
- **Real Data Verification**: Using actual T-Bank webhook payload
- **Debug Output**: Step-by-step validation process verification

## T-Bank Token Generation Algorithm

### According to T-Bank Documentation
1. **Collect Parameters**: Only root-level parameters (exclude nested objects)
2. **Remove Token**: Remove existing Token field if present
3. **Add Password**: Add terminal password as Password parameter
4. **Sort Alphabetically**: Sort all parameters by key name
5. **Concatenate Values**: Join all parameter values into single string
6. **Hash**: Apply SHA-256 hash to concatenated string

### Implementation Details
```php
// Example data processing:
$data = [
    'TerminalKey' => '1726392591291DEMO',
    'OrderId' => 'ORD-XZNVD9N',
    'Amount' => 14900,
    'Status' => 'CONFIRMED',
    // ... other fields
];

// After processing:
$data = [
    'Amount' => 14900,           // Sorted alphabetically
    'OrderId' => 'ORD-XZNVD9N',
    'Password' => 'terminal_password',
    'Status' => 'CONFIRMED',
    'TerminalKey' => '1726392591291DEMO',
    // ... other fields
];

// Concatenated: "14900ORD-XZNVD9Nterminal_passwordCONFIRMED1726392591291DEMO..."
// Token: SHA-256 hash of concatenated string
```

## Testing and Verification

### 1. WebhookData Token Extraction Test
```php
$payload = json_encode([
    'TerminalKey' => '1726392591291DEMO',
    'OrderId' => 'ORD-XZNVD9N',
    'Token' => '58113548931cc236448ebdd2766b4658e6b6139279124337144028e2ff5934ad',
    // ... other fields
]);

$webhookData = WebhookData::fromRequest(
    headers: ['content-type' => 'application/json'],
    payload: $payload
);

// Should extract Token as signature
assert($webhookData->signature === '58113548931cc236448ebdd2766b4658e6b6139279124337144028e2ff5934ad');
assert($webhookData->validate() === true);
```

### 2. Webhook Processing Test
```bash
# Test with actual T-Bank webhook data
curl -X POST https://svs.devet.ru:6443/webhook/tbank \
  -H "Content-Type: application/json" \
  -H "User-Agent: T-Bank-Webhook/1.0" \
  -d '{
    "TerminalKey": "1726392591291DEMO",
    "OrderId": "ORD-XZNVD9N",
    "Token": "58113548931cc236448ebdd2766b4658e6b6139279124337144028e2ff5934ad",
    "Status": "CONFIRMED"
  }'

# Expected: NOT "Webhook signature is missing" error
# Expected: HTTP 200 (success) or 401 (signature verification failure)
```

### 3. Manual Testing Script
```bash
# Run comprehensive test
php test_tbank_webhook_signature.php

# Expected output:
# ✅ WebhookData created successfully
# Signature extracted: YES
# Validation result: PASS
```

## Error Resolution Flow

### Before Fix
```
T-Bank webhook → WebhookData::fromRequest() → No signature in headers → 
signature = '' → WebhookData::validate() → false → 
"Webhook signature is missing" error
```

### After Fix
```
T-Bank webhook → WebhookData::fromRequest() → No signature in headers → 
Check payload Token field → signature = Token value → 
WebhookData::validate() → true → Proceed to gateway signature verification
```

## Security Considerations

### Signature Validation Layers
1. **WebhookData Validation**: Ensures signature field is present
2. **Gateway Signature Verification**: Cryptographic validation of signature
3. **IP Address Validation**: Restricts webhook sources to allowed IPs
4. **Payload Validation**: Validates webhook data structure and content

### T-Bank Specific Security
- **Token Field**: Contains cryptographic signature of webhook data
- **Password Protection**: Uses terminal password in signature generation
- **Field Exclusion**: Excludes nested objects from signature calculation
- **Alphabetical Sorting**: Ensures consistent signature generation

## Benefits

### 1. ✅ Fixes "Webhook signature is missing" Error
- WebhookData now correctly extracts T-Bank Token field
- Webhook validation passes for T-Bank webhooks
- Eliminates false signature missing errors

### 2. ✅ Maintains Compatibility
- Standard header-based signatures still work
- Other payment gateways unaffected
- Header signatures take priority over payload tokens

### 3. ✅ Improves T-Bank Integration
- Follows T-Bank API documentation exactly
- Proper token generation algorithm implementation
- Enhanced debugging and error reporting

### 4. ✅ Better Debugging
- Comprehensive logging for signature validation
- Clear error messages for troubleshooting
- Step-by-step validation process visibility

## Troubleshooting

### Common Issues

#### 1. Still Getting "Webhook signature is missing"
**Cause**: WebhookData not finding Token field in payload
**Debug**: Check if webhook payload contains `Token` field (case-sensitive)
**Solution**: Verify webhook payload structure and JSON parsing

#### 2. Signature Verification Still Failing
**Cause**: Incorrect terminal password or token generation algorithm
**Debug**: Compare generated token with received token in logs
**Solution**: Verify T-Bank terminal credentials and algorithm implementation

#### 3. Webhook Not Reaching Application
**Cause**: CSRF protection or routing issues
**Debug**: Check if webhook URL is accessible and CSRF is disabled
**Solution**: Verify webhook routes and CSRF exclusions

### Debug Commands
```bash
# Test WebhookData extraction
php test_tbank_webhook_signature.php

# Check webhook route
curl -X POST https://svs.devet.ru:6443/webhook/tbank -d '{"test":"data"}'

# Monitor webhook logs
tail -f storage/logs/laravel.log | grep -i "webhook\|signature"
```

## Conclusion

The T-Bank webhook signature validation fix resolves the "Webhook signature is missing" error by properly extracting the Token field from T-Bank webhook payloads. The enhanced token generation algorithm follows T-Bank documentation exactly, and comprehensive logging provides better debugging capabilities. This enables successful processing of T-Bank webhook notifications and automatic payment status updates.
