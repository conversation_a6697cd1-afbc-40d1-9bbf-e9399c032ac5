# Laravel Container Dependency Injection Fix Documentation

This document describes the fix for the Laravel Container dependency injection error that occurred when running `php artisan optimize` after implementing T-Bank payment gateway changes.

## Problem Analysis

### Error Details
```
In Container.php line 1019:
  Target class [env] does not exist.

In Container.php line 1017:
  Class "env" does not exist
```

### Root Cause
The error was caused by using `app()->environment('local')` in configuration files (`config/payments.php`). During the `php artisan optimize` process, Laravel caches configuration files, but at that time the application container isn't fully available, causing Container dependency injection errors.

### Problematic Code
```php
// config/payments.php - PROBLEMATIC CODE
'verify_signature' => env('TBANK_WEBHOOK_VERIFY_SIGNATURE', !app()->environment('local')),
'allowed_ips' => array_merge([
    // T-Bank IPs...
], app()->environment('local') ? [
    // Local IPs...
] : []),
```

### Why This Fails
1. **Configuration Caching**: During `php artisan optimize`, <PERSON><PERSON> caches configuration files
2. **Container Unavailability**: At cache time, the application container isn't fully bootstrapped
3. **Dependency Injection Error**: `app()->environment()` tries to resolve from container, causing the error
4. **Laravel Best Practice**: Configuration files should only use `env()` function, not container-dependent functions

## Solution Implementation

### 1. Replace app()->environment() with env() Checks
Modified all instances of `app()->environment('local')` to use direct environment variable checks:

```php
// BEFORE (Problematic)
'verify_signature' => env('TBANK_WEBHOOK_VERIFY_SIGNATURE', !app()->environment('local')),

// AFTER (Fixed)
'verify_signature' => env('TBANK_WEBHOOK_VERIFY_SIGNATURE', env('APP_ENV') !== 'local'),
```

### 2. Environment-Based Array Merging
Fixed the IP address array merging to use environment variables:

```php
// BEFORE (Problematic)
'allowed_ips' => array_merge([
    // T-Bank webhook IP addresses
    '************/23',
    // ...
], app()->environment('local') ? [
    // Local development IPs
    '127.0.0.1/32',
    // ...
] : []),

// AFTER (Fixed)
'allowed_ips' => array_merge([
    // T-Bank webhook IP addresses
    '************/23',
    // ...
], env('APP_ENV') === 'local' ? [
    // Local development IPs
    '127.0.0.1/32',
    // ...
] : []),
```

### 3. Gateway Code Consistency
Updated TBankGateway to use the same pattern:

```php
// BEFORE (Problematic)
$verifySignature = $this->getConfigValue('verify_signature', !app()->environment('local'));
$this->log('debug', 'T-Bank webhook signature verification disabled', [
    'environment' => app()->environment(),
    // ...
]);

// AFTER (Fixed)
$verifySignature = $this->getConfigValue('verify_signature', env('APP_ENV') !== 'local');
$this->log('debug', 'T-Bank webhook signature verification disabled', [
    'environment' => env('APP_ENV'),
    // ...
]);
```

## Files Modified

### 1. `config/payments.php`
- **Line 87**: `app()->environment('local')` → `env('APP_ENV') !== 'local'`
- **Line 90**: `app()->environment('local')` → `env('APP_ENV') !== 'local'`
- **Line 99**: `app()->environment('local')` → `env('APP_ENV') === 'local'`

### 2. `app/Services/Payment/Gateways/TBankGateway.php`
- **Line 351**: `app()->environment('local')` → `env('APP_ENV') !== 'local'`
- **Line 355**: `app()->environment()` → `env('APP_ENV')`

### 3. `test_config_loading.php` (Temporary)
- **Added**: Configuration loading test script
- **Purpose**: Verify configuration can be loaded without Container errors

## Laravel Configuration Best Practices

### 1. Configuration File Rules
- **Only use `env()` function**: Configuration files should only call `env()` function
- **No container dependencies**: Avoid `app()`, `config()`, `resolve()` in configuration files
- **No service resolution**: Don't resolve services or facades in configuration files
- **Cache compatibility**: Ensure configuration can be cached without runtime dependencies

### 2. Environment Detection
```php
// ✅ CORRECT - Use env() function
$isLocal = env('APP_ENV') === 'local';
$isProduction = env('APP_ENV') === 'production';

// ❌ INCORRECT - Uses container
$isLocal = app()->environment('local');
$isProduction = app()->isProduction();
```

### 3. Conditional Configuration
```php
// ✅ CORRECT - Environment-based configuration
'setting' => env('APP_ENV') === 'local' ? 'development_value' : 'production_value',

// ❌ INCORRECT - Container-dependent
'setting' => app()->environment('local') ? 'development_value' : 'production_value',
```

## Testing and Verification

### 1. Configuration Loading Test
```bash
# Run configuration test
php test_config_loading.php

# Expected output:
# ✅ Payments configuration loaded successfully
# ✅ T-Bank configuration found
# ✅ Configuration doesn't contain app()->environment() calls
```

### 2. Optimization Test
```bash
# Clear existing cache
php artisan optimize:clear

# Test optimization (should work without errors)
php artisan optimize

# Expected: No Container dependency errors
```

### 3. Configuration Cache Test
```bash
# Test configuration caching specifically
php artisan config:cache

# Verify cached configuration works
php artisan config:show payments.gateways.tbank

# Clear cache
php artisan config:clear
```

### 4. Environment-Based Testing
```bash
# Test different environments
APP_ENV=local php artisan config:show payments.gateways.tbank.config.verify_signature
APP_ENV=production php artisan config:show payments.gateways.tbank.config.verify_signature

# Expected:
# local: false (signature verification disabled)
# production: true (signature verification enabled)
```

## Environment Configuration

### Development Environment
```env
# .env for development
APP_ENV=local
TBANK_WEBHOOK_VERIFY_SIGNATURE=false
```

**Result**: 
- Signature verification disabled
- Local development IPs included in allowed list

### Production Environment
```env
# .env for production
APP_ENV=production
TBANK_WEBHOOK_VERIFY_SIGNATURE=true
```

**Result**:
- Signature verification enabled
- Only T-Bank official IPs in allowed list

## Troubleshooting

### Common Issues

#### 1. Still Getting Container Errors
**Symptoms**: Container dependency errors during optimization
**Cause**: Other configuration files using container-dependent functions
**Solution**: Search for `app()`, `resolve()`, `config()` in config files and replace with `env()`

#### 2. Configuration Not Taking Effect
**Symptoms**: Changes to configuration not working
**Cause**: Configuration cache not cleared
**Solution**:
```bash
php artisan config:clear
php artisan optimize:clear
```

#### 3. Environment Detection Not Working
**Symptoms**: Wrong environment-based configuration
**Cause**: `APP_ENV` not set correctly
**Solution**: Verify `.env` file contains correct `APP_ENV=local`

#### 4. Optimization Still Failing
**Symptoms**: Other optimization errors
**Cause**: Other caching issues
**Solution**:
```bash
php artisan optimize:clear
php artisan view:clear
php artisan route:clear
php artisan event:clear
```

### Debug Commands
```bash
# Check current environment
php artisan tinker --execute="echo env('APP_ENV');"

# Check configuration values
php artisan config:show payments.gateways.tbank

# Test configuration loading
php test_config_loading.php

# Verify no container dependencies
grep -r "app()->" config/
```

## Benefits

### 1. ✅ Fixes Optimization Errors
- `php artisan optimize` now works without Container errors
- Configuration caching works correctly
- Application can be deployed with optimized configuration

### 2. ✅ Follows Laravel Best Practices
- Configuration files only use `env()` function
- No container dependencies in configuration
- Cache-compatible configuration structure

### 3. ✅ Maintains Functionality
- Environment-based configuration still works
- T-Bank signature verification behavior unchanged
- Local development features preserved

### 4. ✅ Improves Performance
- Configuration can be cached for better performance
- Optimization commands work correctly
- Faster application bootstrap in production

## Prevention

### 1. Code Review Guidelines
- Review configuration files for container dependencies
- Ensure only `env()` function is used in config files
- Test optimization commands before deployment

### 2. Development Practices
```php
// ✅ DO - Use env() in configuration files
'setting' => env('MY_SETTING', 'default'),

// ❌ DON'T - Use container in configuration files
'setting' => app('my.service')->getSetting(),
'setting' => config('other.setting'),
'setting' => resolve('MyClass')->method(),
```

### 3. Testing Strategy
- Include optimization testing in CI/CD pipeline
- Test configuration caching in staging environment
- Verify environment-based configuration works correctly

## Conclusion

The Container dependency injection fix resolves the `php artisan optimize` error by replacing container-dependent `app()->environment()` calls with direct environment variable checks using `env('APP_ENV')`. This follows Laravel best practices for configuration files and ensures compatibility with configuration caching while maintaining all existing functionality.
