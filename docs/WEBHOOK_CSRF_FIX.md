# Webhook CSRF Protection Fix Documentation

This document describes the fix for T-Bank webhook CSRF protection issue where external POST requests from T-Bank servers were being blocked by <PERSON><PERSON>'s CSRF middleware, preventing payment status updates.

## Problem Analysis

### Root Cause
T-Bank webhook notifications were failing to reach the application because the webhook route `/webhook/tbank` was protected by <PERSON><PERSON>'s CSRF middleware, which blocks external POST requests that don't include a valid CSRF token.

### Issue Details
- **Problem**: T-Bank servers cannot include Laravel CSRF tokens in webhook requests
- **Impact**: Payment and Order records not marked as paid after successful payment completion
- **Symptom**: Webhook requests return HTTP 419 (CSRF token mismatch)
- **Result**: Users complete payments successfully but subscriptions remain inactive

### Flow Analysis
1. ✅ **User Payment**: User successfully completes payment through T-Bank
2. ✅ **T-Bank Processing**: T-Bank processes payment and marks it as successful
3. ❌ **Webhook Notification**: T-Bank attempts to notify application via POST to `/webhook/tbank`
4. ❌ **CSRF Block**: <PERSON><PERSON>'s CSRF middleware blocks the request (HTTP 419)
5. ❌ **Status Update**: Payment and Order records remain in pending status
6. ❌ **Subscription**: User's VPN subscription is not activated

## Solution Implementation

### 1. CSRF Middleware Exclusion
Modified `bootstrap/app.php` to exclude webhook routes from CSRF protection:

```php
->withMiddleware(function (Middleware $middleware): void {
    // Exclude webhook routes from CSRF protection
    $middleware->validateCsrfTokens(except: [
        'webhook/*',
    ]);
})
```

### 2. Enhanced Webhook Configuration
Added comprehensive webhook configuration to T-Bank payment gateway:

```php
// config/payments.php
'tbank' => [
    'config' => [
        // ... existing config
        'verify_signature' => env('TBANK_WEBHOOK_VERIFY_SIGNATURE', true),
    ],
    'webhook' => [
        'verify_signature' => env('TBANK_WEBHOOK_VERIFY_SIGNATURE', true),
        'allowed_ips' => [
            // T-Bank webhook IP addresses
            '************/23',
            '***********/27',
            '***********/27',
            '***********/25',
            '************',
            '************',
        ],
        'timeout' => env('TBANK_WEBHOOK_TIMEOUT', 10),
        'max_retries' => env('TBANK_WEBHOOK_MAX_RETRIES', 3),
    ],
],
```

### 3. Enhanced Webhook Logging
Added detailed logging to T-Bank webhook processing for better debugging:

```php
$this->log('info', 'Found payment for webhook', [
    'payment_id' => $payment->id,
    'payment_public_id' => $payment->public_id,
    'current_status' => $payment->status,
    'new_status' => $mappedStatus,
    'order_id' => $payment->order_id,
]);

if (in_array($mappedStatus, ['paid', 'confirmed'])) {
    $payment->markAsPaid();
    $this->log('info', 'Payment marked as paid', [
        'payment_id' => $payment->id,
        'order_id' => $payment->order_id,
    ]);
}
```

### 4. Environment Configuration
Added webhook configuration to `.env.example`:

```env
# T-Bank Payment Gateway Configuration
TBANK_TERMINAL_KEY=
TBANK_PASSWORD=
TBANK_NOTIFICATION_URL={APP_URL}/{TBANK_NOTIFICATION_ROUTE}
TBANK_WEBHOOK_VERIFY_SIGNATURE=true
PAYMENT_TBANK_ENABLED=false
```

## Files Modified

### 1. `bootstrap/app.php`
- **Added**: CSRF middleware exclusion for `webhook/*` routes
- **Purpose**: Allow external webhook requests to bypass CSRF protection

### 2. `config/payments.php`
- **Added**: T-Bank webhook configuration section
- **Added**: Webhook signature verification settings
- **Added**: Allowed IP addresses for T-Bank webhooks
- **Added**: Webhook timeout and retry settings

### 3. `app/Services/Payment/Gateways/TBankGateway.php`
- **Enhanced**: Webhook processing with detailed logging
- **Added**: Payment status change logging
- **Added**: Payment not found logging
- **Improved**: Error handling and debugging information

### 4. `.env.example`
- **Added**: T-Bank webhook configuration variables
- **Added**: Payment gateway enable/disable setting

### 5. `tests/Feature/WebhookCsrfTest.php` (New)
- **Added**: Comprehensive tests for CSRF exclusion
- **Tests**: Webhook routes bypass CSRF protection
- **Tests**: Non-webhook routes still require CSRF
- **Tests**: Various content types and scenarios

### 6. `test_webhook_csrf.php` (Temporary)
- **Added**: Manual testing script for webhook CSRF exclusion
- **Purpose**: Verify webhook accessibility without CSRF tokens

## Security Considerations

### 1. CSRF Exclusion Scope
- **Specific Pattern**: Only `webhook/*` routes excluded, not entire application
- **Minimal Impact**: Regular application routes still protected by CSRF
- **Targeted Fix**: Addresses webhook-specific requirements

### 2. Alternative Security Measures
- **Signature Verification**: T-Bank webhooks verified using cryptographic signatures
- **IP Whitelisting**: Only allowed T-Bank IP addresses can send webhooks
- **Payload Validation**: Webhook data validated before processing
- **Database Constraints**: Payment status changes validated at model level

### 3. Security Best Practices
```php
// Webhook signature verification
if (!$this->doVerifyWebhookSignature($webhookData->headers, $webhookData->payload)) {
    $this->log('warning', 'T-Bank webhook signature verification failed');
    return false;
}

// IP address validation
$allowedIps = config("payments.gateways.{$gatewayName}.webhook.allowed_ips", []);
if (!empty($allowedIps) && !$webhookData->isFromAllowedIp($allowedIps)) {
    throw new PaymentException('Webhook from unauthorized IP address');
}
```

## Testing

### Unit Tests
The fix includes comprehensive tests covering:

1. **CSRF Exclusion**: Webhook routes bypass CSRF protection
2. **CSRF Preservation**: Non-webhook routes still require CSRF tokens
3. **Route Accessibility**: Webhook routes are properly registered
4. **Content Types**: Various webhook content types supported
5. **Logging**: Webhook reception and processing logged

### Manual Testing
```bash
# Test webhook accessibility
curl -X POST https://svs.devet.ru:6443/webhook/tbank \
  -H "Content-Type: application/json" \
  -d '{"TerminalKey":"test","OrderId":"ORD-123","Status":"CONFIRMED"}'

# Expected: HTTP 200/400/401 (not 419)
```

### Integration Testing
```php
// Test webhook processing flow
$webhookData = [
    'TerminalKey' => 'test_terminal',
    'OrderId' => 'ORD-TEST123',
    'Status' => 'CONFIRMED',
    'PaymentId' => '12345',
];

$response = $this->postJson('/webhook/tbank', $webhookData);
$this->assertNotEquals(419, $response->getStatusCode());
```

## Verification Steps

### 1. Test Webhook Accessibility
```bash
# Should return 200/400/401, NOT 419
curl -X POST https://svs.devet.ru:6443/webhook/tbank \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'
```

### 2. Test CSRF Protection Still Works
```bash
# Should return 419 (CSRF required)
curl -X POST https://svs.devet.ru:6443/access/uuid/plan/purchase \
  -H "Content-Type: application/json" \
  -d '{"plan_id": 1}'
```

### 3. Monitor Webhook Logs
```bash
# Check application logs for webhook processing
tail -f storage/logs/laravel.log | grep -i webhook
```

### 4. Test Payment Status Updates
1. Create test payment through VPN purchase system
2. Send mock T-Bank webhook notification
3. Verify Payment and Order records updated to 'paid' status

## Expected Results

### Before Fix
```
POST /webhook/tbank
Response: HTTP 419 CSRF token mismatch
Result: Payment status not updated
```

### After Fix
```
POST /webhook/tbank
Response: HTTP 200/400/401 (processed)
Result: Payment status updated correctly
```

## Troubleshooting

### Common Issues

#### 1. Still Getting HTTP 419
**Cause**: Configuration cache not cleared
**Solution**:
```bash
php artisan config:clear
php artisan config:cache
```

#### 2. Webhook Route Not Found (HTTP 404)
**Cause**: Routes not properly registered
**Solution**:
```bash
php artisan route:clear
php artisan route:list | grep webhook
```

#### 3. Signature Verification Failing
**Cause**: Incorrect T-Bank credentials or signature algorithm
**Solution**: Check T-Bank terminal key and password configuration

#### 4. Payment Not Found in Webhook
**Cause**: External payment ID mismatch
**Solution**: Verify payment creation stores correct external_payment_id

### Debug Information
```php
// Enable detailed webhook logging
Log::info('Webhook received', [
    'gateway' => 'tbank',
    'ip' => $request->ip(),
    'payload' => $request->getContent(),
    'headers' => $request->headers->all(),
]);
```

## Benefits

### 1. ✅ Enables Webhook Processing
- T-Bank webhooks can now reach the application
- Payment status updates work correctly
- VPN subscriptions activate automatically

### 2. ✅ Maintains Security
- Only webhook routes excluded from CSRF
- Alternative security measures in place
- Signature verification and IP whitelisting active

### 3. ✅ Improves Debugging
- Enhanced logging for webhook processing
- Detailed payment status change tracking
- Better error reporting and troubleshooting

### 4. ✅ Production Ready
- Comprehensive test coverage
- Proper configuration management
- Security best practices followed

## Conclusion

The webhook CSRF fix resolves the payment status update issue by properly excluding webhook routes from CSRF protection while maintaining security through alternative measures. This enables T-Bank webhook notifications to successfully reach the application, allowing Payment and Order records to be automatically marked as paid when payments are completed, thus completing the VPN subscription activation flow.
