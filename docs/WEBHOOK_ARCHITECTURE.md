# Webhook Processing Architecture

This document describes the refactored webhook processing architecture that provides clean separation of concerns and extensibility for multiple payment gateways.

## Overview

The webhook system has been refactored to move gateway-specific logic into individual gateway classes, making the system more maintainable and extensible.

## Architecture Components

### 1. WebhookResult Class

A standardized result object that all gateways return from their `handleWebhook()` method:

```php
class WebhookResult
{
    public function __construct(
        public readonly bool $success,
        public readonly ?string $paymentId = null,
        public readonly ?string $status = null,
        public readonly array $additionalData = [],
        public readonly ?string $message = null,
        public readonly ?string $errorCode = null
    ) {}
}
```

#### Factory Methods

- `WebhookResult::success()` - For successful webhook processing with payment updates
- `WebhookResult::failure()` - For webhook processing errors
- `WebhookResult::ignored()` - For webhooks that don't require payment updates

### 2. Gateway Interface Updates

The `PaymentGatewayInterface::handleWebhook()` method now returns `WebhookResult`:

```php
public function handleWebhook(array $data): WebhookResult;
```

### 3. Gateway-Specific Webhook Processing

Each gateway handles its own webhook format and status mapping:

#### T-Bank Gateway
```php
public function handleWebhook(array $data): WebhookResult
{
    // Validate T-Bank webhook structure
    if (!isset($data['PaymentId']) || !isset($data['Status'])) {
        return WebhookResult::failure('Invalid webhook data');
    }

    // Map T-Bank status to system status
    $mappedStatus = $this->mapTBankStatus($data['Status']);

    // Return standardized result
    return WebhookResult::success(
        paymentId: (string) $data['PaymentId'],
        status: $mappedStatus,
        additionalData: [
            'webhook_data' => $data,
            'tbank_status' => $data['Status'],
            // ... other T-Bank specific data
        ]
    );
}
```

#### Stripe Gateway (Example)
```php
public function handleWebhook(array $data): WebhookResult
{
    // Validate Stripe webhook structure
    if (!isset($data['type']) || !isset($data['data']['object']['id'])) {
        return WebhookResult::failure('Invalid webhook data');
    }

    // Map Stripe event to system status
    $mappedStatus = $this->mapStripeEventToStatus($data['type']);

    if (!$mappedStatus) {
        return WebhookResult::ignored('Event does not require payment update');
    }

    return WebhookResult::success(
        paymentId: $data['data']['object']['id'],
        status: $mappedStatus,
        additionalData: [
            'webhook_data' => $data,
            'stripe_event_type' => $data['type'],
            // ... other Stripe specific data
        ]
    );
}
```

### 4. PaymentService Orchestration

The `PaymentService` now only handles orchestration and database operations:

```php
public function handleWebhook(string $gatewayCode, array $data, array $headers = []): bool
{
    // Log webhook
    $this->logWebhook($gatewayCode, $data, $headers);

    // Get gateway and process webhook
    $gateway = $this->getGateway($gatewayCode);
    $webhookResult = $gateway->handleWebhook($data);

    if ($webhookResult->isSuccess() && $webhookResult->hasPaymentUpdate()) {
        // Update payment using standardized result
        $this->processWebhookPaymentUpdate($webhookResult);
    }

    return $webhookResult->isSuccess();
}
```

## Benefits of the Refactored Architecture

### 1. Separation of Concerns
- **Gateways**: Handle their own webhook formats and status mapping
- **PaymentService**: Handles database operations and orchestration
- **WebhookResult**: Provides standardized communication between layers

### 2. Extensibility
- Adding new gateways requires no changes to PaymentService
- Each gateway can handle its unique webhook format
- Status mapping is encapsulated within each gateway

### 3. Maintainability
- Gateway-specific logic is contained within gateway classes
- No conditional logic based on gateway type in PaymentService
- Clear separation makes testing easier

### 4. Consistency
- All gateways return the same WebhookResult format
- Standardized status values across all gateways
- Consistent error handling and logging

## Status Mapping

All gateways map their specific statuses to our standardized system statuses:

- `pending` - Payment created, awaiting completion
- `paid` - Payment successfully completed
- `failed` - Payment failed
- `cancelled` - Payment cancelled
- `refunded` - Payment refunded
- `disputed` - Payment disputed (chargebacks, etc.)

## Adding New Payment Gateways

### Step 1: Create Gateway Class
```php
class NewGateway extends AbstractPaymentGateway
{
    public function handleWebhook(array $data): WebhookResult
    {
        // 1. Validate webhook structure
        // 2. Extract payment ID and status
        // 3. Map gateway status to system status
        // 4. Return WebhookResult with standardized data
    }

    private function mapGatewayStatusToSystem(string $gatewayStatus): string
    {
        // Map gateway-specific statuses to system statuses
    }
}
```

### Step 2: Register Gateway
Add the gateway to PaymentService's `createGateway()` method:

```php
private function createGateway(string $code, array $config): ?PaymentGatewayInterface
{
    $gateway = match ($code) {
        'tbank' => new TBankGateway(),
        'stripe' => new StripeGateway(),
        'new_gateway' => new NewGateway(), // Add here
        // ...
        default => null,
    };
    
    // ...
}
```

### Step 3: Configure Gateway
Add configuration in `config/payments.php`:

```php
'gateways' => [
    'new_gateway' => [
        'driver' => 'new_gateway',
        'name' => 'New Gateway',
        'enabled' => env('NEW_GATEWAY_ENABLED', false),
        'supports_webhooks' => true,
        'config' => [
            'api_key' => env('NEW_GATEWAY_API_KEY'),
            // ... other config
        ],
    ],
]
```

## Webhook Data Storage

Each gateway can store its specific data in the `external_details` field:

```json
{
    "created_at": "2025-07-16T12:25:57Z",
    "payment_url": "https://gateway.com/payment/...",
    "webhook_data": { /* raw webhook data */ },
    "webhook_received_at": "2025-07-16T12:25:57Z",
    "gateway_specific_field": "value",
    "card_details": { /* if available */ }
}
```

## Error Handling

### Gateway-Level Errors
```php
return WebhookResult::failure(
    'Specific error message',
    'ERROR_CODE',
    ['debug_data' => $data]
);
```

### Service-Level Errors
The PaymentService catches exceptions and logs them appropriately while returning false to indicate webhook processing failure.

## Testing

### Unit Testing Gateways
```php
public function test_webhook_processing()
{
    $gateway = new TBankGateway();
    $webhookData = ['PaymentId' => '123', 'Status' => 'CONFIRMED'];
    
    $result = $gateway->handleWebhook($webhookData);
    
    $this->assertTrue($result->isSuccess());
    $this->assertEquals('paid', $result->status);
    $this->assertEquals('123', $result->paymentId);
}
```

### Integration Testing
```php
public function test_webhook_end_to_end()
{
    // Create payment
    // Send webhook
    // Verify payment status updated
    // Verify external_details populated
}
```

## Migration from Old Architecture

The refactoring maintains backward compatibility:

1. Existing webhook endpoints continue to work
2. Database schema remains unchanged
3. External integrations are unaffected
4. Only internal processing logic has been improved

## Best Practices

1. **Validate webhook data** before processing
2. **Map statuses consistently** across gateways
3. **Store raw webhook data** for debugging
4. **Use appropriate WebhookResult types** (success/failure/ignored)
5. **Log gateway-specific information** for troubleshooting
6. **Handle edge cases** gracefully
7. **Test webhook processing** thoroughly

This architecture provides a solid foundation for supporting multiple payment gateways while maintaining clean, maintainable code.
