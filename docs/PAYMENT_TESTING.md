# Payment Gateway Testing Guide

This document explains how to test payment gateways using the `PaymentTestCommand`.

## Overview

The `PaymentTestCommand` provides a comprehensive testing framework for payment gateways, including:
- Configuration validation
- Gateway availability checks
- Connectivity testing
- Payment request validation

## Usage

### Test All Gateways
```bash
php artisan payment:test --all
```

### Test Specific Gateway
```bash
php artisan payment:test manual
php artisan payment:test tbank
php artisan payment:test cash
php artisan payment:test free
```

### Test with Custom Parameters
```bash
php artisan payment:test manual --amount=50000 --currency=USD
```

### Test with Debug Output
```bash
php artisan payment:test free --debug
php artisan payment:test --all --debug
```

## Command Options

- `gateway` - Specific gateway to test (optional)
- `--all` - Test all available gateways
- `--amount=100` - Test payment amount in kopecks (default: 100)
- `--currency=RUB` - Test payment currency (default: RUB)
- `--debug` - Show debug output during testing

## Test Phases

### 1. Configuration Validation
Verifies that the gateway configuration is valid and the gateway can be instantiated.

### 2. Gateway Availability
Checks if the gateway reports itself as available for processing payments.

### 3. Connectivity Test
Tests the gateway's connectivity and basic functionality.

### 4. Payment Validation
Creates a test `PaymentRequest` and validates it against the gateway's requirements.

**Gateway-Specific Validation:**
- **FreeGateway**: Automatically uses amount=0 (required for free payments)
- **Other Gateways**: Use the specified --amount option (default: 100 kopecks)
- **Configuration Errors**: Distinguished from validation failures (e.g., missing T-Bank terminal_key)

## Test Data Creation

The command automatically handles test data creation:

### Test User Creation
- First attempts to use an existing user from the database
- If no users exist, creates a test user with email `<EMAIL>`
- If that fails (e.g., due to unique constraints), tries with a timestamped email
- Includes all required user fields: `name`, `email`, `password`, `email_verified_at`, `is_active`, `registered_at`

### Test Order Creation
- Creates a temporary `Order` instance with all required fields
- Sets proper UUID for `id` and generates a `public_id`
- Links the order to the test user
- Uses the specified amount and currency from command options
- Marks the order as not persisted to avoid database operations during validation

## Error Handling

The command includes comprehensive error handling:

### User Creation Failures
- Handles unique constraint violations
- Attempts alternative email addresses
- Gracefully degrades if user creation is impossible

### Order Creation Issues
- Provides proper default values for all required fields
- Handles missing relationships
- Prevents accidental database saves during testing

### Gateway Validation Errors
- Captures and reports validation errors from gateways
- Provides detailed error messages for debugging
- Continues testing other gateways even if one fails

## Example Output

### Successful Gateway Test
```
Payment Gateway Test Tool

Testing gateway: manual
✓ Configuration valid
✓ Gateway available
✓ Connectivity test passed
✓ Validation test passed

✓ Gateway manual test completed successfully!
```

### Gateway with Configuration Incomplete
```
Testing gateway: tbank
✓ Configuration valid
✓ Gateway available
✓ Connectivity test passed
⚠ Configuration incomplete (not a validation failure)

⚠ Gateway tbank test completed with configuration incomplete!
  Note: Gateway functionality is working, but some configuration is missing.

Configuration Notes:
  - Configuration incomplete: T-Bank gateway requires terminal_key
```

### All Gateways Test Results
```
Test Results Summary:

+----------+--------+-----------+--------------+------------+---------+
| Gateway  | Config | Available | Connectivity | Validation | Overall |
+----------+--------+-----------+--------------+------------+---------+
| manual   | ✓      | ✓         | ✓            | ✓          | ✓       |
| cash     | ✓      | ✓         | ✓            | ✓          | ✓       |
| free     | ✓      | ✓         | ✓            | ✓          | ✓       |
| tbank    | ✓      | ✓         | ✓            | ⚠          | ✓       |
+----------+--------+-----------+--------------+------------+---------+

Legend: ✓ = Success, ✗ = Failed, ⚠ = Configuration Incomplete
```

## Troubleshooting

### "No test user available" Error
This occurs when:
- No users exist in the database
- User creation fails due to validation rules
- Database permissions prevent user creation

**Solution**: Create a user manually or check database permissions.

### "PaymentRequest validation failed" Error
This occurs when:
- Required fields are missing from the test data
- Gateway-specific validation rules fail
- Currency or amount format is invalid

**Solution**: Check gateway configuration and validation rules.

### "Gateway not available" Error
This occurs when:
- Gateway configuration is incomplete
- Required services are not running
- Network connectivity issues

**Solution**: Verify gateway configuration and dependencies.

### "T-Bank gateway requires terminal_key" Error
This occurs when:
- `TBANK_TERMINAL_KEY` environment variable is not set
- Configuration cache contains old values
- Environment file is not being loaded properly

**Solutions**:
1. Verify `.env` file contains: `TBANK_TERMINAL_KEY=your_terminal_key`
2. Clear configuration cache: `php artisan config:clear`
3. Check configuration loading: `php artisan config:show payments.gateways.tbank`
4. Test with debug output: `php artisan payment:test tbank --debug`

## Integration with Payment System

The test command integrates with the payment system components:

### PaymentGatewayManager
- Uses the same gateway manager as the production system
- Tests actual gateway instances and configurations
- Validates real gateway behavior

### PaymentRequest DTO
- Creates valid `PaymentRequest` instances
- Tests the same validation logic used in production
- Ensures compatibility with gateway interfaces

### Gateway Implementations
- Tests each gateway's `validatePaymentRequest()` method
- Verifies gateway availability and configuration
- Validates gateway-specific requirements

## Best Practices

### Regular Testing
- Run tests after configuration changes
- Test all gateways before deployment
- Include testing in CI/CD pipelines

### Test Data Management
- The command creates temporary test data that doesn't persist
- Test users may be created in the database for validation
- Clean up test data periodically if needed

### Environment Considerations
- Test in environments that mirror production
- Verify network connectivity to payment providers
- Use appropriate test credentials for external gateways

## Security Considerations

### Test User Creation
- Test users are created with secure passwords
- Email addresses use the `@example.com` domain
- Users are marked as active for testing purposes

### Data Isolation
- Test orders are not persisted to the database
- Payment requests use test data only
- No real payment processing occurs during testing

### Credential Management
- Use test credentials for external gateways
- Ensure test mode is enabled for payment providers
- Never use production credentials in testing
