<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\PropertyInfo;

/**
 * Guesses the property's human readable description.
 *
 * <AUTHOR> <<EMAIL>>
 */
interface PropertyDescriptionExtractorInterface
{
    /**
     * Gets the short description of the property.
     */
    public function getShortDescription(string $class, string $property, array $context = []): ?string;

    /**
     * Gets the long description of the property.
     */
    public function getLongDescription(string $class, string $property, array $context = []): ?string;
}
