{"name": "symfony/property-info", "type": "library", "description": "Extracts information about PHP class' properties using metadata of popular sources", "keywords": ["property", "type", "phpdoc", "symfony", "validator", "doctrine"], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/string": "^6.4|^7.0", "symfony/type-info": "~7.2.8|^7.3.1"}, "require-dev": {"symfony/serializer": "^6.4|^7.0", "symfony/cache": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "phpdocumentor/reflection-docblock": "^5.2", "phpstan/phpdoc-parser": "^1.0|^2.0"}, "conflict": {"phpdocumentor/reflection-docblock": "<5.2", "phpdocumentor/type-resolver": "<1.5.1", "symfony/dependency-injection": "<6.4", "symfony/cache": "<6.4", "symfony/serializer": "<6.4"}, "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "minimum-stability": "dev"}