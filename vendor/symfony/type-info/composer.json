{"name": "symfony/type-info", "type": "library", "description": "Extracts PHP types information.", "keywords": ["type", "phpdoc", "phpstan", "symfony"], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Baptiste LEDUC", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=8.2", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "require-dev": {"phpstan/phpdoc-parser": "^1.30|^2.0"}, "conflict": {"phpstan/phpdoc-parser": "<1.30"}, "autoload": {"psr-4": {"Symfony\\Component\\TypeInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "minimum-stability": "dev"}