<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\TypeInfo\Type;

use Symfony\Component\TypeInfo\Type;

/**
 * Represents a type composed of several other types.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @template T of Type
 */
interface CompositeTypeInterface
{
    /**
     * @return list<T>
     */
    public function getTypes(): array;

    /**
     * @param callable(Type): bool $specification
     */
    public function composedTypesAreSatisfiedBy(callable $specification): bool;
}
