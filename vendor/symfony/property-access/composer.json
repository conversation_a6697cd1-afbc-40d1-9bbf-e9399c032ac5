{"name": "symfony/property-access", "type": "library", "description": "Provides functions to read and write from/to an object or array using a simple string notation", "keywords": ["property", "index", "access", "object", "array", "extraction", "injection", "reflection", "property-path"], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/property-info": "^5.4|^6.0|^7.0"}, "require-dev": {"symfony/cache": "^5.4|^6.0|^7.0"}, "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "minimum-stability": "dev"}