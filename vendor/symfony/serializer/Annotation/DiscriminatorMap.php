<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Serializer\Annotation;

class_exists(\Symfony\Component\Serializer\Attribute\DiscriminatorMap::class);

if (false) {
    #[\Attribute(\Attribute::TARGET_CLASS)]
    class DiscriminatorMap extends \Symfony\Component\Serializer\Attribute\DiscriminatorMap
    {
    }
}
