<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Serializer\Attribute;

use Symfony\Component\Serializer\Exception\InvalidArgumentException;

/**
 * <AUTHOR> <<EMAIL>>
 */
#[\Attribute(\Attribute::TARGET_METHOD | \Attribute::TARGET_PROPERTY)]
class MaxDepth
{
    /**
     * @param int $maxDepth The maximum serialization depth
     */
    public function __construct(private readonly int $maxDepth)
    {
        if ($maxDepth <= 0) {
            throw new InvalidArgumentException(\sprintf('Parameter given to "%s" must be a positive integer.', static::class));
        }
    }

    public function getMaxDepth(): int
    {
        return $this->maxDepth;
    }
}

if (!class_exists(\Symfony\Component\Serializer\Annotation\MaxDepth::class, false)) {
    class_alias(MaxDepth::class, \Symfony\Component\Serializer\Annotation\MaxDepth::class);
}
