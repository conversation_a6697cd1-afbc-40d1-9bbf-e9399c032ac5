<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Serializer\Extractor;

use Symfony\Component\PropertyInfo\PropertyListExtractorInterface;

/**
 * <AUTHOR> <<EMAIL>>
 */
final class ObjectPropertyListExtractor implements ObjectPropertyListExtractorInterface
{
    private \Closure $objectClassResolver;

    public function __construct(
        private PropertyListExtractorInterface $propertyListExtractor,
        ?callable $objectClassResolver = null,
    ) {
        $this->objectClassResolver = ($objectClassResolver ?? 'get_class')(...);
    }

    public function getProperties(object $object, array $context = []): ?array
    {
        $class = ($this->objectClassResolver)($object);

        return $this->propertyListExtractor->getProperties($class, $context);
    }
}
