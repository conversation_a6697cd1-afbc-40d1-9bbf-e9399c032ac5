{"name": "laravel/pint", "description": "An opinionated code formatter for PHP.", "keywords": ["php", "format", "formatter", "lint", "linter"], "homepage": "https://laravel.com", "type": "project", "license": "MIT", "support": {"issues": "https://github.com/laravel/pint/issues", "source": "https://github.com/laravel/pint"}, "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2.0", "ext-json": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "ext-xml": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.82.2", "illuminate/view": "^11.45.1", "larastan/larastan": "^3.5.0", "laravel-zero/framework": "^11.45.0", "mockery/mockery": "^1.6.12", "nunomaduro/termwind": "^2.3.1", "pestphp/pest": "^2.36.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["overrides/Runner/Parallel/ProcessFactory.php"]}, "autoload-dev": {"psr-4": {"Scripts\\": "scripts/", "Tests\\": "tests/"}}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "platform": {"php": "8.2.0"}, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true, "bin": ["builds/pint"]}