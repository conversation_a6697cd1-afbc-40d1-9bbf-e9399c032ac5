{"name": "pavelda<PERSON>lin/rest-client", "description": "REST client for PHP 7.4+", "keywords": ["rest-client", "json-rest-client", "interceptor", "psr-7", "psr-18", "php"], "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"RestClient\\": "src/"}, "files": ["src/functions.php"]}, "autoload-dev": {"psr-4": {"RestClient\\Tests\\": "tests"}}, "require": {"php": ">=8.1", "ext-json": "*", "psr/http-client": "1.0.*", "psr/log": "1.1.* || ^2 || ^3", "guzzlehttp/guzzle": "6.*|7.*", "symfony/serializer": "6.4.* || ^7", "symfony/property-access": "6.4.*"}, "require-dev": {"phpstan/phpstan": "1.10.*", "phpunit/phpunit": "10.5.*"}, "scripts": {"test": ["php -d memory_limit=4G ./vendor/bin/phpstan analyse -c phpstan.neon", "php ./vendor/bin/phpunit"]}}