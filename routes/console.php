<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;
use App\Jobs\DetectOfflineUsersJob;
use App\Jobs\SyncUserOnlineStatusJob;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Schedule XUI server synchronization tasks (data only, no user status)
Schedule::command('xui:sync-servers --async --batch-size=5 --ignore-confirmation')
    ->everyTenMinutes()
    ->withoutOverlapping()
    ->runInBackground()
    ->description('Sync XUI servers data asynchronously');

// Schedule user online status synchronization (more frequent)
Schedule::command('xui:sync-user-status --async --batch-size=10 --ignore-confirmation')
    ->everyThirtySeconds()
    ->withoutOverlapping()
    ->runInBackground()
    ->description('Sync user online status from XUI servers');

// Schedule offline user detection (background cleanup)
Schedule::job(new DetectOfflineUsersJob)
    ->everyFiveMinutes()
    ->withoutOverlapping()
    ->description('Detect and log offline users');

// Optional: Schedule a full sync during low traffic hours
Schedule::command('xui:sync-servers --force --batch-size=10 --ignore-confirmation')
    ->dailyAt('03:00')
    ->withoutOverlapping()
    ->description('Full XUI server sync during maintenance window');
