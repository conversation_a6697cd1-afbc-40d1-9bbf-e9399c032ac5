<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;
use App\Jobs\DetectOfflineUsersJob;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Schedule XUI server synchronization tasks
Schedule::command('xui:sync-servers --async --batch-size=5')
    ->everyTenMinutes()
    ->withoutOverlapping()
    ->runInBackground()
    ->description('Sync XUI servers data asynchronously');

// Schedule offline user detection
Schedule::job(new DetectOfflineUsersJob)
    ->everyFiveMinutes()
    ->withoutOverlapping()
    ->description('Detect and log offline users');

// Optional: Schedule a full sync during low traffic hours
Schedule::command('xui:sync-servers --force --batch-size=10')
    ->dailyAt('03:00')
    ->withoutOverlapping()
    ->description('Full XUI server sync during maintenance window');
