<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\WebhookController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

/*
|--------------------------------------------------------------------------
| Webhook Routes
|--------------------------------------------------------------------------
|
| These routes handle incoming webhooks from payment gateways and other
| external services. They are designed to work properly with proxy
| configurations and IP-based security.
|
*/

// Payment webhook routes
Route::prefix('webhooks')->name('webhooks.')->group(function () {
    // T-Bank webhook endpoint
    Route::post('/tbank', [WebhookController::class, 'tbank'])
        ->name('tbank')
        ->middleware(['throttle:webhook']);

    // Generic webhook handler for any gateway
    Route::post('/{gateway}', [WebhookController::class, 'handle'])
        ->name('handle')
        ->where('gateway', '[a-zA-Z0-9_-]+')
        ->middleware(['throttle:webhook']);

    // Webhook management endpoints (admin only)
    Route::middleware(['auth:sanctum', 'admin'])->group(function () {
        Route::get('/statistics', [WebhookController::class, 'statistics'])
            ->name('statistics');
        
        Route::post('/retry-failed', [WebhookController::class, 'retryFailed'])
            ->name('retry-failed');
        
        Route::get('/activity', [WebhookController::class, 'activity'])
            ->name('activity');
        
        Route::post('/cleanup', [WebhookController::class, 'cleanup'])
            ->name('cleanup');
    });
});

/*
|--------------------------------------------------------------------------
| Proxy Test Routes
|--------------------------------------------------------------------------
|
| These routes are used for testing proxy configuration and IP detection.
| They should only be available in development environments.
|
*/

if (app()->environment(['local', 'testing'])) {
    Route::prefix('proxy-test')->group(function () {
        // Test endpoint for proxy configuration
        Route::get('/info', function (Request $request) {
            return response()->json([
                'proxy_config' => [
                    'trusted_proxies' => getTrustedProxies(),
                    'trusted_hosts' => is_callable(getTrustedHosts()) ? getTrustedHosts()() : getTrustedHosts(),
                    'environment' => app()->environment(),
                ],
                'request_info' => [
                    'client_ip' => $request->ip(),
                    'is_secure' => $request->isSecure(),
                    'host' => $request->header('Host'),
                    'method' => $request->method(),
                    'url' => $request->url(),
                    'full_url' => $request->fullUrl(),
                ],
                'all_headers' => $request->headers->all(),
                'server_vars' => [
                    'REMOTE_ADDR' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                    'HTTP_X_FORWARDED_FOR' => $_SERVER['HTTP_X_FORWARDED_FOR'] ?? null,
                    'HTTP_X_REAL_IP' => $_SERVER['HTTP_X_REAL_IP'] ?? null,
                    'SERVER_NAME' => $_SERVER['SERVER_NAME'] ?? 'unknown',
                    'SERVER_PORT' => $_SERVER['SERVER_PORT'] ?? 'unknown',
                    'HTTPS' => $_SERVER['HTTPS'] ?? 'off',
                ],
            ]);
        });

        // Simulate webhook endpoint for testing
        Route::post('/webhook/{gateway?}', function (Request $request, $gateway = 'test') {
            return response()->json([
                'message' => 'Test webhook received',
                'gateway' => $gateway,
                'client_ip' => $request->ip(),
                'headers' => $request->headers->all(),
                'payload' => $request->all(),
                'timestamp' => now()->toISOString(),
            ]);
        });
    });
}

/*
|--------------------------------------------------------------------------
| Health Check Routes
|--------------------------------------------------------------------------
|
| Simple health check endpoints that can be used by load balancers
| and monitoring systems.
|
*/

Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toISOString(),
        'environment' => app()->environment(),
    ]);
});

Route::get('/health/detailed', function (Request $request) {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toISOString(),
        'environment' => app()->environment(),
        'client_ip' => $request->ip(),
        'is_secure' => $request->isSecure(),
        'host' => $request->header('Host'),
        'database' => [
            'connected' => true, // You can add actual DB health check here
        ],
        'cache' => [
            'connected' => true, // You can add actual cache health check here
        ],
    ]);
});
