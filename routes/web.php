<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return 'It works ' . now()->format('c');
});

// VPN Subscription Routes
Route::prefix('access')->group(function () {
    // Display available subscription plans + payment methods
    Route::get('/{uuid}/plan/select', [App\Http\Controllers\PlanSelectionController::class, 'show'])
        ->whereUuid('uuid')
        ->name('vpn.plan.selection');

    // Process plan selection and payment method → generate payment
    Route::post('/{uuid}/plan/purchase', [App\Http\Controllers\VpnPurchaseController::class, 'purchase'])
        ->whereUuid('uuid')
        ->name('vpn.plan.purchase');

    // Payment success/failure routes
    Route::get('/{uuid}/plan/success/{order}', function (string $uuid, string $order) {
        return view('vpn.payment-success', compact('uuid', 'order'));
    })->whereUuid('uuid')->name('vpn.plan.success');

    Route::get('/{uuid}/plan/failure/{order}', function (string $uuid, string $order) {
        return view('vpn.payment-failure', compact('uuid', 'order'));
    })->whereUuid('uuid')->name('vpn.plan.failure');
});

// Payment result routes
Route::prefix('orders')->group(function () {
    Route::get('/{order}/success', function (string $order) {
        return view('payment.success', compact('order'));
    })->name('payment.success');

    Route::get('/{order}/failed', function (string $order) {
        return view('payment.failed', compact('order'));
    })->name('payment.failed');
});

// Payment Webhook Routes (no CSRF protection)
Route::prefix('webhook')->group(function () {
    Route::post('/tbank', [App\Http\Controllers\WebhookController::class, 'tbank'])
        ->name('webhook.tbank');

    Route::post('/{gateway}', [App\Http\Controllers\WebhookController::class, 'handle'])
        ->name('webhook.handle');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

