<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return response()->json([
        'message' => 'SmartVPN Service is running',
        'timestamp' => now()->format('c'),
        'environment' => app()->environment(),
        'debug_info' => config('proxy.debug.show_ip_info', false) ? [
            'client_ip' => request()->ip(),
            'is_secure' => request()->isSecure(),
            'host' => request()->header('Host'),
            'user_agent' => request()->userAgent(),
        ] : null,
    ]);
});

// Proxy configuration test endpoint (only in local environment)
Route::get('/proxy-test', function () {
    if (!app()->environment('local')) {
        abort(404);
    }

    return response()->json([
        'proxy_configuration' => [
            'trusted_proxies' => \App\Support\ProxyHelper::getTrustedProxies(),
            'trusted_hosts' => is_callable(\App\Support\ProxyHelper::getTrustedHosts()) ? \App\Support\ProxyHelper::getTrustedHosts()() : \App\Support\ProxyHelper::getTrustedHosts(),
            'environment' => app()->environment(),
        ],
        'request_info' => [
            'client_ip' => request()->ip(),
            'is_secure' => request()->isSecure(),
            'host' => request()->header('Host'),
            'method' => request()->method(),
            'url' => request()->url(),
            'full_url' => request()->fullUrl(),
        ],
        'proxy_headers' => [
            'X-Forwarded-For' => request()->header('X-Forwarded-For'),
            'X-Forwarded-Host' => request()->header('X-Forwarded-Host'),
            'X-Forwarded-Port' => request()->header('X-Forwarded-Port'),
            'X-Forwarded-Proto' => request()->header('X-Forwarded-Proto'),
            'X-Real-IP' => request()->header('X-Real-IP'),
            'CF-Connecting-IP' => request()->header('CF-Connecting-IP'),
            'Forwarded' => request()->header('Forwarded'),
        ],
        'server_info' => [
            'server_name' => $_SERVER['SERVER_NAME'] ?? 'unknown',
            'server_addr' => $_SERVER['SERVER_ADDR'] ?? 'unknown',
            'server_port' => $_SERVER['SERVER_PORT'] ?? 'unknown',
            'https' => $_SERVER['HTTPS'] ?? 'off',
        ],
    ]);
});
