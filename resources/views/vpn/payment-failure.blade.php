<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Failed</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            max-width: 600px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
        }
        .error-icon {
            width: 80px;
            height: 80px;
            background: #f44336;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            color: white;
            font-size: 40px;
        }
        h1 {
            color: #f44336;
            margin-bottom: 20px;
        }
        .order-info {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 20px;
            margin: 30px 0;
            text-align: left;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e0e0e0;
        }
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .info-label {
            font-weight: bold;
            color: #666;
        }
        .info-value {
            color: #333;
        }
        .troubleshooting {
            background: #fff3e0;
            border: 1px solid #ff9800;
            border-radius: 6px;
            padding: 20px;
            margin: 30px 0;
            text-align: left;
        }
        .troubleshooting h3 {
            margin-top: 0;
            color: #f57c00;
        }
        .troubleshooting ul {
            margin: 0;
            padding-left: 20px;
        }
        .troubleshooting li {
            margin-bottom: 8px;
        }
        .button {
            display: inline-block;
            background: #2196f3;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: bold;
            margin: 10px;
            transition: background 0.3s ease;
        }
        .button:hover {
            background: #1976d2;
        }
        .button.primary {
            background: #4caf50;
        }
        .button.primary:hover {
            background: #388e3c;
        }
        .button.secondary {
            background: #6c757d;
        }
        .button.secondary:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-icon">✗</div>
        
        <h1>Payment Failed</h1>
        <p>We're sorry, but your payment could not be processed at this time.</p>

        <div class="order-info">
            <h3>Transaction Details</h3>
            <div class="info-row">
                <span class="info-label">Order ID:</span>
                <span class="info-value">{{ $order }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">User ID:</span>
                <span class="info-value">{{ $uuid }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Attempt Date:</span>
                <span class="info-value">{{ now()->format('M j, Y \a\t g:i A') }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Status:</span>
                <span class="info-value" style="color: #f44336; font-weight: bold;">Failed</span>
            </div>
        </div>

        <div class="troubleshooting">
            <h3>Common Solutions</h3>
            <ul>
                <li>Check that your payment information is correct</li>
                <li>Ensure you have sufficient funds in your account</li>
                <li>Verify that your card is not expired</li>
                <li>Try using a different payment method</li>
                <li>Contact your bank if the issue persists</li>
                <li>Clear your browser cache and try again</li>
            </ul>
        </div>

        @if(session('error'))
            <div style="background: #ffebee; border: 1px solid #f44336; color: #d32f2f; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <strong>Error Details:</strong><br>
                {{ session('error') }}
            </div>
        @endif

        <div style="margin-top: 30px;">
            <a href="{{ route('vpn.plan.selection', $uuid) }}" class="button primary">
                Try Again
            </a>
            <a href="#" class="button secondary" onclick="window.close(); return false;">
                Close Window
            </a>
        </div>

        <div style="margin-top: 30px; font-size: 14px; color: #666;">
            <p>
                <strong>Still Having Issues?</strong><br>
                If you continue to experience problems with your payment, please contact our support team. 
                We're here to help you get your VPN subscription set up quickly and securely.
            </p>
            <p>
                <strong>Alternative Payment Methods:</strong><br>
                You can also try selecting a different payment method from our available options 
                when you retry your purchase.
            </p>
        </div>
    </div>
</body>
</html>
