<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Failed</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            text-align: center;
            background-color: #f8f9fa;
        }
        .failed-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .failed-icon {
            font-size: 64px;
            color: #dc3545;
            margin-bottom: 20px;
        }
        .order-id {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 20px 0;
        }
        .retry-btn {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .home-btn {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="failed-container">
        <div class="failed-icon">❌</div>
        <h1>Payment Failed</h1>
        <p>Unfortunately, your payment could not be processed.</p>
        
        <div class="order-id">
            Order ID: {{ $order }}
        </div>
        
        <p>Please try again or contact support if the problem persists.</p>
        
        <div>
            <a href="#" class="retry-btn" onclick="history.back()">
                Try Again
            </a>
            <a href="/" class="home-btn">
                Return to Home
            </a>
        </div>
    </div>
</body>
</html>
