# XUI Server Synchronization Services

## Overview

The XUI Server Synchronization system consists of two independent services that handle different aspects of server monitoring:

1. **XuiServerSyncService** - Handles server data synchronization (status, settings, inbounds, traffic)
2. **UserOnlineStatusSyncService** - Handles user online/offline status tracking

This separation allows for different synchronization frequencies and better performance optimization.

## Architecture

### Server Data Synchronization Components

1. **XuiServerSyncService** - Main server data synchronization service
2. **SyncXuiServerJob** - Async job for individual server data sync
3. **SyncXuiServers** - Artisan command for manual server data sync
4. **TestXuiSync** - Testing command for server data sync

### User Status Synchronization Components

1. **UserOnlineStatusSyncService** - User online status tracking service
2. **SyncUserOnlineStatusJob** - Async job for individual server user status sync
3. **SyncUserOnlineStatus** - Artisan command for manual user status sync
4. **DetectOfflineUsersJob** - Background job for offline user detection
5. **TestUserStatusSync** - Testing command for user status sync

### Configuration

Configuration is managed through `config/xui_sync.php`:

```php
// Key configuration options
'default_batch_size' => 10,
'online_threshold_seconds' => 20,
'offline_threshold_seconds' => 15,
'sync_timeout_seconds' => 300,
```

## Usage

### Server Data Synchronization

```bash
# Sync server data for all eligible servers synchronously
php artisan xui:sync-servers --ignore-confirmation

# Sync server data asynchronously with custom batch size
php artisan xui:sync-servers --async --batch-size=5 --ignore-confirmation

# Force sync all servers (ignore auto_sync setting)
php artisan xui:sync-servers --force --ignore-confirmation

# Sync specific servers
php artisan xui:sync-servers --server-ids=1,2,3 --ignore-confirmation
```

### User Status Synchronization

```bash
# Sync user status for all eligible servers synchronously
php artisan xui:sync-user-status --ignore-confirmation

# Sync user status asynchronously with custom batch size
php artisan xui:sync-user-status --async --batch-size=10 --ignore-confirmation

# Force sync user status for all servers
php artisan xui:sync-user-status --force --ignore-confirmation

# Sync user status for specific servers
php artisan xui:sync-user-status --server-ids=1,2,3 --ignore-confirmation
```

### Testing

```bash
# Test server data sync for specific server
php artisan test:xui-sync 1

# Test server data sync for all eligible servers
php artisan test:xui-sync

# Test user status sync for specific server
php artisan test:user-status-sync 1

# Test user status sync for all eligible servers
php artisan test:user-status-sync
```

### Scheduled Tasks

The services automatically run scheduled tasks with different frequencies:

**Server Data Synchronization:**

- **Every 10 minutes**: Async server data sync (batch size 5)
- **Daily at 3:00 AM**: Full server data sync (maintenance window)

**User Status Synchronization:**

- **Every 30 seconds**: Async user status sync (batch size 10)
- **Every 5 minutes**: Background offline user detection

## Data Synchronization

### Raw Data Fields

The service retrieves and stores raw JSON responses:

- `raw_server_status` → Server CPU, memory, disk usage
- `raw_settings_all` → Server configuration settings
- `raw_inbounds_list` → Inbound configurations and client stats
- `raw_clients_online` → Currently connected clients

Each field has a corresponding `*_updated_at` timestamp.

### Computed Fields

Calculated from raw data:

- `server_load` - Percentage (max of CPU/memory usage)
- `clients_count` - Total configured clients
- `clients_online_count` - Currently connected clients
- `clients_online_list` - JSON array of online client emails

### Sync Timestamp

- `last_sync_at` - Set after successful synchronization

## User Online Status Tracking

### Real-time Updates

During each sync, the service:

1. **Identifies online users** from server client data
2. **Updates `last_online_at`** for users who were offline >20 seconds
3. **Creates online logs** for status changes
4. **Detects offline users** who haven't been seen >15 seconds
5. **Creates offline logs** for status changes

### Background Detection

A scheduled job runs every 5 minutes to detect users who went offline between syncs.

### User Online Logs

The `user_online_logs` table tracks status changes:

```php
UserOnlineLog::create([
    'user_id' => $user->id,
    'xui_server_id' => $server->id,
    'status' => 'online', // or 'offline'
    'status_timestamp' => $timestamp,
]);
```

## Traffic Statistics

### Client Traffic Data

For each client in inbound stats:

```php
$subscription->update([
    'traffic_used_bytes' => $downBytes + $upBytes,
    'traffic_up_bytes' => $upBytes,
    'traffic_down_bytes' => $downBytes,
]);
```

### Data Sources

Traffic data comes from `inbound.clientStats`:

- `down` - Downloaded bytes
- `up` - Uploaded bytes
- `email` - Client identifier

## Server Selection Logic

Servers are synchronized if they meet criteria:

- `is_active = true`
- `auto_sync = true` (unless `--force` flag)
- Not deleted (`deleted_at IS NULL`)

## Error Handling

### Graceful Degradation

- Individual server failures don't stop batch processing
- Connection timeouts are handled gracefully
- Detailed error logging for debugging

### Retry Mechanism

- Async jobs retry up to 3 times
- 60-second backoff between retries
- Permanent failure logging

### Logging

All operations are logged with appropriate levels:

- `INFO` - Successful operations
- `WARNING` - Non-critical failures
- `ERROR` - Critical failures requiring attention

## Performance Considerations

### Concurrent Processing

- Configurable batch sizes (default: 10 servers)
- Database transactions for atomic updates
- Memory-efficient processing of large client lists

### Resource Management

- Connection pooling for server requests
- Configurable timeouts (default: 30 seconds)
- Memory limits for large datasets

### Database Optimization

- Efficient queries with proper indexing
- Chunked updates for large user sets
- Transaction boundaries for consistency

## Monitoring and Maintenance

### Health Checks

Monitor these metrics:

- Sync success/failure rates
- Server response times
- Queue job processing times
- Database query performance

### Maintenance Tasks

- Clean up old user online logs (configurable retention)
- Monitor disk space for raw data storage
- Review server load thresholds

### Troubleshooting

Common issues and solutions:

1. **Server connection failures** - Check network connectivity and credentials
2. **High memory usage** - Reduce batch sizes or increase memory limits
3. **Slow sync times** - Optimize server response times or increase timeouts
4. **Queue backlog** - Scale queue workers or optimize job processing

## Configuration Reference

### Environment Variables

```env
# Sync configuration
XUI_SYNC_DEFAULT_BATCH_SIZE=10
XUI_SYNC_ONLINE_THRESHOLD=20
XUI_SYNC_OFFLINE_THRESHOLD=15
XUI_SYNC_TIMEOUT=300

# Server thresholds
XUI_SERVER_REQUEST_TIMEOUT=30
XUI_SERVER_LOAD_WARNING=80.0
XUI_SERVER_LOAD_CRITICAL=95.0

# Feature toggles
XUI_TRAFFIC_STATS_ENABLED=true
XUI_USER_STATUS_ENABLED=true
XUI_SYNC_LOGGING_ENABLED=true

# Performance tuning
XUI_SYNC_MAX_CONCURRENT=50
XUI_SYNC_MEMORY_LIMIT=512
XUI_SYNC_USER_CHUNK_SIZE=100
```

### Queue Configuration

```env
XUI_SYNC_QUEUE=default
XUI_SYNC_QUEUE_CONNECTION=database
```

## API Reference

### XuiServerSyncService Methods

```php
// Sync multiple servers
$results = $syncService->syncServers([
    'async' => true,
    'batch_size' => 10,
    'server_ids' => [1, 2, 3],
    'force' => false,
]);

// Sync single server
$success = $syncService->syncSingleServer($server);

// Detect offline users
$syncService->detectOfflineUsers();
```

### Return Values

```php
// syncServers() returns:
[
    'synced' => 5,      // Successfully synced
    'failed' => 1,      // Failed to sync
    'skipped' => 2,     // Skipped (inactive/disabled)
    'jobs_dispatched' => 8, // For async mode
]
```
