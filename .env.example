APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# Payment Gateway Configuration
PAYMENT_DEFAULT_GATEWAY=free
PAYMENT_FREE_ENABLED=true
PAYMENT_MANUAL_ENABLED=true
PAYMENT_CASH_ENABLED=true
PAYMENT_TBANK_ENABLED=false

# T-Bank Payment Gateway
TBANK_TERMINAL_KEY=
TBANK_PASSWORD=
TBANK_API_URL=https://securepay.tinkoff.ru/v2
TBANK_SUCCESS_URL={app_url}/access/{uuid}/plan/success/{order_id}
TBANK_FAIL_URL={app_url}/access/{uuid}/plan/failure/{order_id}
TBANK_NOTIFICATION_URL={app_url}/webhook/tbank
TBANK_LANGUAGE=ru
TBANK_CURRENCY=RUB
TBANK_PAY_TYPE=O
TBANK_TIMEOUT=30
TBANK_VERIFY_SSL=true

# T-Bank Payment Gateway Configuration
TBANK_TERMINAL_KEY=
TBANK_PASSWORD=
TBANK_NOTIFICATION_URL={APP_URL}/{TBANK_NOTIFICATION_ROUTE}
TBANK_WEBHOOK_VERIFY_SIGNATURE=false
PAYMENT_TBANK_ENABLED=false
