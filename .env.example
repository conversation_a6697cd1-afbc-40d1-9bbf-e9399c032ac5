APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# Trusted Proxies and Hosts Configuration
# Comma-separated list of trusted proxy IPs or CIDR ranges
# Use '*' to trust all proxies (not recommended for production)
TRUSTED_PROXIES=
# Example: TRUSTED_PROXIES="10.0.0.0/8,**********/12,***********/16"

# Trusted proxy headers configuration
# Options: HEADER_X_FORWARDED_ALL, HEADER_X_FORWARDED_AWS_ELB, HEADER_FORWARDED
TRUSTED_PROXY_HEADERS=HEADER_X_FORWARDED_ALL

# Comma-separated list of trusted hostnames
TRUSTED_HOSTS="localhost,127.0.0.1,svs.local,svs.devet.ru"

# Whether to allow subdomains of trusted hosts
TRUSTED_HOSTS_ALLOW_SUBDOMAINS=true

# Local development settings
LOCAL_TRUST_ALL_PROXIES=false

# Webhook security settings
WEBHOOK_ENFORCE_HOST_VALIDATION=true

# Security and debugging
LOG_SUSPICIOUS_REQUESTS=true
PROXY_STRICT_MODE=false
RATE_LIMIT_BY_REAL_IP=true
DEBUG_PROXY_HEADERS=false
DEBUG_SHOW_IP_INFO=false
