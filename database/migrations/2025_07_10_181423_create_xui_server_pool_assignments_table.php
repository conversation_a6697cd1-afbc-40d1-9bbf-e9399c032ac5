<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /**
         * This table is used as an intermediate table linking the xui_servers and server_pools.
         * For many to many relationship.
         */
        Schema::create('xui_server_pool_assignments', function (Blueprint $table) {
            $table->id()->primary();

            $table->foreignId('server_pool_id')->nullable();
            $table->foreignId('xui_server_id')->nullable();

            $table->timestamp('assigned_at')->nullable()->comment('The date when the user was assigned to the pool');
            $table->timestamp('released_at')->nullable()->comment('The date when the user was released from the pool, that means the user is no longer assigned to the pool');

            $table->timestamps();
            // soft deletes
            $table->softDeletes();

            $table->unique(['server_pool_id', 'xui_server_id']);

            $table->foreign('server_pool_id')
                    ->references('id')
                    ->on('server_pools')
                    ->onDelete('cascade');

            $table->foreign('xui_server_id')
                    ->references('id')
                    ->on('xui_servers')
                    ->onDelete('cascade');

            // indexes
            $table->index(['server_pool_id', 'released_at'], 'pool_released');
            $table->index(['xui_server_id', 'released_at'], 'server_released');
            $table->index(['server_pool_id', 'xui_server_id', 'released_at'], 'pool_server_released');
            $table->index(['xui_server_id', 'server_pool_id', 'released_at'], 'server_pool_released');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('xui_server_pool_assignments');
    }
};
