<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 2 test users: with subscription_plan = 1, and second one is without
        DB::table('users')->insert([
            [
                'id' => '10000000-439b-4e64-86cf-396df125c001',
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => bcrypt('password'),
                'is_active' => true,
                'registered_at' => now(),
                'subscription_plan_id' => 1,
                'admin_notes' => 'Test user with subscription plan'
            ],
            [
                'id' => '10000000-439b-4e64-86cf-396df125c002',
                'name' => 'Test User 2',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => bcrypt('password'),
                'is_active' => true,
                'registered_at' => now(),
                'subscription_plan_id' => null,
                'admin_notes' => 'Test user without subscription plan'
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('users')->where('email', '<EMAIL>')->delete();
        DB::table('users')->where('email', '<EMAIL>')->delete();
    }
};
