<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /**
         * Таблица для логирования webhook-данных
         */
        Schema::create('payment_webhooks', function (Blueprint $table) {
            $table->id()->primary();

            $table->uuid('payment_id')->nullable();
            $table->foreignId('method_id')->nullable();
            $table->string('external_payment_id')->nullable()->comment('External payment ID, e.g. from Stripe');
            $table->json('raw_payload')->nullable()->comment('Raw payload from the payment system, for logging and debugging purposes');

            $table->timestamps();
            // soft deletes
            $table->softDeletes();

            $table->foreign('payment_id')
                    ->references('id')
                    ->on('payments')
                    ->onDelete('set null');

            $table->foreign('method_id')
                    ->references('id')
                    ->on('payment_methods')
                    ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_webhooks');
    }
};
