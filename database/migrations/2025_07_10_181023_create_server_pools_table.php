<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /**
         * Группы серверов
         */
        Schema::create('server_pools', function (Blueprint $table) {
            $table->id()->primary();
            $table->string('name')->nullable();
            $table->text('notes')->nullable();
            $table->integer('max_users')->default(250);
            $table->boolean('is_active')->default(true);

            $table->timestamps();
            // soft deletes
            $table->softDeletes();
        });

        // insert first server pool
        DB::table('server_pools')->insert([
            [
                'name' => 'Pool #1',
                'notes' => 'First pool',
                'max_users' => 250,
                'is_active' => true,
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('server_pools');
    }
};
