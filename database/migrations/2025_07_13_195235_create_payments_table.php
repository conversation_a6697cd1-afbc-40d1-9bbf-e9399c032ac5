<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /**
         * Оплаты
         */
        Schema::create('payments', function (Blueprint $table) {
            $table->uuid('id')->primary();

            $table->string('public_id', 50)->unique()->comment('Public ID for payment, e.g. PAY-STRP-IUHW5KR');
            $table->uuid('order_id')->nullable();
            $table->foreignId('method_id')->nullable();
            $table->enum('status', ['pending', 'paid', 'failed', 'cancelled'])->default('pending');
            $table->integer('amount')->default(0)->comment('Total amount in minor units, e.g. kopecks, cents, etc.');
            $table->string('currency')->default('RUB');
            $table->json('raw_payload')->nullable()->comment('Raw payload from the payment system, for logging and debugging purposes');
            $table->timestamp('paid_at')->nullable();

            $table->timestamps();
            // soft deletes
            $table->softDeletes();

            $table->foreign('order_id')
                    ->references('id')
                    ->on('orders')
                    ->onDelete('set null');

            $table->foreign('method_id')
                    ->references('id')
                    ->on('payment_methods')
                    ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
