<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /**
         * Рефералы
         */
        Schema::create('referrals', function (Blueprint $table) {
            $table->id()->primary();

            $table->uuid('inviter_user_id')->nullable();
            $table->uuid('invited_user_id')->nullable();
            $table->timestamp('invited_at')->nullable();

            $table->json('utm')->nullable();
            $table->json('details')->nullable();

            $table->timestamps();
            // soft deletes
            $table->softDeletes();

            // indexes
            $table->foreign('inviter_user_id')
                    ->references('id')
                    ->on('users')
                    ->onDelete('set null');

            $table->foreign('invited_user_id')
                    ->references('id')
                    ->on('users')
                    ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('referrals');
    }
};
