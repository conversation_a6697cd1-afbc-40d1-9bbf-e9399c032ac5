<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */

    public function up(): void
    {
        /**
         * Заказы (может быть несколько позиций)
         */
        Schema::create('orders', function (Blueprint $table) {
            $table->uuid('id')->primary();

            $table->string('public_id', 50)->unique()->comment('Public ID for order, e.g. ORD-IUHW5KR');
            $table->uuid('user_id')->nullable();
            $table->enum('status', ['new', 'processing', 'paid', 'failed', 'cancelled'])->default('new');
            $table->integer('total_amount')->default(0)->comment('Total amount in minor units, e.g. kopecks, cents, etc.');
            $table->string('currency')->default('RUB');
            $table->timestamp('paid_at')->nullable();
            $table->timestamp('expires_at')->nullable()->comment('The date when the order expires, that means the order will be cancelled and not available for payment');
            $table->text('notes')->nullable();
            $table->text('admin_notes')->nullable()->comment('Notes added by admin for admins');

            $table->index(['status', 'expires_at']);
            $table->index(['status', 'paid_at']);
            $table->index(['status', 'created_at']);
            $table->index(['user_id', 'status']);
            $table->index(['user_id', 'created_at']);
            $table->index(['user_id', 'paid_at']);
            $table->index(['user_id', 'expires_at']);

            $table->timestamps();
            // soft deletes
            $table->softDeletes();

            $table->foreign('user_id')
                    ->references('id')
                    ->on('users')
                    ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
