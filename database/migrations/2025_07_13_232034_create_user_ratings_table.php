<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /**
         * Рейтинги пользователей
         */
        Schema::create('user_ratings', function (Blueprint $table) {
            $table->id()->primary();

            $table->uuid('user_id')->nullable();
            $table->tinyInteger('rating')->nullable()->comment('Rating from 1 to 5 (1=angry, 2=confused, 3=neutral, 4=happy, 5=excited)');
            $table->string('user_ip', 45)->nullable();
            $table->string('user_agent', 255)->nullable();

            $table->timestamps();

            $table->foreign('user_id')
                    ->references('id')
                    ->on('users')
                    ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_ratings');
    }
};
