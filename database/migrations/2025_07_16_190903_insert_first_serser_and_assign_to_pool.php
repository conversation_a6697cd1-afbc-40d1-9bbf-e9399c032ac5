<?php

use App\Models\ServerPool;
use App\Models\XuiServer;
use App\Models\XuiServerPoolAssignment;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $testServerAddress = config('services.test_xui_server.address');
        $testServerPort = config('services.test_xui_server.port');
        $testServerWebBasePath = config('services.test_xui_server.web_base_path');
        $testServerUsername = config('services.test_xui_server.username');
        $testServerPassword = config('services.test_xui_server.password');

        if ($testServerAddress) {
            XuiServer::create([
                'name' => 'Test server',
                'address' => $testServerAddress,
                'port' => $testServerPort,
                'web_base_path' => $testServerWebBasePath,
                'username' => $testServerUsername,
                'password' => $testServerPassword,
                'notes' => 'Test server for development',
                'is_active' => true,
                'auto_sync' => true,
            ]);

            // assign server to pool
            $pool = ServerPool::first();
            $server = XuiServer::first();
            XuiServerPoolAssignment::assignServerToPool($server->id, $pool->id);
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
