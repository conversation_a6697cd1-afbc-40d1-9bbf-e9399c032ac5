<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /**
         * Логи онлайна пользователей
         * Логирует статусы изменения онлайн и оффлайн
         */
        Schema::create('user_online_logs', function (Blueprint $table) {
            $table->id()->primary();

            $table->uuid('user_id')->nullable();
            $table->foreignId('xui_server_id')->nullable();
            $table->enum('status', ['online', 'offline', 'unknown'])->default('unknown');
            $table->timestamp('status_timestamp')->nullable();

            $table->timestamps();

            $table->foreign('user_id')
                    ->references('id')
                    ->on('users')
                    ->onDelete('set null');

            $table->foreign('xui_server_id')
                    ->references('id')
                    ->on('xui_servers')
                    ->onDelete('set null');

            // indexes
            $table->index(['user_id', 'status_timestamp']);
            $table->index(['xui_server_id', 'status_timestamp']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_online_logs');
    }
};
