<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /**
         * Позиции заказа (тариф, доп.услуги и т.п.)
         */
        Schema::create('order_items', function (Blueprint $table) {
            $table->id()->primary();
            $table->uuid('order_id')->nullable();
            $table->enum('item_type', ['subscription_plan', 'addon', 'custom'])->nullable()->comment('Type of the item, e.g. subscription plan, addon, custom');
            $table->string('item_id')->nullable()->comment('ID of the item, e.g. subscription plan ID, addon ID, etc.');
            $table->enum('action', ['new', 'renew', 'upgrade', 'extend', 'downgrade'])->nullable()->comment('What kind of subscription action is this: new/renew/upgrade');
            $table->text('notes')->nullable();
            $table->text('admin_notes')->nullable()->comment('Notes added by admin for admins');

            $table->integer('quantity')->default(1);
            $table->integer('unit_price')->default(0)->comment('Total amount in minor units, e.g. kopecks, cents, etc.');

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('order_id')
                    ->references('id')
                    ->on('orders')
                    ->onDelete('set null');

            // indexes
            $table->index(['order_id', 'item_type', 'item_id']);
            $table->index(['item_type', 'item_id']);

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};
