<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /**
         * Тарифные планы
         */
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->id()->primary();
            $table->string('name');
            $table->integer('duration')->nullable();
            $table->enum('duration_units', ['minute', 'hour', 'day', 'week', 'month', 'year'])->nullable();
            $table->bigInteger('traffic_limit_bytes')->nullable();
            $table->integer('traffic_limit_duration')->nullable()->comment('The duration of the traffic');
            $table->enum('traffic_limit_duration_unit', ['minute', 'hour', 'day', 'week', 'month'])->nullable()->comment('The unit of the traffic limit');
            $table->unsignedSmallInteger('limit_ip')->default(0);
            $table->integer('price')->default(0)->comment('Price in minor units, e.g. kopecks, cents, etc.');
            $table->integer('regular_price')->nullable()->comment('Regular price (old price) in minor units, e.g. kopecks, cents, etc.');
            $table->string('currency')->default('RUB');
            $table->boolean('is_custom')->default(false)->comment('Means that the special plan');
            $table->boolean('is_active')->default(false);
            $table->boolean('is_demo')->default(false);
            $table->boolean('is_public')->default(false)->comment('Whether the plan is available for public purchase');
            $table->boolean('is_archived')->default(false)->comment('Whether the plan is archived and not available for purchase');
            $table->foreignId('next_plan_id')->nullable()->comment('The next tariff plan that will be used in case the tariff plan no longer works. Can be used with is_archived and with is_active. Can be suggested to upgrade');
            $table->timestamp('access_expires_at')->nullable()->comment('The date when the access to the plan expires, that means the plan will be archived and not available for purchase and will be hidden from the user or public');

            $table->timestamps();
            // soft deletes
            $table->softDeletes();

            $table->foreign('next_plan_id')
                    ->references('id')
                    ->on('subscription_plans')
                    ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_plans');
    }
};
