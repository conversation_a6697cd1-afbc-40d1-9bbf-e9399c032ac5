<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /**
         * История изменений подписки
         */
        Schema::create('subscription_history', function (Blueprint $table) {
            $table->id()->primary();
            $table->uuid('subscription_id')->nullable();
            $table->uuid('user_id')->nullable();
            $table->enum('change_type', ['created', 'renewed', 'extended', 'upgraded', 'expired', 'manual'])->nullable();
            $table->text('notes')->nullable();
            $table->text('admin_notes')->nullable()->comment('Notes added by admin for admins');
            $table->integer('delta_duration')->nullable();
            $table->enum('delta_duration_units', ['minute', 'hour', 'day', 'week', 'month', 'year'])->nullable();
            // $table->uuid('changed_by_admin_id')->nullable()->index();

            $table->timestamps();
            // soft deletes
            $table->softDeletes();

            $table->foreign('subscription_id')
                    ->references('id')
                    ->on('subscriptions')
                    ->onDelete('set null');
            $table->foreign('user_id')
                    ->references('id')
                    ->on('users')
                    ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_history');
    }
};
