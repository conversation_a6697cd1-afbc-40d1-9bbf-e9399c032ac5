<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /**
         * Пользователи
         */
        Schema::create('users', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name')->nullable();
            $table->string('email')->unique()->nullable();
            $table->string('tg_id')->unique()->nullable();
            $table->string('old_client_id')->unique()->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_online_at')->nullable();
            $table->timestamp('registered_at')->useCurrent();
            $table->string('referral_code')->unique()->nullable();
            $table->uuid('referred_by_user_id')->nullable()->index();
            $table->text('notes')->nullable();
            $table->text('admin_notes')->nullable();
            $table->json('details')->nullable();
            $table->string('source')->nullable();
            $table->boolean('use_common_routing')->default(true)->comment('Whether to use common xray routing rules for the user');
            $table->rememberToken();

            $table->timestamps();
            // soft deletes
            $table->softDeletes();

            $table->foreign('referred_by_user_id')
                    ->references('id')
                    ->on('users')
                    ->onDelete('set null');
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
