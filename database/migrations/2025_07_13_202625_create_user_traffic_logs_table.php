<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /**
         * Логи трафика пользователей
         */
        Schema::create('user_traffic_logs', function (Blueprint $table) {
            $table->id()->primary();

            $table->uuid('user_id')->nullable();
            $table->uuid('subscription_id')->nullable();
            $table->foreignId('xui_server_id')->nullable();

            $table->bigInteger('traffic_used_bytes')->default(0)->comment('Total traffic used in bytes (sum of up and down)');
            $table->bigInteger('traffic_up_bytes')->default(0)->comment('Total traffic up in bytes');
            $table->bigInteger('traffic_down_bytes')->default(0)->comment('Total traffic down in bytes');

            $table->timestamps();

            $table->foreign('user_id')
                    ->references('id')
                    ->on('users')
                    ->onDelete('set null');

            $table->foreign('subscription_id')
                    ->references('id')
                    ->on('subscriptions')
                    ->onDelete('set null');

            $table->foreign('xui_server_id')
                    ->references('id')
                    ->on('xui_servers')
                    ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_traffic_logs');
    }
};
