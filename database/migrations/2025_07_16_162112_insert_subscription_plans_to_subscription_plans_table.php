<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Insert default subscription plans
        DB::table('subscription_plans')->insert([
            [
                'name' => 'ТЕСТОВЫЙ: 5 минут, 10 Мб',
                'duration' => 5,
                'duration_units' => 'minute',
                'traffic_limit_bytes' => 10 * 1024 * 1024, // 10 MB
                'traffic_limit_duration' => 5,
                'traffic_limit_duration_unit' => 'minute',
                'price' => 500, // price in minor units, e.g. kopecks, cents, etc.
                'regular_price' => 500,  // price in minor units, e.g. kopecks, cents, etc.
                'currency' => 'RUB',
                'is_custom' => false,
                'is_active' => true,
                'is_demo' => false,
                'is_public' => true,
                'is_archived' => false,
                'next_plan_id' => null,
            ],
            [
                'name' => 'ТЕСТОВЫЙ: 10 минут, 100 Мб',
                'duration' => 10,
                'duration_units' => 'minute',
                'traffic_limit_bytes' => 100 * 1024 * 1024, // 100 MB
                'traffic_limit_duration' => 10,
                'traffic_limit_duration_unit' => 'minute',
                'price' => 1000, // price in minor units, e.g. kopecks, cents, etc.
                'regular_price' => 1000,  // price in minor units, e.g. kopecks, cents, etc.
                'currency' => 'RUB',
                'is_custom' => false,
                'is_active' => true,
                'is_demo' => false,
                'is_public' => true,
                'is_archived' => false,
                'next_plan_id' => null,
            ],
            [
                'name' => 'ТЕСТОВЫЙ: 1 месяц, 50 Мб',
                'duration' => 1,
                'duration_units' => 'month',
                'traffic_limit_bytes' => 50 * 1024 * 1024, // 50 MB
                'traffic_limit_duration' => 1,
                'traffic_limit_duration_unit' => 'month',
                'price' => 1000, // price in minor units, e.g. kopecks, cents, etc.
                'regular_price' => 1000,  // price in minor units, e.g. kopecks, cents, etc.
                'currency' => 'RUB',
                'is_custom' => false,
                'is_active' => true,
                'is_demo' => false,
                'is_public' => true,
                'is_archived' => false,
                'next_plan_id' => null,
            ],
            [
                'name' => 'ТЕСТОВЫЙ: 5 минут, безлимит',
                'duration' => 5,
                'duration_units' => 'minute',
                'traffic_limit_bytes' => null,
                'traffic_limit_duration' => null,
                'traffic_limit_duration_unit' => null,
                'price' => 500, // price in minor units, e.g. kopecks, cents, etc.
                'regular_price' => 500,  // price in minor units, e.g. kopecks, cents, etc.
                'currency' => 'RUB',
                'is_custom' => false,
                'is_active' => true,
                'is_demo' => false,
                'is_public' => true,
                'is_archived' => false,
                'next_plan_id' => null,
            ],
            [
                'name' => 'Бессрочный безлимитный доступ',
                'duration' => null,
                'duration_units' => null,
                'traffic_limit_bytes' => null,
                'traffic_limit_duration' => null,
                'traffic_limit_duration_unit' => null,
                'price' => 0, // price in minor units, e.g. kopecks, cents, etc.
                'regular_price' => 0,  // price in minor units, e.g. kopecks, cents, etc.
                'currency' => 'RUB',
                'is_custom' => false,
                'is_active' => true,
                'is_demo' => false,
                'is_public' => false,
                'is_archived' => false,
                'next_plan_id' => null,
            ],
            [
                'name' => 'Demo безлимит на 24 часа',
                'duration' => 24,
                'duration_units' => 'hour',
                'traffic_limit_bytes' => null,
                'traffic_limit_duration' => null,
                'traffic_limit_duration_unit' => null,
                'price' => 0, // price in minor units, e.g. kopecks, cents, etc.
                'regular_price' => 0,  // price in minor units, e.g. kopecks, cents, etc.
                'currency' => 'RUB',
                'is_custom' => false,
                'is_active' => true,
                'is_demo' => true,
                'is_public' => false,
                'is_archived' => false,
                'next_plan_id' => null,
            ],
            [
                'name' => '1 месяц',
                'duration' => 1,
                'duration_units' => 'month',
                'traffic_limit_bytes' => null,
                'traffic_limit_duration' => null,
                'traffic_limit_duration_unit' => null,
                'price' => 14900, // price in minor units, e.g. kopecks, cents, etc.
                'regular_price' => 14900, // price in minor units, e.g. kopecks, cents, etc.
                'currency' => 'RUB',
                'is_custom' => false,
                'is_active' => true,
                'is_demo' => false,
                'is_public' => true,
                'is_archived' => false,
                'next_plan_id' => null,
            ],
            [
                'name' => '3 месяца',
                'duration' => 3,
                'duration_units' => 'month',
                'traffic_limit_bytes' => null,
                'traffic_limit_duration' => null,
                'traffic_limit_duration_unit' => null,
                'price' => 39900, // price in minor units, e.g. kopecks, cents, etc.
                'regular_price' => 45000, // price in minor units, e.g. kopecks, cents, etc.
                'currency' => 'RUB',
                'is_custom' => false,
                'is_active' => true,
                'is_demo' => false,
                'is_public' => true,
                'is_archived' => false,
                'next_plan_id' => null,
            ],
            [
                'name' => '6 месяцев',
                'duration' => 6,
                'duration_units' => 'month',
                'traffic_limit_bytes' => null,
                'traffic_limit_duration' => null,
                'traffic_limit_duration_unit' => null,
                'price' => 79900, // price in minor units, e.g. kopecks, cents, etc.
                'regular_price' => 90000, // price in minor units, e.g. kopecks, cents, etc.
                'currency' => 'RUB',
                'is_custom' => false,
                'is_active' => true,
                'is_demo' => false,
                'is_public' => true,
                'is_archived' => false,
                'next_plan_id' => null,
            ],
            [
                'name' => '1 год',
                'duration' => 12,
                'duration_units' => 'month',
                'traffic_limit_bytes' => null,
                'traffic_limit_duration' => null,
                'traffic_limit_duration_unit' => null,
                'price' => 149900, // price in minor units, e.g. kopecks, cents, etc.
                'regular_price' => 180000, // price in minor units, e.g. kopecks, cents, etc.
                'currency' => 'RUB',
                'is_custom' => false,
                'is_active' => true,
                'is_demo' => false,
                'is_public' => true,
                'is_archived' => false,
                'next_plan_id' => null,
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
