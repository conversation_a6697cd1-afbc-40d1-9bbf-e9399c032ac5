<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /**
         * This table is used as an intermediate table linking the users and server_pools.
         * For many to many relationship.
         */
        Schema::create('user_server_assignments', function (Blueprint $table) {
            $table->id()->primary();

            $table->uuid('user_id')->nullable();
            $table->foreignId('pool_id')->nullable();

            $table->timestamp('assigned_at')->nullable()->comment('The date when the user was assigned to the pool');
            $table->timestamp('released_at')->nullable()->comment('The date when the user was released from the pool, that means the user is no longer assigned to the pool');

            $table->unique(['user_id', 'pool_id']);

            $table->foreign('user_id')
                    ->references('id')
                    ->on('users')
                    ->onDelete('cascade');

            $table->foreign('pool_id')
                    ->references('id')
                    ->on('server_pools')
                    ->onDelete('cascade');

            $table->timestamps();

            // indexes
            $table->index(['user_id', 'released_at'], 'user_released');
            $table->index(['pool_id', 'released_at'], 'pool_released');
            $table->index(['user_id', 'pool_id', 'released_at'], 'user_pool_released');
            $table->index(['pool_id', 'user_id', 'released_at'], 'pool_user_released');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_server_assignments');
    }
};
