<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withProviders([
        App\Providers\ProxyServiceProvider::class,
    ])
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // Configure trusted proxies
        $middleware->trustProxies(
            at: getTrustedProxies(),
            headers: getTrustedProxyHeaders()
        );

        // Configure trusted hosts
        $middleware->trustHosts(
            at: getTrustedHosts(),
            subdomains: config('proxy.trusted_hosts.allow_subdomains', true)
        );

        // Add proxy information logging middleware
        $middleware->append(\App\Http\Middleware\LogProxyInformation::class);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        // Exception handling configuration can be added here
    })->create();
