<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Request;

/**
 * Get trusted proxy IP addresses based on environment and configuration.
 */
function getTrustedProxies(): array|string|null
{
    $environment = app()->environment();

    // Check if we should trust all proxies for this environment
    if (config("proxy.environments.{$environment}.trust_all_proxies", false)) {
        return '*';
    }

    // Get base trusted proxies
    $proxies = config('proxy.trusted_proxies.ips', []);

    // Add environment-specific proxies
    $envProxies = config("proxy.environments.{$environment}.additional_proxies", []);
    if (!empty($envProxies)) {
        $proxies = array_merge($proxies, $envProxies);
    }

    // Add webhook-specific IPs if this is a webhook request
    if (request() && str_contains(request()->path(), 'webhook')) {
        $webhookIps = config('proxy.webhook.trusted_ips', []);
        $proxies = array_merge($proxies, $webhookIps);
    }

    return array_filter($proxies);
}

/**
 * Get trusted proxy headers configuration.
 */
function getTrustedProxyHeaders(): int
{
    $headerConfig = config('proxy.trusted_proxies.headers', 'HEADER_X_FORWARDED_ALL');

    // Map string configuration to constants
    return match ($headerConfig) {
        'HEADER_X_FORWARDED_ALL' => Request::HEADER_X_FORWARDED_FOR |
                                   Request::HEADER_X_FORWARDED_HOST |
                                   Request::HEADER_X_FORWARDED_PORT |
                                   Request::HEADER_X_FORWARDED_PROTO |
                                   Request::HEADER_X_FORWARDED_PREFIX |
                                   Request::HEADER_X_FORWARDED_AWS_ELB,
        'HEADER_X_FORWARDED_AWS_ELB' => Request::HEADER_X_FORWARDED_AWS_ELB,
        'HEADER_FORWARDED' => Request::HEADER_FORWARDED,
        default => Request::HEADER_X_FORWARDED_FOR |
                  Request::HEADER_X_FORWARDED_HOST |
                  Request::HEADER_X_FORWARDED_PORT |
                  Request::HEADER_X_FORWARDED_PROTO,
    };
}

/**
 * Get trusted host names based on environment and configuration.
 */
function getTrustedHosts(): array|callable
{
    return function () {
        $environment = app()->environment();

        // Get base trusted hosts
        $hosts = config('proxy.trusted_hosts.hosts', []);

        // Add environment-specific hosts
        $envHosts = config("proxy.environments.{$environment}.additional_hosts", []);
        if (!empty($envHosts)) {
            $hosts = array_merge($hosts, $envHosts);
        }

        // Add current APP_URL host if not already included
        $appUrl = config('app.url');
        if ($appUrl) {
            $appHost = parse_url($appUrl, PHP_URL_HOST);
            if ($appHost && !in_array($appHost, $hosts)) {
                $hosts[] = $appHost;
            }
        }

        return array_filter($hosts);
    };
}

return Application::configure(basePath: dirname(__DIR__))
    ->withProviders([
        App\Providers\ProxyServiceProvider::class,
    ])
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // Configure trusted proxies
        $middleware->trustProxies(
            at: getTrustedProxies(),
            headers: getTrustedProxyHeaders()
        );

        // Configure trusted hosts
        $middleware->trustHosts(
            at: getTrustedHosts(),
            subdomains: config('proxy.trusted_hosts.allow_subdomains', true)
        );

        // Add proxy information logging middleware
        $middleware->append(\App\Http\Middleware\LogProxyInformation::class);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        // Exception handling configuration can be added here
    })->create();
