<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\PaymentMethod;
use App\Models\User;
use App\Models\Order;
use App\Models\Payment;
use App\Services\Payment\DTOs\WebhookData;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;

class TBankWebhookSignatureValidationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create T-Bank payment method
        PaymentMethod::factory()->create([
            'code' => 'tbank',
            'name' => 'T-Bank',
            'is_active' => true,
        ]);
    }

    /**
     * Test WebhookData extraction of T-Bank Token field.
     */
    public function test_webhook_data_extracts_tbank_token_from_payload(): void
    {
        // Sample T-Bank webhook payload from logs
        $payload = json_encode([
            'TerminalKey' => '1726392591291DEMO',
            'OrderId' => 'ORD-XZNVD9N',
            'Success' => true,
            'Status' => 'CONFIRMED',
            'PaymentId' => **********,
            'ErrorCode' => '0',
            'Amount' => 14900,
            'CardId' => *********,
            'Pan' => '430000******0777',
            'ExpDate' => '1230',
            'Token' => '58113548931cc236448ebdd2766b4658e6b6139279124337144028e2ff5934ad',
        ]);

        $headers = [
            'content-type' => 'application/json',
            'user-agent' => 'T-Bank-Webhook/1.0',
        ];

        $webhookData = WebhookData::fromRequest(
            headers: $headers,
            payload: $payload,
            userAgent: 'T-Bank-Webhook/1.0',
            ipAddress: '127.0.0.1'
        );

        // Verify that Token is extracted as signature
        $this->assertEquals('58113548931cc236448ebdd2766b4658e6b6139279124337144028e2ff5934ad', $webhookData->signature);
        $this->assertNotEmpty($webhookData->signature);
        
        // Verify webhook data validation passes
        $this->assertTrue($webhookData->validate());
    }

    /**
     * Test webhook processing with actual T-Bank data.
     */
    public function test_webhook_processing_with_actual_tbank_data(): void
    {
        // Disable signature verification for this test
        Config::set('payments.gateways.tbank.config.verify_signature', false);
        Config::set('payments.gateways.tbank.webhook.verify_signature', false);

        // Create test payment matching the webhook data
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'public_id' => 'ORD-XZNVD9N',
            'total_amount' => 14900,
            'status' => 'pending',
        ]);
        $payment = Payment::factory()->create([
            'order_id' => $order->id,
            'external_payment_id' => '**********',
            'status' => 'pending',
            'amount' => 14900,
        ]);

        // Actual T-Bank webhook data from logs
        $webhookData = [
            'TerminalKey' => '1726392591291DEMO',
            'OrderId' => 'ORD-XZNVD9N',
            'Success' => true,
            'Status' => 'CONFIRMED',
            'PaymentId' => **********,
            'ErrorCode' => '0',
            'Amount' => 14900,
            'CardId' => *********,
            'Pan' => '430000******0777',
            'ExpDate' => '1230',
            'Token' => '58113548931cc236448ebdd2766b4658e6b6139279124337144028e2ff5934ad',
        ];

        $response = $this->postJson('/webhook/tbank', $webhookData, [
            'User-Agent' => 'T-Bank-Webhook/1.0',
            'Content-Type' => 'application/json',
        ]);

        // Should not return "Webhook signature is missing" error
        $this->assertNotEquals(400, $response->getStatusCode());
        
        // Should process successfully (200) or fail validation (400) but not signature missing
        $this->assertContains($response->getStatusCode(), [200, 400, 401]);
        
        if ($response->getStatusCode() === 400) {
            $responseData = $response->json();
            $this->assertNotEquals('Webhook signature is missing', $responseData['message'] ?? '');
        }
    }

    /**
     * Test T-Bank token generation algorithm.
     */
    public function test_tbank_token_generation_algorithm(): void
    {
        // Create T-Bank gateway with test credentials
        $gateway = new \App\Services\Payment\Gateways\TBankGateway();
        $gateway->initialize([
            'terminal_key' => '1726392591291DEMO',
            'password' => 'test_password',
            'api_url' => 'https://test.api.url',
            'verify_signature' => true,
            'enabled' => true,
        ]);

        // Sample data for token generation (excluding Token field)
        $data = [
            'TerminalKey' => '1726392591291DEMO',
            'OrderId' => 'ORD-XZNVD9N',
            'Success' => true,
            'Status' => 'CONFIRMED',
            'PaymentId' => **********,
            'ErrorCode' => '0',
            'Amount' => 14900,
            'CardId' => *********,
            'Pan' => '430000******0777',
            'ExpDate' => '1230',
        ];

        // Use reflection to access private method
        $reflection = new \ReflectionClass($gateway);
        $generateTokenMethod = $reflection->getMethod('generateToken');
        $generateTokenMethod->setAccessible(true);

        $generatedToken = $generateTokenMethod->invoke($gateway, $data);

        // Verify token is generated
        $this->assertNotEmpty($generatedToken);
        $this->assertEquals(64, strlen($generatedToken)); // SHA-256 produces 64-character hex string
        $this->assertMatchesRegularExpression('/^[a-f0-9]{64}$/', $generatedToken);
    }

    /**
     * Test webhook signature verification with enabled verification.
     */
    public function test_webhook_signature_verification_enabled(): void
    {
        // Enable signature verification
        Config::set('payments.gateways.tbank.config.verify_signature', true);
        Config::set('payments.gateways.tbank.webhook.verify_signature', true);

        // Webhook data with potentially invalid signature
        $webhookData = [
            'TerminalKey' => '1726392591291DEMO',
            'OrderId' => 'ORD-TEST123',
            'Success' => true,
            'Status' => 'CONFIRMED',
            'PaymentId' => 12345,
            'ErrorCode' => '0',
            'Amount' => 1000,
            'Token' => 'invalid_signature_for_testing',
        ];

        $response = $this->postJson('/webhook/tbank', $webhookData, [
            'User-Agent' => 'T-Bank-Webhook/1.0',
            'Content-Type' => 'application/json',
        ]);

        // Should not return "Webhook signature is missing" error
        $responseData = $response->json();
        $this->assertNotEquals('Webhook signature is missing', $responseData['message'] ?? '');
        
        // Should return signature verification failure, not missing signature
        if ($response->getStatusCode() === 401) {
            $this->assertEquals('Invalid signature', $responseData['message'] ?? '');
        }
    }

    /**
     * Test webhook with missing Token field.
     */
    public function test_webhook_with_missing_token_field(): void
    {
        // Enable signature verification
        Config::set('payments.gateways.tbank.config.verify_signature', true);
        Config::set('payments.gateways.tbank.webhook.verify_signature', true);

        // Webhook data without Token field
        $webhookData = [
            'TerminalKey' => '1726392591291DEMO',
            'OrderId' => 'ORD-TEST123',
            'Success' => true,
            'Status' => 'CONFIRMED',
            'PaymentId' => 12345,
            'ErrorCode' => '0',
            'Amount' => 1000,
            // Missing Token field
        ];

        $response = $this->postJson('/webhook/tbank', $webhookData, [
            'User-Agent' => 'T-Bank-Webhook/1.0',
            'Content-Type' => 'application/json',
        ]);

        // Should return signature verification failure due to missing Token
        $this->assertEquals(401, $response->getStatusCode());
        
        $responseData = $response->json();
        $this->assertEquals('Invalid signature', $responseData['message'] ?? '');
    }

    /**
     * Test webhook data validation with various scenarios.
     */
    public function test_webhook_data_validation_scenarios(): void
    {
        // Test 1: Valid webhook with Token in payload
        $validPayload = json_encode([
            'TerminalKey' => 'test',
            'OrderId' => 'ORD-123',
            'Token' => 'valid_token_string',
        ]);

        $webhookData = WebhookData::fromRequest(
            headers: ['content-type' => 'application/json'],
            payload: $validPayload
        );

        $this->assertTrue($webhookData->validate());
        $this->assertEquals('valid_token_string', $webhookData->signature);

        // Test 2: Webhook with signature in header (standard approach)
        $headerPayload = json_encode(['TerminalKey' => 'test', 'OrderId' => 'ORD-123']);
        
        $webhookDataWithHeader = WebhookData::fromRequest(
            headers: ['signature' => 'header_signature'],
            payload: $headerPayload
        );

        $this->assertTrue($webhookDataWithHeader->validate());
        $this->assertEquals('header_signature', $webhookDataWithHeader->signature);

        // Test 3: Webhook without any signature
        $noSignaturePayload = json_encode(['TerminalKey' => 'test', 'OrderId' => 'ORD-123']);
        
        $webhookDataNoSignature = WebhookData::fromRequest(
            headers: ['content-type' => 'application/json'],
            payload: $noSignaturePayload
        );

        $this->assertFalse($webhookDataNoSignature->validate());
        $this->assertEmpty($webhookDataNoSignature->signature);
    }

    /**
     * Test T-Bank specific token field extraction.
     */
    public function test_tbank_token_field_extraction(): void
    {
        // Test case-sensitive Token field
        $payloadWithToken = json_encode([
            'TerminalKey' => 'test',
            'Token' => 'correct_token',
            'token' => 'wrong_token', // lowercase should be ignored
        ]);

        $webhookData = WebhookData::fromRequest(
            headers: [],
            payload: $payloadWithToken
        );

        // Should extract the uppercase Token field
        $this->assertEquals('correct_token', $webhookData->signature);

        // Test Token field priority over headers
        $webhookDataWithBoth = WebhookData::fromRequest(
            headers: ['signature' => 'header_signature'],
            payload: $payloadWithToken
        );

        // Header signature should take priority
        $this->assertEquals('header_signature', $webhookDataWithBoth->signature);
    }
}
