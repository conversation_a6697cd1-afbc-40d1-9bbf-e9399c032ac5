<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\XuiServer;
use App\Services\XuiManagementService;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class XuiLoginTest extends TestCase
{
    public function test_it_logs_into_xui_server()
    {
        // Предполагаем, что в БД уже есть хотя бы один XuiServer
        $server = XuiServer::active()->first();

        if (! $server) {
            $this->markTestSkipped('Нет доступного XuiServer в базе данных');
        }

        // Удалим предыдущий cookie, если есть
        $cookieDirectory = storage_path("app/xui/{$server->id}");
        $cookiePath = "{$cookieDirectory}/cookie.txt";
        if (File::exists($cookiePath)) {
            unlink($cookiePath);
        }

        // Убедимся, что файл cookie.txt не существует
        $this->assertFileDoesNotExist($cookiePath);

        // Инициализируем сервис (вызовет login в конструкторе)
        $service = new XuiManagementService($server);

        $status = $service->status();

        // Проверяем, что файл cookie был создан
        $this->assertFileExists($cookiePath);

        // Обновим модель из базы (вдруг изменилась)
        $server->refresh();

        // Проверяем, что в базе установлены поля
        $this->assertNotNull($server->last_login_at);
        $this->assertNotNull($server->session_cookie);

        // Проверяем, что cookie выглядит как непустая строка
        $this->assertIsString($server->session_cookie);
        $this->assertTrue(Str::length($server->session_cookie) > 10);

        // Проверяем, что сервер вернул статус
        $this->assertIsArray($status);
        $this->assertTrue($status['success']);
        $this->assertIsArray($status['obj']);

        // Получим время последнего логина до повторного вызова
        $lastLoginBefore = $server->last_login_at;

        // Далее тестируем, что куки существующие куки переиспользуются и не происходит каждый раз логин
        sleep(1); // чтобы изменить timestamps

        // Повторная инициализация сервиса (не должна вызывать login, а использовать куки)
        $service = new XuiManagementService($server);
        $status = $service->status();

        $lastLoginAfter = $server->fresh()->last_login_at;

        // Проверки
        $this->assertEquals($lastLoginBefore?->timestamp, $lastLoginAfter?->timestamp, 'last_login_at не должен обновиться');

        // Также проверим, что сервис работает
        $this->assertTrue($status['success']);
    }
}
