<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\PaymentMethod;
use App\Models\User;
use App\Models\Order;
use App\Models\Payment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;

class TBankWebhookSignatureTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create T-Bank payment method
        PaymentMethod::factory()->create([
            'code' => 'tbank',
            'name' => 'T-Bank',
            'is_active' => true,
        ]);
    }

    /**
     * Test webhook processing with signature verification disabled.
     */
    public function test_webhook_processing_with_signature_verification_disabled(): void
    {
        // Disable signature verification
        Config::set('payments.gateways.tbank.config.verify_signature', false);
        Config::set('payments.gateways.tbank.webhook.verify_signature', false);

        // Create test payment
        $user = User::factory()->create();
        $order = Order::factory()->create(['user_id' => $user->id]);
        $payment = Payment::factory()->create([
            'order_id' => $order->id,
            'external_payment_id' => '12345',
            'status' => 'pending',
        ]);

        // Mock T-Bank webhook data (without valid signature)
        $webhookData = [
            'TerminalKey' => 'test_terminal',
            'OrderId' => $order->public_id,
            'Success' => true,
            'Status' => 'CONFIRMED',
            'PaymentId' => '12345',
            'ErrorCode' => '0',
            'Amount' => 1000,
            'Token' => 'invalid_signature', // Invalid signature should be ignored
        ];

        $response = $this->postJson('/webhook/tbank', $webhookData);

        // Should process successfully without signature verification
        $this->assertContains($response->getStatusCode(), [200, 400]); // 200 = success, 400 = validation error
        $this->assertNotEquals(401, $response->getStatusCode()); // Should not be signature error
    }

    /**
     * Test webhook processing with signature verification enabled but invalid signature.
     */
    public function test_webhook_processing_with_invalid_signature(): void
    {
        // Enable signature verification
        Config::set('payments.gateways.tbank.config.verify_signature', true);
        Config::set('payments.gateways.tbank.webhook.verify_signature', true);

        // Mock T-Bank webhook data with invalid signature
        $webhookData = [
            'TerminalKey' => 'test_terminal',
            'OrderId' => 'ORD-TEST123',
            'Success' => true,
            'Status' => 'CONFIRMED',
            'PaymentId' => '12345',
            'ErrorCode' => '0',
            'Amount' => 1000,
            'Token' => 'invalid_signature',
        ];

        $response = $this->postJson('/webhook/tbank', $webhookData);

        // Should fail signature verification
        $this->assertEquals(401, $response->getStatusCode());
        
        $responseData = $response->json();
        $this->assertFalse($responseData['success']);
        $this->assertEquals('Invalid signature', $responseData['message']);
    }

    /**
     * Test webhook processing from local IP address.
     */
    public function test_webhook_processing_from_local_ip(): void
    {
        // Disable signature verification for this test
        Config::set('payments.gateways.tbank.config.verify_signature', false);
        Config::set('payments.gateways.tbank.webhook.verify_signature', false);

        // Mock T-Bank webhook data
        $webhookData = [
            'TerminalKey' => 'test_terminal',
            'OrderId' => 'ORD-TEST123',
            'Success' => true,
            'Status' => 'CONFIRMED',
            'PaymentId' => '12345',
            'ErrorCode' => '0',
            'Amount' => 1000,
            'Token' => 'test_token',
        ];

        // Test from localhost (should be allowed in local environment)
        $response = $this->postJson('/webhook/tbank', $webhookData, [
            'REMOTE_ADDR' => '127.0.0.1',
        ]);

        // Should not be blocked by IP validation
        $this->assertNotEquals(403, $response->getStatusCode()); // Should not be IP blocked
        $this->assertContains($response->getStatusCode(), [200, 400, 401]); // Valid processing responses
    }

    /**
     * Test webhook signature verification configuration in different environments.
     */
    public function test_signature_verification_configuration_by_environment(): void
    {
        // Test local environment (should disable signature verification by default)
        app()->detectEnvironment(function () {
            return 'local';
        });
        
        // Reload configuration
        $this->refreshApplication();
        
        $localConfig = config('payments.gateways.tbank.config.verify_signature');
        $this->assertFalse($localConfig, 'Signature verification should be disabled in local environment');

        // Test production environment (should enable signature verification by default)
        app()->detectEnvironment(function () {
            return 'production';
        });
        
        // Reload configuration
        $this->refreshApplication();
        
        $prodConfig = config('payments.gateways.tbank.config.verify_signature');
        $this->assertTrue($prodConfig, 'Signature verification should be enabled in production environment');
    }

    /**
     * Test webhook with missing Token field.
     */
    public function test_webhook_with_missing_token_field(): void
    {
        // Enable signature verification
        Config::set('payments.gateways.tbank.config.verify_signature', true);
        Config::set('payments.gateways.tbank.webhook.verify_signature', true);

        // Mock T-Bank webhook data without Token field
        $webhookData = [
            'TerminalKey' => 'test_terminal',
            'OrderId' => 'ORD-TEST123',
            'Success' => true,
            'Status' => 'CONFIRMED',
            'PaymentId' => '12345',
            'ErrorCode' => '0',
            'Amount' => 1000,
            // Missing Token field
        ];

        $response = $this->postJson('/webhook/tbank', $webhookData);

        // Should fail due to missing signature
        $this->assertEquals(401, $response->getStatusCode());
    }

    /**
     * Test webhook with invalid JSON payload.
     */
    public function test_webhook_with_invalid_json(): void
    {
        // Enable signature verification
        Config::set('payments.gateways.tbank.config.verify_signature', true);
        Config::set('payments.gateways.tbank.webhook.verify_signature', true);

        // Send invalid JSON
        $response = $this->post('/webhook/tbank', [], [
            'Content-Type' => 'application/json',
        ]);
        
        // Add raw invalid JSON content
        $response = $this->call('POST', '/webhook/tbank', [], [], [], [
            'CONTENT_TYPE' => 'application/json',
        ], 'invalid json content');

        // Should handle invalid JSON gracefully
        $this->assertContains($response->getStatusCode(), [400, 401, 500]);
    }

    /**
     * Test IP address validation with various IP ranges.
     */
    public function test_ip_address_validation(): void
    {
        // Disable signature verification to focus on IP validation
        Config::set('payments.gateways.tbank.config.verify_signature', false);
        Config::set('payments.gateways.tbank.webhook.verify_signature', false);

        $webhookData = [
            'TerminalKey' => 'test_terminal',
            'OrderId' => 'ORD-TEST123',
            'Success' => true,
            'Status' => 'CONFIRMED',
            'PaymentId' => '12345',
            'ErrorCode' => '0',
            'Amount' => 1000,
            'Token' => 'test_token',
        ];

        // Test allowed local IPs
        $allowedIPs = ['127.0.0.1', '::1', '***********', '********'];
        
        foreach ($allowedIPs as $ip) {
            $response = $this->postJson('/webhook/tbank', $webhookData, [
                'REMOTE_ADDR' => $ip,
            ]);
            
            $this->assertNotEquals(403, $response->getStatusCode(), 
                "IP {$ip} should be allowed in local environment");
        }
    }

    /**
     * Test webhook processing with detailed logging.
     */
    public function test_webhook_processing_with_logging(): void
    {
        // Disable signature verification
        Config::set('payments.gateways.tbank.config.verify_signature', false);
        Config::set('payments.gateways.tbank.webhook.verify_signature', false);

        // Enable debug logging
        Config::set('app.debug', true);

        $webhookData = [
            'TerminalKey' => 'test_terminal',
            'OrderId' => 'ORD-TEST123',
            'Success' => true,
            'Status' => 'CONFIRMED',
            'PaymentId' => '12345',
            'ErrorCode' => '0',
            'Amount' => 1000,
            'Token' => 'test_token',
        ];

        // Capture logs
        $this->expectsEvents(\Illuminate\Log\Events\MessageLogged::class);

        $response = $this->postJson('/webhook/tbank', $webhookData);

        // Should process and log appropriately
        $this->assertContains($response->getStatusCode(), [200, 400, 401]);
    }
}
