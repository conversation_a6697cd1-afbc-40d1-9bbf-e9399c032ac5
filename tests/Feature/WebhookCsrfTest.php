<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\PaymentMethod;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WebhookCsrfTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that webhook routes are excluded from CSRF protection.
     */
    public function test_webhook_routes_excluded_from_csrf(): void
    {
        // Create a T-Bank payment method
        PaymentMethod::factory()->create([
            'code' => 'tbank',
            'name' => 'T-Bank',
            'is_active' => true,
        ]);

        // Test T-Bank webhook without CSRF token
        $webhookData = [
            'TerminalKey' => 'test_terminal',
            'OrderId' => 'ORD-TEST123',
            'Success' => true,
            'Status' => 'CONFIRMED',
            'PaymentId' => '12345',
            'ErrorCode' => '0',
            'Amount' => 1000,
            'Token' => 'test_token',
        ];

        $response = $this->postJson('/webhook/tbank', $webhookData);

        // Should not return 419 (CSRF token mismatch)
        $this->assertNotEquals(419, $response->getStatusCode());
        
        // Should return either 200 (success), 400 (validation error), or 401 (signature error)
        // but not 419 (CSRF error)
        $this->assertContains($response->getStatusCode(), [200, 400, 401, 500]);
    }

    /**
     * Test that generic webhook route is excluded from CSRF protection.
     */
    public function test_generic_webhook_route_excluded_from_csrf(): void
    {
        // Create a test payment method
        PaymentMethod::factory()->create([
            'code' => 'test',
            'name' => 'Test Gateway',
            'is_active' => true,
        ]);

        $webhookData = [
            'payment_id' => '12345',
            'status' => 'completed',
            'amount' => 1000,
        ];

        $response = $this->postJson('/webhook/test', $webhookData);

        // Should not return 419 (CSRF token mismatch)
        $this->assertNotEquals(419, $response->getStatusCode());
        
        // Should return either 200 (success), 400 (validation error), or 401 (signature error)
        // but not 419 (CSRF error)
        $this->assertContains($response->getStatusCode(), [200, 400, 401, 500]);
    }

    /**
     * Test that non-webhook routes still require CSRF protection.
     */
    public function test_non_webhook_routes_require_csrf(): void
    {
        // Test a regular POST route (VPN purchase) without CSRF token
        $response = $this->postJson('/access/********-1234-1234-1234-************/plan/purchase', [
            'plan_id' => 1,
            'payment_method' => 'manual',
        ]);

        // Should return 419 (CSRF token mismatch) for non-webhook routes
        $this->assertEquals(419, $response->getStatusCode());
    }

    /**
     * Test webhook route accessibility.
     */
    public function test_webhook_routes_are_accessible(): void
    {
        // Test that webhook routes exist and are accessible
        $response = $this->get('/webhook/tbank');
        
        // Should return 405 (Method Not Allowed) for GET request, not 404 (Not Found)
        $this->assertEquals(405, $response->getStatusCode());
        
        $response = $this->get('/webhook/test');
        
        // Should return 405 (Method Not Allowed) for GET request, not 404 (Not Found)
        $this->assertEquals(405, $response->getStatusCode());
    }

    /**
     * Test webhook logging functionality.
     */
    public function test_webhook_logging(): void
    {
        // Create a T-Bank payment method
        PaymentMethod::factory()->create([
            'code' => 'tbank',
            'name' => 'T-Bank',
            'is_active' => true,
        ]);

        // Mock webhook data
        $webhookData = [
            'TerminalKey' => 'test_terminal',
            'OrderId' => 'ORD-TEST123',
            'Success' => true,
            'Status' => 'CONFIRMED',
            'PaymentId' => '12345',
            'ErrorCode' => '0',
            'Amount' => 1000,
            'Token' => 'test_token',
        ];

        // Capture logs
        $this->expectsEvents(\Illuminate\Log\Events\MessageLogged::class);

        $response = $this->postJson('/webhook/tbank', $webhookData, [
            'Content-Type' => 'application/json',
            'User-Agent' => 'T-Bank-Webhook/1.0',
        ]);

        // Verify that webhook was received and logged
        // The exact response depends on webhook processing logic
        $this->assertNotEquals(419, $response->getStatusCode());
    }

    /**
     * Test webhook with various content types.
     */
    public function test_webhook_with_different_content_types(): void
    {
        // Create a T-Bank payment method
        PaymentMethod::factory()->create([
            'code' => 'tbank',
            'name' => 'T-Bank',
            'is_active' => true,
        ]);

        $webhookData = [
            'TerminalKey' => 'test_terminal',
            'OrderId' => 'ORD-TEST123',
            'Success' => true,
            'Status' => 'CONFIRMED',
            'PaymentId' => '12345',
            'ErrorCode' => '0',
            'Amount' => 1000,
            'Token' => 'test_token',
        ];

        // Test with JSON content type
        $response = $this->postJson('/webhook/tbank', $webhookData);
        $this->assertNotEquals(419, $response->getStatusCode());

        // Test with form data content type
        $response = $this->post('/webhook/tbank', $webhookData);
        $this->assertNotEquals(419, $response->getStatusCode());

        // Test with custom content type (as T-Bank might send)
        $response = $this->post('/webhook/tbank', $webhookData, [
            'Content-Type' => 'application/x-www-form-urlencoded',
        ]);
        $this->assertNotEquals(419, $response->getStatusCode());
    }
}
