<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\Payment\PaymentGatewayManager;
use App\Services\Payment\Gateways\ManualGateway;
use App\Services\Payment\Gateways\FreeGateway;
use App\Services\Payment\Gateways\CashGateway;
use Illuminate\Foundation\Testing\RefreshDatabase;

class GatewayConfigurationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that ManualGateway receives the correct configuration structure.
     */
    public function test_manual_gateway_receives_correct_configuration(): void
    {
        // Mock the configuration
        config([
            'payments.gateways.manual' => [
                'driver' => 'manual',
                'name' => 'Manual Payment',
                'enabled' => true,
                'config' => [
                    'auto_confirm' => false,
                    'admin_notification' => true,
                ]
            ]
        ]);

        $gatewayManager = new PaymentGatewayManager();
        
        // This should not throw an exception
        $gateway = $gatewayManager->getGateway('manual');
        
        $this->assertInstanceOf(ManualGateway::class, $gateway);
        
        // Check that the gateway received the correct configuration
        $config = $gateway->getConfig();
        
        $this->assertArrayHasKey('enabled', $config);
        $this->assertArrayHasKey('auto_confirm', $config);
        $this->assertArrayHasKey('admin_notification', $config);
        $this->assertTrue($config['enabled']);
        $this->assertFalse($config['auto_confirm']);
        $this->assertTrue($config['admin_notification']);
    }

    /**
     * Test that FreeGateway receives the correct configuration structure.
     */
    public function test_free_gateway_receives_correct_configuration(): void
    {
        // Mock the configuration
        config([
            'payments.gateways.free' => [
                'driver' => 'free',
                'name' => 'Free Payment',
                'enabled' => true,
                'config' => [
                    'max_amount' => 0,
                    'auto_confirm' => true,
                ]
            ]
        ]);

        $gatewayManager = new PaymentGatewayManager();
        
        // This should not throw an exception
        $gateway = $gatewayManager->getGateway('free');
        
        $this->assertInstanceOf(FreeGateway::class, $gateway);
        
        // Check that the gateway received the correct configuration
        $config = $gateway->getConfig();
        
        $this->assertArrayHasKey('enabled', $config);
        $this->assertArrayHasKey('max_amount', $config);
        $this->assertTrue($config['enabled']);
        $this->assertEquals(0, $config['max_amount']);
    }

    /**
     * Test that CashGateway receives the correct configuration structure.
     */
    public function test_cash_gateway_receives_correct_configuration(): void
    {
        // Mock the configuration
        config([
            'payments.gateways.cash' => [
                'driver' => 'cash',
                'name' => 'Cash Payment',
                'enabled' => true,
                'config' => [
                    'auto_confirm' => false,
                ]
            ]
        ]);

        $gatewayManager = new PaymentGatewayManager();
        
        // This should not throw an exception
        $gateway = $gatewayManager->getGateway('cash');
        
        $this->assertInstanceOf(CashGateway::class, $gateway);
        
        // Check that the gateway received the correct configuration
        $config = $gateway->getConfig();
        
        $this->assertArrayHasKey('enabled', $config);
        $this->assertTrue($config['enabled']);
    }

    /**
     * Test that all gateways can be created without errors.
     */
    public function test_all_gateways_can_be_created(): void
    {
        // Mock configurations for all gateways
        config([
            'payments.gateways' => [
                'manual' => [
                    'driver' => 'manual',
                    'name' => 'Manual Payment',
                    'enabled' => true,
                    'config' => [
                        'auto_confirm' => false,
                    ]
                ],
                'cash' => [
                    'driver' => 'cash',
                    'name' => 'Cash Payment',
                    'enabled' => true,
                    'config' => [
                        'auto_confirm' => false,
                    ]
                ],
                'free' => [
                    'driver' => 'free',
                    'name' => 'Free Payment',
                    'enabled' => true,
                    'config' => [
                        'max_amount' => 0,
                    ]
                ],
                'tbank' => [
                    'driver' => 'tbank',
                    'name' => 'T-Bank',
                    'enabled' => true,
                    'config' => [
                        'terminal_key' => 'test_terminal_key',
                        'password' => 'test_password',
                    ]
                ],
            ]
        ]);

        $gatewayManager = new PaymentGatewayManager();
        
        // Test that all gateways can be created
        $gateways = ['manual', 'cash', 'free', 'tbank'];
        
        foreach ($gateways as $gatewayName) {
            $gateway = $gatewayManager->getGateway($gatewayName);
            $this->assertNotNull($gateway);
            $this->assertTrue($gateway->isAvailable());
        }
    }
}
