<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Console\Commands\PaymentTestCommand;
use App\Services\Payment\PaymentGatewayManager;
use App\Models\User;
use App\Models\Order;
use App\Services\Payment\DTOs\PaymentRequest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PaymentTestCommandTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that PaymentRequest can be created with proper parameters.
     */
    public function test_payment_request_creation_with_valid_parameters(): void
    {
        // Create a test user
        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Create a test order
        $order = new Order();
        $order->id = (string) \Illuminate\Support\Str::uuid();
        $order->public_id = 'ORD-TEST123';
        $order->user_id = $user->id;
        $order->status = 'pending';
        $order->total_amount = 10000; // 100.00 RUB in kopecks
        $order->currency = 'RUB';
        $order->created_at = now();
        $order->updated_at = now();

        // Set the user relationship
        $order->setRelation('user', $user);

        // Test PaymentRequest creation
        $paymentRequest = new PaymentRequest(
            order: $order,
            user: $user,
            amount: 10000,
            currency: 'RUB',
            description: 'Test payment'
        );

        // Assertions
        $this->assertInstanceOf(PaymentRequest::class, $paymentRequest);
        $this->assertEquals($order, $paymentRequest->order);
        $this->assertEquals($user, $paymentRequest->user);
        $this->assertEquals(10000, $paymentRequest->amount);
        $this->assertEquals('RUB', $paymentRequest->currency);
        $this->assertEquals('Test payment', $paymentRequest->description);
    }

    /**
     * Test that the command can create test users and orders.
     */
    public function test_command_can_create_test_data(): void
    {
        // Mock the gateway manager
        $gatewayManager = $this->createMock(PaymentGatewayManager::class);
        $gatewayManager->method('getAvailableGateways')->willReturn(['manual']);

        // Create command instance
        $command = new PaymentTestCommand($gatewayManager);

        // Use reflection to test private methods
        $reflection = new \ReflectionClass($command);

        // Test getOrCreateTestUser method
        $getOrCreateTestUserMethod = $reflection->getMethod('getOrCreateTestUser');
        $getOrCreateTestUserMethod->setAccessible(true);

        $user = $getOrCreateTestUserMethod->invoke($command);

        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals('Test User', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);

        // Test createTestOrder method
        $createTestOrderMethod = $reflection->getMethod('createTestOrder');
        $createTestOrderMethod->setAccessible(true);

        $order = $createTestOrderMethod->invoke($command, $user, 100);

        $this->assertInstanceOf(Order::class, $order);
        $this->assertEquals($user->id, $order->user_id);
        $this->assertEquals('pending', $order->status);
        $this->assertEquals(100, $order->total_amount); // Specified amount
        $this->assertEquals('RUB', $order->currency); // Default currency from command
        $this->assertStringStartsWith('ORD-TEST', $order->public_id);
    }

    /**
     * Test that PaymentRequest validation works with test data.
     */
    public function test_payment_validation_with_test_data(): void
    {
        // Create a test user
        $user = User::factory()->create();

        // Create command instance with mock gateway manager
        $gatewayManager = $this->createMock(PaymentGatewayManager::class);
        $command = new PaymentTestCommand($gatewayManager);

        // Use reflection to access private methods
        $reflection = new \ReflectionClass($command);

        $createTestOrderMethod = $reflection->getMethod('createTestOrder');
        $createTestOrderMethod->setAccessible(true);

        $order = $createTestOrderMethod->invoke($command, $user);

        // Test that we can create a PaymentRequest with the test data
        $paymentRequest = new PaymentRequest(
            order: $order,
            user: $user,
            amount: $order->total_amount,
            currency: $order->currency,
            description: 'Test payment'
        );

        $this->assertInstanceOf(PaymentRequest::class, $paymentRequest);
        $this->assertEquals($order->total_amount, $paymentRequest->amount);
        $this->assertEquals($order->currency, $paymentRequest->currency);
        $this->assertEquals($user, $paymentRequest->user);
        $this->assertEquals($order, $paymentRequest->order);
    }

    /**
     * Test gateway-specific amount handling.
     */
    public function test_gateway_specific_amounts(): void
    {
        // Mock the gateway manager
        $gatewayManager = $this->createMock(PaymentGatewayManager::class);

        // Create command instance
        $command = new PaymentTestCommand($gatewayManager);

        // Use reflection to test private method
        $reflection = new \ReflectionClass($command);
        $getTestAmountMethod = $reflection->getMethod('getTestAmountForGateway');
        $getTestAmountMethod->setAccessible(true);

        // Test free gateway gets amount 0
        $freeAmount = $getTestAmountMethod->invoke($command, 'free');
        $this->assertEquals(0, $freeAmount);

        // Test other gateways get the specified amount (default 100)
        $manualAmount = $getTestAmountMethod->invoke($command, 'manual');
        $this->assertEquals(100, $manualAmount); // Default from command signature

        $tbankAmount = $getTestAmountMethod->invoke($command, 'tbank');
        $this->assertEquals(100, $tbankAmount); // Default from command signature
    }

    /**
     * Test configuration error detection.
     */
    public function test_configuration_error_detection(): void
    {
        // Mock the gateway manager
        $gatewayManager = $this->createMock(PaymentGatewayManager::class);

        // Create command instance
        $command = new PaymentTestCommand($gatewayManager);

        // Use reflection to test private method
        $reflection = new \ReflectionClass($command);
        $isConfigErrorMethod = $reflection->getMethod('isConfigurationError');
        $isConfigErrorMethod->setAccessible(true);

        // Test T-Bank configuration errors
        $tbankError = new \Exception('T-Bank gateway requires terminal_key');
        $this->assertTrue($isConfigErrorMethod->invoke($command, $tbankError, 'tbank'));

        // Test general configuration errors
        $configError = new \Exception('Missing configuration for payment gateway');
        $this->assertTrue($isConfigErrorMethod->invoke($command, $configError, 'manual'));

        // Test non-configuration errors
        $validationError = new \Exception('Invalid payment amount');
        $this->assertFalse($isConfigErrorMethod->invoke($command, $validationError, 'manual'));
    }
}
