<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\Payment\Gateways\TBankGateway;
use App\Services\Payment\DTOs\PaymentRequest;
use App\Models\User;
use App\Models\Order;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TBankCurrencyMappingTest extends TestCase
{
    use RefreshDatabase;

    private TBankGateway $gateway;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->gateway = new TBankGateway();
        $this->gateway->initialize([
            'terminal_key' => 'test_terminal_key',
            'password' => 'test_password',
            'api_url' => 'https://test.api.url',
            'enabled' => true,
        ]);
    }

    /**
     * Test currency code conversion for supported currencies.
     */
    public function test_currency_code_conversion_for_supported_currencies(): void
    {
        $user = User::factory()->create();
        $order = $this->createTestOrder($user, 1000, 'RUB');

        // Test RUB currency
        $paymentRequest = new PaymentRequest(
            order: $order,
            user: $user,
            amount: 1000,
            currency: 'RUB',
            description: 'Test payment'
        );

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->gateway);
        $buildPaymentDataMethod = $reflection->getMethod('buildPaymentData');
        $buildPaymentDataMethod->setAccessible(true);

        $paymentData = $buildPaymentDataMethod->invoke($this->gateway, $paymentRequest);

        // Verify that currency is converted to numeric code
        $this->assertEquals(643, $paymentData['Currency']); // RUB = 643
    }

    /**
     * Test currency code conversion for different currencies.
     */
    public function test_currency_code_conversion_for_different_currencies(): void
    {
        $user = User::factory()->create();
        
        $testCases = [
            'RUB' => 643,
            'USD' => 840,
            'EUR' => 978,
            'GBP' => 826,
            'CNY' => 156,
            'JPY' => 392,
            'KZT' => 398,
            'BYN' => 933,
            'UAH' => 980,
        ];

        $reflection = new \ReflectionClass($this->gateway);
        $getCurrencyCodeMethod = $reflection->getMethod('getCurrencyCode');
        $getCurrencyCodeMethod->setAccessible(true);

        foreach ($testCases as $currencyCode => $expectedNumericCode) {
            $result = $getCurrencyCodeMethod->invoke($this->gateway, $currencyCode);
            $this->assertEquals($expectedNumericCode, $result, "Currency {$currencyCode} should map to {$expectedNumericCode}");
        }
    }

    /**
     * Test currency code conversion with lowercase input.
     */
    public function test_currency_code_conversion_with_lowercase(): void
    {
        $reflection = new \ReflectionClass($this->gateway);
        $getCurrencyCodeMethod = $reflection->getMethod('getCurrencyCode');
        $getCurrencyCodeMethod->setAccessible(true);

        // Test lowercase input
        $result = $getCurrencyCodeMethod->invoke($this->gateway, 'rub');
        $this->assertEquals(643, $result);

        $result = $getCurrencyCodeMethod->invoke($this->gateway, 'usd');
        $this->assertEquals(840, $result);
    }

    /**
     * Test currency code conversion with unsupported currency.
     */
    public function test_currency_code_conversion_with_unsupported_currency(): void
    {
        $reflection = new \ReflectionClass($this->gateway);
        $getCurrencyCodeMethod = $reflection->getMethod('getCurrencyCode');
        $getCurrencyCodeMethod->setAccessible(true);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Unsupported currency: XYZ');

        $getCurrencyCodeMethod->invoke($this->gateway, 'XYZ');
    }

    /**
     * Test that buildPaymentData includes correct currency code.
     */
    public function test_build_payment_data_includes_correct_currency_code(): void
    {
        $user = User::factory()->create();
        $order = $this->createTestOrder($user, 5000, 'USD');

        $paymentRequest = new PaymentRequest(
            order: $order,
            user: $user,
            amount: 5000,
            currency: 'USD',
            description: 'Test USD payment'
        );

        $reflection = new \ReflectionClass($this->gateway);
        $buildPaymentDataMethod = $reflection->getMethod('buildPaymentData');
        $buildPaymentDataMethod->setAccessible(true);

        $paymentData = $buildPaymentDataMethod->invoke($this->gateway, $paymentRequest);

        // Verify all required fields are present
        $this->assertArrayHasKey('Currency', $paymentData);
        $this->assertArrayHasKey('Amount', $paymentData);
        $this->assertArrayHasKey('OrderId', $paymentData);
        $this->assertArrayHasKey('Description', $paymentData);
        $this->assertArrayHasKey('Token', $paymentData);

        // Verify currency is numeric
        $this->assertEquals(840, $paymentData['Currency']); // USD = 840
        $this->assertIsInt($paymentData['Currency']);
    }

    /**
     * Test that supported currencies list matches currency mapping.
     */
    public function test_supported_currencies_matches_mapping(): void
    {
        $reflection = new \ReflectionClass($this->gateway);
        $currencyCodesConstant = $reflection->getConstant('CURRENCY_CODES');
        
        $supportedCurrenciesProperty = $reflection->getProperty('supportedCurrencies');
        $supportedCurrenciesProperty->setAccessible(true);
        $supportedCurrencies = $supportedCurrenciesProperty->getValue($this->gateway);

        // Verify that all supported currencies have mappings
        foreach ($supportedCurrencies as $currency) {
            $this->assertArrayHasKey($currency, $currencyCodesConstant, 
                "Supported currency {$currency} should have a mapping in CURRENCY_CODES");
        }

        // Verify that all mapped currencies are in supported list
        foreach (array_keys($currencyCodesConstant) as $currency) {
            $this->assertContains($currency, $supportedCurrencies, 
                "Mapped currency {$currency} should be in supportedCurrencies list");
        }
    }

    /**
     * Helper method to create a test order.
     */
    private function createTestOrder(User $user, int $amount, string $currency): Order
    {
        $order = new Order();
        $order->id = (string) \Illuminate\Support\Str::uuid();
        $order->public_id = 'ORD-TEST' . strtoupper(\Illuminate\Support\Str::random(7));
        $order->user_id = $user->id;
        $order->status = 'pending';
        $order->total_amount = $amount;
        $order->currency = $currency;
        $order->created_at = now();
        $order->updated_at = now();
        
        $order->setRelation('user', $user);
        $order->exists = false;
        
        return $order;
    }
}
