<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\Payment\Gateways\TBankGateway;
use App\Services\Payment\DTOs\PaymentRequest;
use App\Models\User;
use App\Models\Order;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TBankNotificationUrlTest extends TestCase
{
    use RefreshDatabase;

    private TBankGateway $gateway;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->gateway = new TBankGateway();
        $this->gateway->initialize([
            'terminal_key' => 'test_terminal_key',
            'password' => 'test_password',
            'api_url' => 'https://test.api.url',
            'enabled' => true,
        ]);
    }

    /**
     * Test notification URL resolution with placeholders.
     */
    public function test_notification_url_resolution_with_placeholders(): void
    {
        // Set app URL for testing
        config(['app.url' => 'https://svs.devet.ru:6443']);
        
        $user = User::factory()->create();
        $order = $this->createTestOrder($user, 1000, 'RUB');

        // Test with placeholder URL
        $paymentRequest = new PaymentRequest(
            order: $order,
            user: $user,
            amount: 1000,
            currency: 'RUB',
            description: 'Test payment',
            notificationUrl: '{APP_URL}/{TBANK_NOTIFICATION_ROUTE}'
        );

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->gateway);
        $buildPaymentDataMethod = $reflection->getMethod('buildPaymentData');
        $buildPaymentDataMethod->setAccessible(true);

        $paymentData = $buildPaymentDataMethod->invoke($this->gateway, $paymentRequest);

        // Verify that notification URL is properly resolved
        $this->assertArrayHasKey('NotificationURL', $paymentData);
        $this->assertEquals('https://svs.devet.ru:6443/webhook/tbank', $paymentData['NotificationURL']);
        $this->assertStringNotContainsString('{', $paymentData['NotificationURL']);
        $this->assertStringNotContainsString('}', $paymentData['NotificationURL']);
    }

    /**
     * Test notification URL resolution with partial placeholders.
     */
    public function test_notification_url_resolution_with_partial_placeholders(): void
    {
        config(['app.url' => 'https://example.com']);
        
        $user = User::factory()->create();
        $order = $this->createTestOrder($user, 1000, 'RUB');

        // Test with only APP_URL placeholder
        $paymentRequest = new PaymentRequest(
            order: $order,
            user: $user,
            amount: 1000,
            currency: 'RUB',
            description: 'Test payment',
            notificationUrl: '{APP_URL}/webhook/tbank'
        );

        $reflection = new \ReflectionClass($this->gateway);
        $buildPaymentDataMethod = $reflection->getMethod('buildPaymentData');
        $buildPaymentDataMethod->setAccessible(true);

        $paymentData = $buildPaymentDataMethod->invoke($this->gateway, $paymentRequest);

        $this->assertEquals('https://example.com/webhook/tbank', $paymentData['NotificationURL']);
    }

    /**
     * Test notification URL resolution without placeholders.
     */
    public function test_notification_url_resolution_without_placeholders(): void
    {
        $user = User::factory()->create();
        $order = $this->createTestOrder($user, 1000, 'RUB');

        // Test with direct URL (no placeholders)
        $directUrl = 'https://direct.example.com/webhook/tbank';
        $paymentRequest = new PaymentRequest(
            order: $order,
            user: $user,
            amount: 1000,
            currency: 'RUB',
            description: 'Test payment',
            notificationUrl: $directUrl
        );

        $reflection = new \ReflectionClass($this->gateway);
        $buildPaymentDataMethod = $reflection->getMethod('buildPaymentData');
        $buildPaymentDataMethod->setAccessible(true);

        $paymentData = $buildPaymentDataMethod->invoke($this->gateway, $paymentRequest);

        // Should use the direct URL without modification
        $this->assertEquals($directUrl, $paymentData['NotificationURL']);
    }

    /**
     * Test notification URL resolution from configuration.
     */
    public function test_notification_url_resolution_from_config(): void
    {
        config(['app.url' => 'https://config.example.com']);
        
        // Initialize gateway with notification URL in config
        $this->gateway->initialize([
            'terminal_key' => 'test_terminal_key',
            'password' => 'test_password',
            'api_url' => 'https://test.api.url',
            'notification_url' => '{APP_URL}/{TBANK_NOTIFICATION_ROUTE}',
            'enabled' => true,
        ]);

        $user = User::factory()->create();
        $order = $this->createTestOrder($user, 1000, 'RUB');

        // Test without notification URL in request (should use config)
        $paymentRequest = new PaymentRequest(
            order: $order,
            user: $user,
            amount: 1000,
            currency: 'RUB',
            description: 'Test payment'
        );

        $reflection = new \ReflectionClass($this->gateway);
        $buildPaymentDataMethod = $reflection->getMethod('buildPaymentData');
        $buildPaymentDataMethod->setAccessible(true);

        $paymentData = $buildPaymentDataMethod->invoke($this->gateway, $paymentRequest);

        $this->assertEquals('https://config.example.com/webhook/tbank', $paymentData['NotificationURL']);
    }

    /**
     * Test notification URL fallback when placeholders are invalid.
     */
    public function test_notification_url_fallback_for_invalid_placeholders(): void
    {
        config(['app.url' => 'https://fallback.example.com']);
        
        $user = User::factory()->create();
        $order = $this->createTestOrder($user, 1000, 'RUB');

        // Test with invalid placeholder
        $paymentRequest = new PaymentRequest(
            order: $order,
            user: $user,
            amount: 1000,
            currency: 'RUB',
            description: 'Test payment',
            notificationUrl: '{INVALID_PLACEHOLDER}/webhook'
        );

        $reflection = new \ReflectionClass($this->gateway);
        $buildPaymentDataMethod = $reflection->getMethod('buildPaymentData');
        $buildPaymentDataMethod->setAccessible(true);

        $paymentData = $buildPaymentDataMethod->invoke($this->gateway, $paymentRequest);

        // Should fallback to building URL from scratch
        $this->assertStringContains('webhook/tbank', $paymentData['NotificationURL']);
        $this->assertStringNotContainsString('{INVALID_PLACEHOLDER}', $paymentData['NotificationURL']);
    }

    /**
     * Test resolveNotificationUrl method directly.
     */
    public function test_resolve_notification_url_method(): void
    {
        config(['app.url' => 'https://direct.test.com']);
        
        $reflection = new \ReflectionClass($this->gateway);
        $resolveMethod = $reflection->getMethod('resolveNotificationUrl');
        $resolveMethod->setAccessible(true);

        // Test various URL patterns
        $testCases = [
            '{APP_URL}/{TBANK_NOTIFICATION_ROUTE}' => 'https://direct.test.com/webhook/tbank',
            '{APP_URL}/webhook/tbank' => 'https://direct.test.com/webhook/tbank',
            'https://custom.com/webhook/tbank' => 'https://custom.com/webhook/tbank',
            '{INVALID}/test' => 'https://direct.test.com/webhook/tbank', // Should fallback
        ];

        foreach ($testCases as $input => $expected) {
            $result = $resolveMethod->invoke($this->gateway, $input);
            $this->assertStringContains('webhook/tbank', $result, "Failed for input: {$input}");
            
            if (!str_contains($input, '{INVALID}')) {
                $this->assertEquals($expected, $result, "Failed exact match for input: {$input}");
            }
        }
    }

    /**
     * Helper method to create a test order.
     */
    private function createTestOrder(User $user, int $amount, string $currency): Order
    {
        $order = new Order();
        $order->id = (string) \Illuminate\Support\Str::uuid();
        $order->public_id = 'ORD-TEST' . strtoupper(\Illuminate\Support\Str::random(7));
        $order->user_id = $user->id;
        $order->status = 'pending';
        $order->total_amount = $amount;
        $order->currency = $currency;
        $order->created_at = now();
        $order->updated_at = now();
        
        $order->setRelation('user', $user);
        $order->exists = false;
        
        return $order;
    }
}
