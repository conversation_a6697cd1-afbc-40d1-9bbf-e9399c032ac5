<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\Payment\PaymentGatewayManager;
use App\Services\Payment\Gateways\TBankGateway;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TBankConfigurationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that TBankGateway receives the correct configuration structure.
     */
    public function test_tbank_gateway_receives_correct_configuration(): void
    {
        // Mock the configuration
        config([
            'payments.gateways.tbank' => [
                'driver' => 'tbank',
                'name' => 'T-Bank',
                'enabled' => true,
                'config' => [
                    'terminal_key' => 'test_terminal_key',
                    'password' => 'test_password',
                    'api_url' => 'https://test.api.url',
                ]
            ]
        ]);

        $gatewayManager = new PaymentGatewayManager();
        
        // This should not throw an exception
        $gateway = $gatewayManager->getGateway('tbank');
        
        $this->assertInstanceOf(TBankGateway::class, $gateway);
        
        // Check that the gateway received the correct configuration
        $config = $gateway->getConfig();
        
        $this->assertArrayHasKey('terminal_key', $config);
        $this->assertArrayHasKey('password', $config);
        $this->assertArrayHasKey('enabled', $config);
        $this->assertEquals('test_terminal_key', $config['terminal_key']);
        $this->assertEquals('test_password', $config['password']);
        $this->assertTrue($config['enabled']);
    }

    /**
     * Test that TBankGateway throws exception when terminal_key is missing.
     */
    public function test_tbank_gateway_throws_exception_when_terminal_key_missing(): void
    {
        // Mock the configuration without terminal_key
        config([
            'payments.gateways.tbank' => [
                'driver' => 'tbank',
                'name' => 'T-Bank',
                'enabled' => true,
                'config' => [
                    'password' => 'test_password',
                    'api_url' => 'https://test.api.url',
                ]
            ]
        ]);

        $gatewayManager = new PaymentGatewayManager();
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('T-Bank gateway requires terminal_key');
        
        $gatewayManager->getGateway('tbank');
    }

    /**
     * Test that TBankGateway throws exception when password is missing.
     */
    public function test_tbank_gateway_throws_exception_when_password_missing(): void
    {
        // Mock the configuration without password
        config([
            'payments.gateways.tbank' => [
                'driver' => 'tbank',
                'name' => 'T-Bank',
                'enabled' => true,
                'config' => [
                    'terminal_key' => 'test_terminal_key',
                    'api_url' => 'https://test.api.url',
                ]
            ]
        ]);

        $gatewayManager = new PaymentGatewayManager();
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('T-Bank gateway requires password');
        
        $gatewayManager->getGateway('tbank');
    }

    /**
     * Test that TBankGateway works with empty config section.
     */
    public function test_tbank_gateway_handles_empty_config_section(): void
    {
        // Mock the configuration with empty config section
        config([
            'payments.gateways.tbank' => [
                'driver' => 'tbank',
                'name' => 'T-Bank',
                'enabled' => true,
                'config' => []
            ]
        ]);

        $gatewayManager = new PaymentGatewayManager();
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('T-Bank gateway requires terminal_key');
        
        $gatewayManager->getGateway('tbank');
    }

    /**
     * Test that TBankGateway works with missing config section.
     */
    public function test_tbank_gateway_handles_missing_config_section(): void
    {
        // Mock the configuration without config section
        config([
            'payments.gateways.tbank' => [
                'driver' => 'tbank',
                'name' => 'T-Bank',
                'enabled' => true,
            ]
        ]);

        $gatewayManager = new PaymentGatewayManager();
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('T-Bank gateway requires terminal_key');
        
        $gatewayManager->getGateway('tbank');
    }
}
