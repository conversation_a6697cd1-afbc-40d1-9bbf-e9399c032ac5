<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\Payment\DTOs\PaymentRequest;
use App\Services\Payment\DTOs\RecurringPaymentRequest;
use App\Services\Payment\Gateways\FreeGateway;
use App\Services\Payment\Gateways\ManualGateway;
use App\Models\User;
use App\Models\Order;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PaymentValidationFixTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that PaymentRequest allows zero amounts.
     */
    public function test_payment_request_allows_zero_amount(): void
    {
        $user = User::factory()->create();
        $order = $this->createTestOrder($user, 0);

        $paymentRequest = new PaymentRequest(
            order: $order,
            user: $user,
            amount: 0,
            currency: 'RUB',
            description: 'Test free payment'
        );

        $errors = $paymentRequest->validate();

        $this->assertEmpty($errors, 'PaymentRequest should allow zero amounts');
        $this->assertEquals(0, $paymentRequest->amount);
    }

    /**
     * Test that PaymentRequest rejects negative amounts.
     */
    public function test_payment_request_rejects_negative_amount(): void
    {
        $user = User::factory()->create();
        $order = $this->createTestOrder($user, -100);

        $paymentRequest = new PaymentRequest(
            order: $order,
            user: $user,
            amount: -100,
            currency: 'RUB',
            description: 'Test negative payment'
        );

        $errors = $paymentRequest->validate();

        $this->assertNotEmpty($errors, 'PaymentRequest should reject negative amounts');
        $this->assertContains('Amount cannot be negative', $errors);
    }

    /**
     * Test that RecurringPaymentRequest allows zero amounts.
     */
    public function test_recurring_payment_request_allows_zero_amount(): void
    {
        $user = User::factory()->create();

        $recurringRequest = new RecurringPaymentRequest(
            user: $user,
            amount: 0,
            currency: 'RUB',
            description: 'Test free recurring payment',
            customerKey: 'test-customer-key',
            rebillId: 'test-rebill-id'
        );

        $errors = $recurringRequest->validate();

        $this->assertEmpty($errors, 'RecurringPaymentRequest should allow zero amounts');
        $this->assertEquals(0, $recurringRequest->amount);
    }

    /**
     * Test FreeGateway validation with zero amount.
     */
    public function test_free_gateway_validates_zero_amount(): void
    {
        $user = User::factory()->create();
        $order = $this->createTestOrder($user, 0);

        $paymentRequest = new PaymentRequest(
            order: $order,
            user: $user,
            amount: 0,
            currency: 'RUB',
            description: 'Test free payment'
        );

        $gateway = new FreeGateway();
        $gateway->initialize([
            'enabled' => true,
            'max_amount' => 0,
        ]);

        $errors = $gateway->validatePaymentRequest($paymentRequest);

        // Should not have the "Free payments must have zero amount" error
        $this->assertNotContains('Free payments must have zero amount', $errors);
        
        // May have other errors (like user eligibility), but not the amount error
        $amountErrors = array_filter($errors, function($error) {
            return str_contains($error, 'amount') || str_contains($error, 'Amount');
        });
        
        $this->assertEmpty($amountErrors, 'FreeGateway should not have amount-related errors for zero amount');
    }

    /**
     * Test FreeGateway validation with non-zero amount.
     */
    public function test_free_gateway_rejects_non_zero_amount(): void
    {
        $user = User::factory()->create();
        $order = $this->createTestOrder($user, 100);

        $paymentRequest = new PaymentRequest(
            order: $order,
            user: $user,
            amount: 100,
            currency: 'RUB',
            description: 'Test non-free payment'
        );

        $gateway = new FreeGateway();
        $gateway->initialize([
            'enabled' => true,
            'max_amount' => 0,
        ]);

        $errors = $gateway->validatePaymentRequest($paymentRequest);

        $this->assertContains('Free payments must have zero amount', $errors);
    }

    /**
     * Test ManualGateway validation with positive amount.
     */
    public function test_manual_gateway_validates_positive_amount(): void
    {
        $user = User::factory()->create();
        $order = $this->createTestOrder($user, 100);

        $paymentRequest = new PaymentRequest(
            order: $order,
            user: $user,
            amount: 100,
            currency: 'RUB',
            description: 'Test manual payment'
        );

        $gateway = new ManualGateway();
        $gateway->initialize([
            'enabled' => true,
        ]);

        $errors = $gateway->validatePaymentRequest($paymentRequest);

        // Should not have amount-related errors for valid positive amount
        $amountErrors = array_filter($errors, function($error) {
            return str_contains($error, 'amount') || str_contains($error, 'Amount');
        });
        
        $this->assertEmpty($amountErrors, 'ManualGateway should not have amount-related errors for valid positive amount');
    }

    /**
     * Helper method to create a test order.
     */
    private function createTestOrder(User $user, int $amount): Order
    {
        $order = new Order();
        $order->id = (string) \Illuminate\Support\Str::uuid();
        $order->public_id = 'ORD-TEST' . strtoupper(\Illuminate\Support\Str::random(7));
        $order->user_id = $user->id;
        $order->status = 'pending';
        $order->total_amount = $amount;
        $order->currency = 'RUB';
        $order->created_at = now();
        $order->updated_at = now();
        
        $order->setRelation('user', $user);
        $order->exists = false;
        
        return $order;
    }
}
