<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Http\Controllers\PlanSelectionController;
use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\PaymentMethod;
use App\Models\Subscription;
use App\Services\Payment\PaymentGatewayManager;
use App\Services\Payment\Gateways\ManualGateway;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\View\View;
use Mockery;

class PlanSelectionControllerTest extends TestCase
{
    use RefreshDatabase;

    private PaymentGatewayManager $gatewayManager;
    private PlanSelectionController $controller;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->gatewayManager = Mockery::mock(PaymentGatewayManager::class);
        $this->controller = new PlanSelectionController($this->gatewayManager);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test plan selection page for user without subscription.
     */
    public function test_plan_selection_for_user_without_subscription(): void
    {
        // Create test data
        $user = User::factory()->create();
        
        $plans = SubscriptionPlan::factory()->count(3)->create([
            'is_public' => true,
            'is_active' => true,
            'is_archived' => false,
        ]);

        $paymentMethods = PaymentMethod::factory()->count(2)->create([
            'is_active' => true,
        ]);

        // Mock gateway availability
        $mockGateway = Mockery::mock(ManualGateway::class);
        $mockGateway->shouldReceive('isAvailable')->andReturn(true);
        
        $this->gatewayManager
            ->shouldReceive('getGateway')
            ->andReturn($mockGateway);

        // Execute
        $response = $this->controller->show($user->id);

        // Assert
        $this->assertInstanceOf(View::class, $response);
        $this->assertEquals('vpn.plan-selection', $response->name());
        
        $data = $response->getData();
        $this->assertEquals($user->id, $data['user']->id);
        $this->assertNull($data['currentPlan']);
        $this->assertNull($data['currentSubscription']);
        $this->assertEquals(3, $data['availablePlans']->count());
        $this->assertEquals(2, $data['paymentMethods']->count());
        $this->assertFalse($data['subscriptionStatus']['has_subscription']);
    }

    /**
     * Test plan selection page for user with active subscription.
     */
    public function test_plan_selection_for_user_with_active_subscription(): void
    {
        // Create user with active subscription
        $user = User::factory()->create();
        $currentPlan = SubscriptionPlan::factory()->create([
            'is_public' => true,
            'is_active' => true,
            'name' => 'Current Plan',
        ]);
        
        $subscription = Subscription::factory()->create([
            'user_id' => $user->id,
            'plan_id' => $currentPlan->id,
            'status' => 'active',
            'start_date' => now()->subDays(10),
            'end_date' => now()->addDays(20), // 20 days remaining
        ]);

        // Create additional public plans
        SubscriptionPlan::factory()->count(2)->create([
            'is_public' => true,
            'is_active' => true,
            'is_archived' => false,
        ]);

        $paymentMethods = PaymentMethod::factory()->count(2)->create([
            'is_active' => true,
        ]);

        // Mock gateway availability
        $mockGateway = Mockery::mock(ManualGateway::class);
        $mockGateway->shouldReceive('isAvailable')->andReturn(true);
        
        $this->gatewayManager
            ->shouldReceive('getGateway')
            ->andReturn($mockGateway);

        // Execute
        $response = $this->controller->show($user->id);

        // Assert
        $this->assertInstanceOf(View::class, $response);
        
        $data = $response->getData();
        $this->assertEquals($currentPlan->id, $data['currentPlan']->id);
        $this->assertEquals($subscription->id, $data['currentSubscription']->id);
        $this->assertEquals(3, $data['availablePlans']->count()); // 2 public + 1 current
        $this->assertTrue($data['subscriptionStatus']['has_subscription']);
        $this->assertEquals('active', $data['subscriptionStatus']['status']);
        $this->assertEquals(20, $data['subscriptionStatus']['days_remaining']);
    }

    /**
     * Test plan selection with expired subscription.
     */
    public function test_plan_selection_with_expired_subscription(): void
    {
        // Create user with expired subscription
        $user = User::factory()->create();
        $currentPlan = SubscriptionPlan::factory()->create([
            'is_public' => true,
            'is_active' => true,
        ]);
        
        $subscription = Subscription::factory()->create([
            'user_id' => $user->id,
            'plan_id' => $currentPlan->id,
            'status' => 'expired',
            'start_date' => now()->subDays(40),
            'end_date' => now()->subDays(10), // Expired 10 days ago
        ]);

        $paymentMethods = PaymentMethod::factory()->count(1)->create([
            'is_active' => true,
        ]);

        // Mock gateway availability
        $mockGateway = Mockery::mock(ManualGateway::class);
        $mockGateway->shouldReceive('isAvailable')->andReturn(true);
        
        $this->gatewayManager
            ->shouldReceive('getGateway')
            ->andReturn($mockGateway);

        // Execute
        $response = $this->controller->show($user->id);

        // Assert
        $data = $response->getData();
        $this->assertTrue($data['subscriptionStatus']['has_subscription']);
        $this->assertEquals('expired', $data['subscriptionStatus']['status']);
        $this->assertEquals(0, $data['subscriptionStatus']['days_remaining']);
    }

    /**
     * Test plan selection with subscription expiring soon.
     */
    public function test_plan_selection_with_subscription_expiring_soon(): void
    {
        // Create user with subscription expiring in 3 days
        $user = User::factory()->create();
        $currentPlan = SubscriptionPlan::factory()->create([
            'is_public' => true,
            'is_active' => true,
        ]);
        
        $subscription = Subscription::factory()->create([
            'user_id' => $user->id,
            'plan_id' => $currentPlan->id,
            'status' => 'active',
            'start_date' => now()->subDays(27),
            'end_date' => now()->addDays(3), // Expires in 3 days
        ]);

        $paymentMethods = PaymentMethod::factory()->count(1)->create([
            'is_active' => true,
        ]);

        // Mock gateway availability
        $mockGateway = Mockery::mock(ManualGateway::class);
        $mockGateway->shouldReceive('isAvailable')->andReturn(true);
        
        $this->gatewayManager
            ->shouldReceive('getGateway')
            ->andReturn($mockGateway);

        // Execute
        $response = $this->controller->show($user->id);

        // Assert
        $data = $response->getData();
        $this->assertTrue($data['subscriptionStatus']['has_subscription']);
        $this->assertEquals('expiring_soon', $data['subscriptionStatus']['status']);
        $this->assertEquals(3, $data['subscriptionStatus']['days_remaining']);
    }

    /**
     * Test plan selection includes archived current plan.
     */
    public function test_plan_selection_includes_archived_current_plan(): void
    {
        // Create user with subscription to an archived plan
        $user = User::factory()->create();
        $archivedPlan = SubscriptionPlan::factory()->create([
            'is_public' => false,
            'is_active' => false,
            'is_archived' => true,
            'name' => 'Archived Plan',
        ]);
        
        $subscription = Subscription::factory()->create([
            'user_id' => $user->id,
            'plan_id' => $archivedPlan->id,
            'status' => 'active',
            'start_date' => now()->subDays(10),
            'end_date' => now()->addDays(20),
        ]);

        // Create public plans
        SubscriptionPlan::factory()->count(2)->create([
            'is_public' => true,
            'is_active' => true,
            'is_archived' => false,
        ]);

        $paymentMethods = PaymentMethod::factory()->count(1)->create([
            'is_active' => true,
        ]);

        // Mock gateway availability
        $mockGateway = Mockery::mock(ManualGateway::class);
        $mockGateway->shouldReceive('isAvailable')->andReturn(true);
        
        $this->gatewayManager
            ->shouldReceive('getGateway')
            ->andReturn($mockGateway);

        // Execute
        $response = $this->controller->show($user->id);

        // Assert
        $data = $response->getData();
        $this->assertEquals($archivedPlan->id, $data['currentPlan']->id);
        $this->assertEquals(3, $data['availablePlans']->count()); // 2 public + 1 archived current
        
        // Verify archived plan is first in the collection
        $this->assertEquals($archivedPlan->id, $data['availablePlans']->first()->id);
    }

    /**
     * Test plan selection filters unavailable payment methods.
     */
    public function test_plan_selection_filters_unavailable_payment_methods(): void
    {
        $user = User::factory()->create();
        
        SubscriptionPlan::factory()->count(1)->create([
            'is_public' => true,
            'is_active' => true,
        ]);

        // Create payment methods
        $availableMethod = PaymentMethod::factory()->create([
            'is_active' => true,
            'code' => 'manual',
        ]);
        $unavailableMethod = PaymentMethod::factory()->create([
            'is_active' => true,
            'code' => 'unavailable',
        ]);

        // Mock gateway availability
        $this->gatewayManager
            ->shouldReceive('getGateway')
            ->with('manual')
            ->andReturn(Mockery::mock(ManualGateway::class, function ($mock) {
                $mock->shouldReceive('isAvailable')->andReturn(true);
            }));

        $this->gatewayManager
            ->shouldReceive('getGateway')
            ->with('unavailable')
            ->andThrow(new \Exception('Gateway not available'));

        // Execute
        $response = $this->controller->show($user->id);

        // Assert
        $data = $response->getData();
        $this->assertEquals(1, $data['paymentMethods']->count());
        $this->assertEquals('manual', $data['paymentMethods']->first()->code);
    }

    /**
     * Test plan selection with invalid UUID.
     */
    public function test_plan_selection_with_invalid_uuid(): void
    {
        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);
        $this->controller->show('invalid-uuid');
    }

    /**
     * Test plan selection with non-existent user.
     */
    public function test_plan_selection_with_nonexistent_user(): void
    {
        $uuid = '12345678-1234-1234-1234-123456789012';
        
        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);
        $this->controller->show($uuid);
    }
}
