<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Http\Controllers\VpnPurchaseController;
use App\Models\User;
use App\Models\Order;
use App\Models\SubscriptionPlan;
use App\Models\PaymentMethod;
use App\Models\Subscription;
use App\Services\Payment\PaymentService;
use App\Services\Payment\DTOs\PaymentResponse;
use Illuminate\Http\Request;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Validation\ValidationException;
use Mockery;

class VpnPurchaseControllerTest extends TestCase
{
    use RefreshDatabase;

    private PaymentService $paymentService;
    private VpnPurchaseController $controller;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->paymentService = Mockery::mock(PaymentService::class);
        $this->controller = new VpnPurchaseController($this->paymentService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test successful purchase for new subscription.
     */
    public function test_successful_purchase_new_subscription(): void
    {
        // Create test data
        $user = User::factory()->create(['is_active' => true]);
        $plan = SubscriptionPlan::factory()->create([
            'is_public' => true,
            'is_active' => true,
            'is_archived' => false,
            'price' => 1000, // 10.00 in kopecks
        ]);
        $paymentMethod = PaymentMethod::factory()->create([
            'is_active' => true,
            'code' => 'manual',
        ]);

        // Mock successful payment response
        $paymentResponse = new PaymentResponse(
            success: true,
            paymentId: 'test-payment-id',
            paymentUrl: 'https://payment.example.com/pay',
            message: 'Payment initiated successfully'
        );

        $this->paymentService
            ->shouldReceive('createPayment')
            ->once()
            ->andReturn($paymentResponse);

        // Create request
        $request = Request::create('/test', 'POST', [
            'plan_id' => $plan->id,
            'payment_method' => $paymentMethod->code,
            '_token' => csrf_token(),
        ]);

        // Execute
        $response = $this->controller->purchase($request, $user->id);

        // Assert
        $this->assertInstanceOf(\Illuminate\Http\RedirectResponse::class, $response);
        $this->assertEquals('https://payment.example.com/pay', $response->getTargetUrl());

        // Verify order was created
        $this->assertDatabaseHas('orders', [
            'user_id' => $user->id,
            'total_amount' => 1000,
            'status' => 'pending',
        ]);
    }

    /**
     * Test purchase validation with invalid UUID.
     */
    public function test_purchase_with_invalid_uuid(): void
    {
        $request = Request::create('/test', 'POST', [
            'plan_id' => 1,
            'payment_method' => 'manual',
            '_token' => csrf_token(),
        ]);

        $this->expectException(ValidationException::class);
        $this->controller->purchase($request, 'invalid-uuid');
    }

    /**
     * Test purchase validation with non-existent user.
     */
    public function test_purchase_with_nonexistent_user(): void
    {
        $request = Request::create('/test', 'POST', [
            'plan_id' => 1,
            'payment_method' => 'manual',
            '_token' => csrf_token(),
        ]);

        $uuid = '12345678-1234-1234-1234-123456789012';
        
        $this->expectException(ValidationException::class);
        $this->controller->purchase($request, $uuid);
    }

    /**
     * Test purchase validation with inactive user.
     */
    public function test_purchase_with_inactive_user(): void
    {
        $user = User::factory()->create(['is_active' => false]);

        $request = Request::create('/test', 'POST', [
            'plan_id' => 1,
            'payment_method' => 'manual',
            '_token' => csrf_token(),
        ]);

        $this->expectException(ValidationException::class);
        $this->controller->purchase($request, $user->id);
    }

    /**
     * Test purchase with invalid plan ID.
     */
    public function test_purchase_with_invalid_plan(): void
    {
        $user = User::factory()->create(['is_active' => true]);
        $paymentMethod = PaymentMethod::factory()->create([
            'is_active' => true,
            'code' => 'manual',
        ]);

        $request = Request::create('/test', 'POST', [
            'plan_id' => 99999, // Non-existent plan
            'payment_method' => $paymentMethod->code,
            '_token' => csrf_token(),
        ]);

        $this->expectException(ValidationException::class);
        $this->controller->purchase($request, $user->id);
    }

    /**
     * Test purchase with inactive plan.
     */
    public function test_purchase_with_inactive_plan(): void
    {
        $user = User::factory()->create(['is_active' => true]);
        $plan = SubscriptionPlan::factory()->create([
            'is_public' => false, // Not public
            'is_active' => true,
            'is_archived' => false,
        ]);
        $paymentMethod = PaymentMethod::factory()->create([
            'is_active' => true,
            'code' => 'manual',
        ]);

        $request = Request::create('/test', 'POST', [
            'plan_id' => $plan->id,
            'payment_method' => $paymentMethod->code,
            '_token' => csrf_token(),
        ]);

        $this->expectException(ValidationException::class);
        $this->controller->purchase($request, $user->id);
    }

    /**
     * Test renewal of current plan.
     */
    public function test_renewal_of_current_plan(): void
    {
        // Create user with current subscription
        $user = User::factory()->create(['is_active' => true]);
        $plan = SubscriptionPlan::factory()->create([
            'is_public' => true,
            'is_active' => true,
            'is_archived' => false,
            'price' => 1500,
        ]);
        
        // Create current subscription
        Subscription::factory()->create([
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'status' => 'active',
            'start_date' => now()->subDays(10),
            'end_date' => now()->addDays(20),
        ]);

        $paymentMethod = PaymentMethod::factory()->create([
            'is_active' => true,
            'code' => 'manual',
        ]);

        // Mock successful payment response
        $paymentResponse = new PaymentResponse(
            success: true,
            paymentId: 'test-payment-id',
            paymentUrl: 'https://payment.example.com/pay',
            message: 'Payment initiated successfully'
        );

        $this->paymentService
            ->shouldReceive('createPayment')
            ->once()
            ->andReturn($paymentResponse);

        $request = Request::create('/test', 'POST', [
            'plan_id' => $plan->id,
            'payment_method' => $paymentMethod->code,
            '_token' => csrf_token(),
        ]);

        $response = $this->controller->purchase($request, $user->id);

        // Verify order was created with renewal description
        $order = Order::where('user_id', $user->id)->first();
        $this->assertStringContains('Renewal:', $order->notes);
    }

    /**
     * Test plan change (upgrade/downgrade).
     */
    public function test_plan_change(): void
    {
        // Create user with current subscription
        $user = User::factory()->create(['is_active' => true]);
        $currentPlan = SubscriptionPlan::factory()->create([
            'is_public' => true,
            'is_active' => true,
            'price' => 1000,
            'name' => 'Basic Plan',
        ]);
        $newPlan = SubscriptionPlan::factory()->create([
            'is_public' => true,
            'is_active' => true,
            'price' => 2000,
            'name' => 'Premium Plan',
        ]);
        
        // Create current subscription
        Subscription::factory()->create([
            'user_id' => $user->id,
            'plan_id' => $currentPlan->id,
            'status' => 'active',
            'start_date' => now()->subDays(10),
            'end_date' => now()->addDays(20),
        ]);

        $paymentMethod = PaymentMethod::factory()->create([
            'is_active' => true,
            'code' => 'manual',
        ]);

        // Mock successful payment response
        $paymentResponse = new PaymentResponse(
            success: true,
            paymentId: 'test-payment-id',
            paymentUrl: 'https://payment.example.com/pay',
            message: 'Payment initiated successfully'
        );

        $this->paymentService
            ->shouldReceive('createPayment')
            ->once()
            ->andReturn($paymentResponse);

        $request = Request::create('/test', 'POST', [
            'plan_id' => $newPlan->id,
            'payment_method' => $paymentMethod->code,
            '_token' => csrf_token(),
        ]);

        $response = $this->controller->purchase($request, $user->id);

        // Verify order was created with plan change description
        $order = Order::where('user_id', $user->id)->first();
        $this->assertStringContains('Plan change from', $order->notes);
        $this->assertEquals(2000, $order->total_amount);
    }

    /**
     * Test JSON response for AJAX requests.
     */
    public function test_json_response_for_ajax_request(): void
    {
        $user = User::factory()->create(['is_active' => true]);
        $plan = SubscriptionPlan::factory()->create([
            'is_public' => true,
            'is_active' => true,
            'price' => 1000,
        ]);
        $paymentMethod = PaymentMethod::factory()->create([
            'is_active' => true,
            'code' => 'manual',
        ]);

        // Mock successful payment response
        $paymentResponse = new PaymentResponse(
            success: true,
            paymentId: 'test-payment-id',
            paymentUrl: 'https://payment.example.com/pay',
            message: 'Payment initiated successfully'
        );

        $this->paymentService
            ->shouldReceive('createPayment')
            ->once()
            ->andReturn($paymentResponse);

        // Create AJAX request
        $request = Request::create('/test', 'POST', [
            'plan_id' => $plan->id,
            'payment_method' => $paymentMethod->code,
            '_token' => csrf_token(),
        ]);
        $request->headers->set('Accept', 'application/json');

        $response = $this->controller->purchase($request, $user->id);

        $this->assertInstanceOf(\Illuminate\Http\JsonResponse::class, $response);
        $responseData = $response->getData(true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals('https://payment.example.com/pay', $responseData['payment_url']);
        $this->assertEquals('test-payment-id', $responseData['payment_id']);
    }
}
